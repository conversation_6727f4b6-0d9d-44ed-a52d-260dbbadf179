package com.cabcharge.card.migration.job;

import com.cabcharge.card.migration.cgw.entities.CardToken;
import com.cabcharge.card.migration.cgw.entities.TokenizationTransaction;
import com.cabcharge.card.migration.dcp.entities.PytbAccountDTO;
import com.cabcharge.card.migration.dcp.entities.PytbAccountGroupByTokenIDDTO;
import com.cabcharge.card.migration.dcp.repositories.PytbAccountRepository;
import com.cabcharge.card.migration.ibs.IbsCardInfo;
import com.cabcharge.card.migration.ibs.entities.PmtbProductV2Dto;
import com.cabcharge.card.migration.ibs.repository.PmtbProductRepository;
import com.cabcharge.card.migration.ptda.entities.PTDAPytbWDTxnDTO;
import com.cabcharge.card.migration.ptda.repositories.ptdaWDTXNRepository;
import jakarta.persistence.Column;
import jakarta.persistence.EntityManagerFactory;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class CardMigrationJob {

  @Autowired
  @Qualifier("dcpEntityManagerFactory")
  private EntityManagerFactory entityManagerFactory;

  //
  //    @Autowired
  //    @Qualifier("ibsJdbcTemplate")
  //    NamedParameterJdbcTemplate ibsJdbcTemplate;
  //
  //    @Autowired
  //    @Qualifier("dcpJdbcTemplate")
  //    NamedParameterJdbcTemplate dcpJdbcTemplate;

  @Autowired private Environment environment;

  @Autowired PytbAccountRepository pytbAccountRepository;

  @Autowired private PmtbProductRepository pmtbProductRepository;

  @Autowired private ptdaWDTXNRepository ptdaWDTXNRepository;
  public static final DateTimeFormatter cardDateformatter =
      DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

  // read config cdg.ccp.redirecturl from application.yaml
  @Value("${cdg.ccp.redirecturl}")
  private String redirectUrl;

  @Value("${cdg.ccp.cancelredirecturl}")
  public String cancelRedirectUrl;

  @Value("${cdg.ccp.failredirecturl}")
  public String failedRedirectUrl;

  @Value("${cdg.ccp.processingredirecturl}")
  public String processingRedirectUrl;

  @Value("${cdg.ccp.successredirecturl}")
  public String successRedirectUrl;

  public void run() throws Exception {
    log.info("Running migration job");
    log.info("Create list from dcp.pytb_account");

    List<IbsCardInfo> migrateCardInfo = new ArrayList<>();
    int i=0;

    log.info("Create list from PytbAccountDTO");
    List<PytbAccountDTO> accountsByToken = pytbAccountRepository.findAllAccounts();
    log.info("list PytbAccountDTO size: {}", accountsByToken.size());

    // Use Set to remove duplicate
    Set<PytbAccountGroupByTokenIDDTO> groupedByTokenId =
        new HashSet<PytbAccountGroupByTokenIDDTO>();
    for (PytbAccountDTO account : accountsByToken) {
      groupedByTokenId.add(
          new PytbAccountGroupByTokenIDDTO(
              account.getMaskedAccountNo(),
              account.getCardRegNo(),
              account.getFirst6Cardno(),
              account.getLast4Cardno(),
              account.getTokenId(),
              account.getExpirationMonth(),
              account.getExpirationYear(),
              account.getCardTypeId(),
              account.getTokenPart1(),
              account.getTokenPart2(),
              account.getCreatedDt(),
              account.getUpdatedDt()));
    }

    log.info("list PytbAccountGroupByTokenIDDTO size: {}", groupedByTokenId.size());

    // remove duplicate tokenId

    Set<PytbAccountGroupByTokenIDDTO> groupedByTokenIdUnique =
        groupedByTokenId.stream()
            .collect(
                Collectors.toMap(
                    PytbAccountGroupByTokenIDDTO::getTokenId,
                    Function.identity(),
                    (existing, replacement) ->
                        existing.getCreatedAt().isBefore(replacement.getCreatedAt())
                            ? replacement
                            : existing))
            .values()
            .stream()
            .collect(Collectors.toSet());
    log.info("list groupedByTokenIdUnique size: {}", groupedByTokenIdUnique.size());

    log.info("Create list from ibs.PmtbProductV2Dto");
    List<PmtbProductV2Dto> allV1Product = pmtbProductRepository.findAllV1Product();
    log.info("list PmtbProductV2Dto size: {}", allV1Product.size());
    log.info("Check list in PytbAccountDTO");
    try {

      groupedByTokenIdUnique.forEach(
          account -> {
            IbsCardInfo ibsCardInfo = IbsCardInfo.fromAccountToken(account);

            List<PmtbProductV2Dto> pmtbProducts = findIbsCard(ibsCardInfo, allV1Product);
            if (CollectionUtils.isEmpty(pmtbProducts)) {

              if (ibsCardInfo.getExpirationYear().equals("2025")) {

                // copy object ibsCardInfo into ibsCardInfo2025
                IbsCardInfo ibsCardInfo2025 =
                    ibsCardInfo.toBuilder().expirationYear("2030").build();

                //
                //                BeanUtils.copyProperties(ibsCardInfo2025, ibsCardInfo);
                //                ibsCardInfo2025.setExpirationYear("2030");
//                log.error("try again to find 2025 card {}", ibsCardInfo2025);
                pmtbProducts = findIbsCard(ibsCardInfo2025, allV1Product);
              }
            }

            if (CollectionUtils.isEmpty(pmtbProducts)) {
              log.error("Could not find IBS card for {}", ibsCardInfo);
            } else if (pmtbProducts.size() > 1) {
              List<PytbAccountDTO> duplicateAccounts =
                  accountsByToken.stream()
                      .filter(k -> k.getTokenId().equals(account.getTokenId()))
                      .collect(Collectors.toList());
              PmtbProductV2Dto pmtbProductV2Dto =
                  matchNameorMobile(duplicateAccounts, pmtbProducts);

              if (pmtbProductV2Dto == null) {
                pmtbProductV2Dto = matchPTDAWDTXNMobile(duplicateAccounts, pmtbProducts);
              }
              if (pmtbProductV2Dto == null) {
                log.error("Multiple cards found and fail match for {}", ibsCardInfo);
              } else {
                log.error("check me: {} {}",  duplicateAccounts.get(0).getTokenId(), pmtbProductV2Dto.getCardNo());

                ibsCardInfo.setCardNo(pmtbProductV2Dto.getCardNo());
                ibsCardInfo.setExpirationDate(pmtbProductV2Dto.getExpiryDate());
                ibsCardInfo.setCardHolderName(pmtbProductV2Dto.getNameOnProduct());
                ibsCardInfo.setCardHolderMobile(pmtbProductV2Dto.getCardHolderMobile());

                if (pmtbProductV2Dto.getUpdatedAt() != null) {
                  ibsCardInfo.setUpdatedAt(pmtbProductV2Dto.getUpdatedAt());
                }

                migrateCardInfo.add(ibsCardInfo);
              }

            } else {
              ibsCardInfo.setCardNo(pmtbProducts.get(0).getCardNo());
              ibsCardInfo.setExpirationDate(pmtbProducts.get(0).getExpiryDate());
              ibsCardInfo.setCardHolderName(
                  pmtbProducts.get(0).getNameOnProduct().replaceAll("[\\t]", " "));
              ibsCardInfo.setCardHolderMobile(pmtbProducts.get(0).getCardHolderMobile());

              if (pmtbProducts.get(0).getUpdatedAt() != null) {
                ibsCardInfo.setUpdatedAt(pmtbProducts.get(0).getUpdatedAt());
              }

              migrateCardInfo.add(ibsCardInfo);
            }
          });

      generateCardMigrationFileCSV(migrateCardInfo);

    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  private void generateCardMigrationFileCSV(List<IbsCardInfo> migrateCardInfo) {
    log.info("Migrating {} cards from IBS", migrateCardInfo.size());
    LocalDateTime now = LocalDateTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    String timestamp = now.format(formatter);
    String tokenizationFileName =
        String.format(
            "%s_tokenizationTransaction_%s.csv", environment.getActiveProfiles()[0], timestamp);
    String cardTokenFileName =
        String.format("%s_cardToken_%s.csv", environment.getActiveProfiles()[0], timestamp);

    LocalDateTime currentDateTime = LocalDateTime.now();

    List<String> tokenizationTransactionKeys = csvDataKeys(TokenizationTransaction.class);
    List<String> cardTokenKeys = csvDataKeys(CardToken.class);

    System.out.println(tokenizationTransactionKeys);
    System.out.println(cardTokenKeys);

    try (FileWriter tokenizationTransactionWriter = new FileWriter(tokenizationFileName);
        FileWriter cardTokenWriter = new FileWriter(cardTokenFileName);
        CSVPrinter csvWriterTokenizationPrinter =
            new CSVPrinter(
                tokenizationTransactionWriter,
                CSVFormat.DEFAULT
                    .builder()
                    .setHeader(tokenizationTransactionKeys.toArray(new String[0]))
                    .build());
        CSVPrinter csvCardTokenPrinter =
            new CSVPrinter(
                cardTokenWriter,
                CSVFormat.DEFAULT
                    .builder()
                    .setHeader(cardTokenKeys.toArray(new String[0]))
                    .build()); ) {

      for (IbsCardInfo ibsCardInfo : migrateCardInfo) {

        TokenizationTransaction tokenizationTransaction = new TokenizationTransaction();

        tokenizationTransaction.setId(UUID.randomUUID().toString());
        tokenizationTransaction.setAccountNumber(ibsCardInfo.getMaskedAccountNo());
        tokenizationTransaction.setAttemptThreed("true");
        tokenizationTransaction.setCancelRedirectUrl(cancelRedirectUrl);
        tokenizationTransaction.setCardHolderName(ibsCardInfo.getCardHolderName());
        tokenizationTransaction.setCardHolderMobile(ibsCardInfo.getCardHolderMobile());
        tokenizationTransaction.setCardNumber(ibsCardInfo.getCardNo());
        tokenizationTransaction.setCardType(ibsCardInfo.getCardTypeId());
        tokenizationTransaction.setExpirationMonth(
            String.format("%02d", Integer.parseInt(ibsCardInfo.getExpirationMonth())));
        tokenizationTransaction.setExpirationYear(ibsCardInfo.getExpirationYear());
        tokenizationTransaction.setExpiryDate(ibsCardInfo.getExpirationDate());
        tokenizationTransaction.setFailRedirectUrl(failedRedirectUrl);
        tokenizationTransaction.setFieldName1("udid");
        tokenizationTransaction.setFieldName2("customer-id");
        tokenizationTransaction.setFieldValue1("88887dbd-715f-472c-8805-0833757799bb");
        tokenizationTransaction.setFieldValue2("00000");
        tokenizationTransaction.setIpAddress("127.0.0.1");
        tokenizationTransaction.setLastName("");
        tokenizationTransaction.setLocale("en");
        tokenizationTransaction.setMerchantAccountId("8426abfb-bba6-46c5-b5ac-383d6b00f558");
        tokenizationTransaction.setNotificationTransactionState("");
        tokenizationTransaction.setNotificationUrl("");
        tokenizationTransaction.setOrderDetail("1 widget");
        tokenizationTransaction.setOrderNumber("123456");
        tokenizationTransaction.setPaReq("********-1189-4473-9783-4bda3a2c3f5a");
        tokenizationTransaction.setPaymentMethod("creditcard");
        tokenizationTransaction.setProcessingRedirectUrl(processingRedirectUrl);
        tokenizationTransaction.setPspName("cabcharge");
        tokenizationTransaction.setRedirectUrl(redirectUrl);
        tokenizationTransaction.setRequestId(ibsCardInfo.getCardRegNo());
        tokenizationTransaction.setRequestSignature(
            "52b2187259221fb2b8553631d71588faa06e69ae5e29a679cbd06aa545d79f13");
        tokenizationTransaction.setRequestTimestamp("**************");
        tokenizationTransaction.setRequestedAmount("0");
        tokenizationTransaction.setRequestedAmountCurrency("SGD");
        tokenizationTransaction.setResponseCode("00");
        tokenizationTransaction.setResponseMessage("OK");
        tokenizationTransaction.setSuccessRedirectUrl(successRedirectUrl);
        tokenizationTransaction.setTransactionDateTime(currentDateTime);
        tokenizationTransaction.setTransactionType("authorization-only");
        tokenizationTransaction.setCreatedAt(ibsCardInfo.getCreatedAt());
        tokenizationTransaction.setUpdatedAt(ibsCardInfo.getUpdatedAt());

        List<String> tokenizationTransactionValues =
            csvDataValues(tokenizationTransaction, tokenizationTransactionKeys);

        csvWriterTokenizationPrinter.printRecord(tokenizationTransactionValues);

        CardToken cardToken = new CardToken();
        cardToken.setId(UUID.randomUUID().toString());
        cardToken.setCardTokenId(ibsCardInfo.getTokenPart1() + ibsCardInfo.getTokenPart2());
        cardToken.setTokenStatus("ACTIVE");
        cardToken.setCreatedAt(ibsCardInfo.getCreatedAt());
        cardToken.setUpdatedAt(currentDateTime);
        cardToken.setTransactionId(tokenizationTransaction.getId());

        csvCardTokenPrinter.printRecord(csvDataValues(cardToken, cardTokenKeys));
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /*
   * Get all the values from the object and return as list
   */
  public List<String> csvDataValues(Object obj, List<String> tokenizationTransactionKeys) {

    return tokenizationTransactionKeys.stream()
        .map(
            key -> {
              try {
                for (Method method : obj.getClass().getDeclaredMethods()) {
                  Column annotationColumn = method.getAnnotation(Column.class);
                  if (annotationColumn != null && annotationColumn.name().equalsIgnoreCase(key)) {
                    String invokeValue =
                        method.invoke(obj) == null ? "" : method.invoke(obj).toString();

                    Object invokeMethodObj = method.invoke(obj);
                    if (invokeMethodObj == null) {
                      return "";
                    }

                    // Compare to LocalDateTime
                    if (invokeMethodObj instanceof LocalDateTime) {
                      return ((LocalDateTime) invokeMethodObj).format(cardDateformatter);
                    } else {
                      return invokeMethodObj.toString();
                    }
                  }
                }
              } catch (Exception e) {
                throw new RuntimeException(e);
              }
              return null;
            })
        .collect(Collectors.toList());
  }

  public List<String> csvDataKeys(Class clazz) {
    List<String> tableHeaders = new ArrayList<>();
    // sort clazz.getDeclaredMethods()
    Method[] clonedMethods = clazz.getDeclaredMethods().clone();
    Arrays.sort(clonedMethods, Comparator.comparing(Method::getName));

    for (Method method : clonedMethods) {
      Column annotationColumn = method.getAnnotation(Column.class);
      if (annotationColumn != null) {
        String column_name = annotationColumn.name();
        tableHeaders.add(column_name);
      }
    }
    return tableHeaders;
  }

  public Map<String, String> csvData(Object obj) {
    Map<String, String> objectKeyValues = new HashMap<>();
    for (Method method : obj.getClass().getDeclaredMethods()) {
      Column annotationColumn = method.getAnnotation(Column.class);
      if (annotationColumn != null) {
        String column_name = annotationColumn.name();
        try {
          Object invokeMethod = method.invoke(obj);
          String invokeValue = invokeMethod != null ? invokeMethod.toString() : "";
          objectKeyValues.put(column_name, invokeValue);
        } catch (Exception e) {
          log.error("Error in getting field value for {}", e.getMessage());
        }
      }
    }
    return objectKeyValues;
  }

  // Get Tokenization Transaction fiels annotation name
  public String getFieldAnnotationName(String methodName) {
    Field field = null;

    Method declaredMethod = null;
    try {
      declaredMethod = TokenizationTransaction.class.getDeclaredMethod(methodName);
    } catch (NoSuchMethodException e) {
      throw new RuntimeException(e);
    }
    if (declaredMethod != null) {
      Column annotationColumn = declaredMethod.getAnnotation(Column.class);
      if (annotationColumn != null) {
        return annotationColumn.name();
      }
    }
    return "";
  }

  private static void generateCardMigrationFile(List<IbsCardInfo> migrateCardInfo) {
    log.info("Migrating {} cards from IBS", migrateCardInfo.size());
    LocalDateTime now = LocalDateTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    String timestamp = now.format(formatter);
    String fileName = "output_" + timestamp + ".txt";
    try (FileWriter writer = new FileWriter(fileName)) {
      // Write the content to the file
      for (IbsCardInfo ibsCardInfo : migrateCardInfo) {
        String transactionId = UUID.randomUUID().toString();
        writer.write("INSERT INTO tokenization_transaction ");
        writer.write(
            "(id, account_number, attempt_threed, cancel_redirect_url, card_holder_name, card_number, card_type, expiration_month, expiration_year, expiry_date, fail_redirect_url, field_name1, field_name2, field_value1, field_value2, ip_address, last_name, locale, merchant_account_id, notification_transaction_state, notification_url, order_detail, order_number, pa_req, payment_method, processing_redirect_url, psp_name, redirect_url, request_id, request_signature, request_timestamp, requested_amount, requested_amount_currency, response_code, response_message, success_redirect_url, transaction_date_time, transaction_type, created_at, updated_at) ");
        writer.write(
            "VALUES ('"
                + transactionId
                + "', NULL, 'true', 'https://interapps-uat.cdgtaxi.com.sg/dcp-payment/rest/v1/complete?state=cancel', 'migration', '"
                + ibsCardInfo.getCardNo()
                + "', NULL, NULL, NULL, '"
                + ibsCardInfo.getExpirationYear()
                + String.format("%02d", Integer.parseInt(ibsCardInfo.getExpirationMonth()))
                + "', 'https://interapps-uat.cdgtaxi.com.sg/dcp-payment/rest/v1/complete?state=fail', 'udid', 'customer-id', '88887dbd-715f-472c-8805-0833757799bb', '00000', '127.0.0.1', NULL, 'en', 'b5e63c80-a2bd-4a8c-9d11-be011aed6491', '', '', '1 widget', '123456', '********-1189-4473-9783-4bda3a2c3f5a', 'creditcard', 'https://interapps-uat.cdgtaxi.com.sg/dcp-payment/rest/v1/complete?state=processing', 'cabcharge', 'https://interapps-uat.cdgtaxi.com.sg/dcp-payment/rest/v1/complete?state=success', '8888df3a-60e3-4f63-9cc5-2ae3f97de34f', '52b2187259221fb2b8553631d71588faa06e69ae5e29a679cbd06aa545d79f13', '**************', '0', 'SGD', '00', 'OK', 'https://interapps-uat.cdgtaxi.com.sg/dcp-payment/rest/v1/complete?state=success', '2024-01-01 00:00:00.000', 'authorization-only', '2024-01-01 00:00:00.000', '2024-01-01 00:00:00.000'); ");
        writer.write("\n");

        String tokenId = UUID.randomUUID().toString();
        writer.write("INSERT INTO card_token ");
        writer.write("(id, created_at, token_status, transaction_id, updated_at, card_token_id) ");
        writer.write(
            "VALUES ('"
                + tokenId
                + "', '2024-01-01 00:00:00.000', 'ACTIVE', '"
                + transactionId
                + "', '2024-01-01 00:00:00.000', '"
                + ibsCardInfo.getTokenPart1()
                + ibsCardInfo.getTokenPart2()
                + "'); ");
        writer.write("\n");
      }
      log.info("Data has been written to " + fileName);
    } catch (IOException e) {
      // Handle any IO exceptions that might occur
      log.error("An IOException occurred: " + e.getMessage());
    }
  }

  private PmtbProductV2Dto matchPTDAWDTXNMobile(
      List<PytbAccountDTO> duplicateAccounts, List<PmtbProductV2Dto> pmtbProducts) {
    List<String> accountIDs =
        duplicateAccounts.stream().map(PytbAccountDTO::getAccountId).collect(Collectors.toList());

    // Get from ptda wd transaction
    List<PTDAPytbWDTxnDTO> ptdaMobileNumbers =
        ptdaWDTXNRepository.findWDTXNMobileNumbers(accountIDs);

    for (PTDAPytbWDTxnDTO ptdatxn : ptdaMobileNumbers) {

      for (PmtbProductV2Dto pmtbProduct : pmtbProducts) {
        if (ptdatxn.getMobile().equals(pmtbProduct.getCardHolderMobile())) {
          log.info(" -> match {}", ptdaMobileNumbers);
          return pmtbProduct;
        }
      }
    }

    duplicateAccounts.forEach(account -> log.info(" -> fail  match {}", account));
    return null;
  }

  private PmtbProductV2Dto matchNameorMobile(
      List<PytbAccountDTO> duplicateAccounts, List<PmtbProductV2Dto> pmtbProducts) {
    // go through all duplicate accounts, try to find the one with the same last name or mobile
    // number in pmtbProducts
    for (PytbAccountDTO account : duplicateAccounts) {
      for (PmtbProductV2Dto pmtbProduct : pmtbProducts) {
        if (account
                .getLastName()
                .toLowerCase()
                .contains(pmtbProduct.getNameOnProduct().toLowerCase())
            || account.getMobile().equals(pmtbProduct.getCardHolderMobile())) {
          //                    log.info(" -> match {}", account);
          return pmtbProduct;
        } else if (partailMatchName(account.getLastName(), pmtbProduct.getNameOnProduct())
            == true) {
          return pmtbProduct;
        }
      }
    }

    duplicateAccounts.forEach(account -> log.info(" -> fail dcp match {}", account));
    pmtbProducts.forEach(pmtbProduct -> log.info(" -> fail ibs match{}", pmtbProduct));
    return null;
  }

  /**
   * check if last name contains name on product
   *
   * @param lastName
   * @param nameOnProduct
   * @return
   */
  public boolean partailMatchName(String lastName, String nameOnProduct) {
    // toUpperCase
    lastName = lastName.toUpperCase();
    nameOnProduct = nameOnProduct.toUpperCase();

    // remove "MOHAMMED" in Name
    nameOnProduct = nameOnProduct.replace("MOHAMMED", "");
    lastName = lastName.replace("MOHAMMED", "");

    nameOnProduct = nameOnProduct.replace("MOHAMAD", "");
    lastName = lastName.replace("MOHAMAD", "");

    // remove "MUHAMMAD" in Name
    nameOnProduct = nameOnProduct.replace("MUHAMMAD", "");
    lastName = lastName.replace("MUHAMMAD", "");

    // remove "BIN" in Name
    nameOnProduct = nameOnProduct.replace("BIN", "");
    lastName = lastName.replace("BIN", "");

    String[] lastNameWords = lastName.trim().split("\\s+");
    String[] nameOnProductWords = nameOnProduct.trim().split("\\s+");

    int matchCnt = 0;
    for (String lastNameWord : lastNameWords) {
      for (String nameOnProductWord : nameOnProductWords) {
        if (nameOnProductWord.toLowerCase().equals(lastNameWord.toLowerCase())) {
          matchCnt++;
          break;
        }
      }
    }

    // at less two words in name need match or one match when single word
    if (matchCnt >= 2) {
      return true;
    } else
      if (matchCnt == 1 && lastNameWords.length == 1) {
        return true;
      } else {
        return false;
      }
  }

  private List<PmtbProductV2Dto> findIbsCard(
      IbsCardInfo ibsCardInfo, List<PmtbProductV2Dto> allV1Product) {
    String first6CardNo = ibsCardInfo.getFirst6CardNo();
    String last4CardNo = ibsCardInfo.getLast4CardNo();
    String expirationMonth = ibsCardInfo.getExpirationMonth();
    String expirationYear = ibsCardInfo.getExpirationYear();

    List<PmtbProductV2Dto> v2Dtos =
        allV1Product.parallelStream()
            .filter(
                product ->
                    product.getCardNo().startsWith(first6CardNo)
                        && product.getCardNo().endsWith(last4CardNo))
            .filter(product -> product.getExpirationYear() == Integer.parseInt(expirationYear))
            .filter(product -> product.getExpirationMonth() == Integer.parseInt(expirationMonth))
            .collect(Collectors.toList());

    return v2Dtos;
  }
}
