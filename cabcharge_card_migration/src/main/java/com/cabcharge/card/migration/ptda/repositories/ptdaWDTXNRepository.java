package com.cabcharge.card.migration.ptda.repositories;

import com.cabcharge.card.migration.ptda.entities.PTDAPytbWDTxnDTO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ptdaWDTXNRepository extends JpaRepository<PTDAPytbWDTxnDTO, Integer> {

  @Query(nativeQuery = true, name = "findWDTXNMobileNumbers")
  List<PTDAPytbWDTxnDTO> findWDTXNMobileNumbers(@Param("accountIds") List<String> accountIDs);
}
