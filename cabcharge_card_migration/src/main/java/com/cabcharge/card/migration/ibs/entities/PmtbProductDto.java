package com.cabcharge.card.migration.ibs.entities;

import jakarta.persistence.*;
import java.io.Serializable;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * DTO for {@link PmtbProduct} query = "select product_no, card_no from ibssys.pmtb_product\n" +
 * "where\n" + "product_type_id='V1'\n" + "and expiry_date>to_date('20240101 00:00:00','yyyymmdd
 * hh24:mi:ss')\n" + "and card_no like :cardNoPattern\n" + "and extract( MONTH from expiry_date) =
 * :expirationMonth\n" + "and to_char(expiry_date, 'YYYY') = :expirationYear",
 */
@Entity
@Data
@Getter
@Setter
@NoArgsConstructor
@Slf4j
@NamedNativeQuery(
    name = "findByProductTypeId",
    query =
        "select product_no, card_no from ibssys.pmtb_product\n"
            + "where\n"
            + "product_type_id='V1'\n"
            + "and expiry_date>to_date('20240101 00:00:00','yyyymmdd hh24:mi:ss')\n"
            + "and card_no like :cardNoPattern\n"
            + "and extract( MONTH from expiry_date) = :expirationMonth\n"
            + "and to_char(expiry_date, 'YYYY') = :expirationYear",
    resultSetMapping = "PmtbProductDtoMapping")
@SqlResultSetMapping(
    name = "PmtbProductDtoMapping",
    classes =
        @ConstructorResult(
            targetClass = PmtbProductDto.class,
            columns = {
              @ColumnResult(name = "product_no", type = String.class),
              @ColumnResult(name = "card_no", type = String.class)
            }))
public class PmtbProductDto implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "product_no", nullable = false, length = 36)
  String productNo;

  @Column(name = "card_no", length = 36)
  String cardNo;

  // Constructors
  public PmtbProductDto(String productNo, String cardNo) {
    this.productNo = productNo;
    this.cardNo = cardNo;
  }

  // toString
  @Override
  public String toString() {
    return "PmtbProductDto{" + "productNo=" + productNo + ", cardNo='" + cardNo + '\'' + '}';
  }
}
