package com.cabcharge.card.migration.ibs;

import com.cabcharge.card.migration.dcp.entities.PytbAccountGroupByTokenIDDTO;

import java.time.LocalDateTime;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

@Data
@Builder(toBuilder = true)
@ToString
public class IbsCardInfo {

    private String maskedAccountNo;
    private String first6CardNo;
    private String last4CardNo;
    private String expirationMonth;
    private String expirationYear;
    private String expirationDate;
    private String cardTypeId;
    private String tokenPart1;
    private String tokenPart2;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    private String cardHolderName;
    private String cardHolderMobile;
    private String cardRegNo;

    // CardNo found on IBS DB
    private String cardNo;

    public static IbsCardInfo fromDataLine(String line) {
        String[] items = line.split("\t");
        String maskedAccountNo = items[0];
        String first6CardNo = items[1];
        String last4CardNo = items[2];
        String expirationMonth = items[3];
        String expirationYear = items[4];
        String cardTypeId = items[5];
        String tokenPart1 = items[6].replaceAll("'", "");
        String tokenPart2 = items[7].replaceAll("'", "");
        return IbsCardInfo.builder()
                .maskedAccountNo(maskedAccountNo)
                .first6CardNo(first6CardNo)
                .last4CardNo(last4CardNo)
                .expirationMonth(expirationMonth)
                .expirationYear(expirationYear)
                .cardTypeId(cardTypeId)
                .tokenPart1(tokenPart1)
                .tokenPart2(tokenPart2)
                .build();
    }

    public static IbsCardInfo fromAccountToken(PytbAccountGroupByTokenIDDTO account) {
        return IbsCardInfo.builder()
                .maskedAccountNo(account.getMaskedAccountNo())
                .first6CardNo(account.getFirst6Cardno().replace("\t", ""))
                .last4CardNo(account.getLast4Cardno().replace("\t", ""))
                .expirationMonth(String.valueOf(account.getExpirationMonth()))
                .expirationYear(String.valueOf(account.getExpirationYear()))
                .cardTypeId(account.getCardTypeId())
                .tokenPart1(account.getTokenPart1())
                .tokenPart2(account.getTokenPart2())
                .createdAt(account.getCreatedAt())
                .updatedAt(account.getUpdatedAt())
                .cardRegNo(account.getCardRegNo())
                .build();
    }


}
