package com.cabcharge.card.migration.dcp.entities;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "PYTB_ACCOUNT")
public class PytbAccount implements Serializable {
  private static final long serialVersionUID = -2291255427384294462L;

  private Integer id;

  private String cardRegNo;

  private String paymentMode;

  private String firstName;

  private String lastName;

  private String maskedAccountNo;

  private Short expirationMonth;

  private Short expirationYear;

  private String cardType;

  private String tokenId;

  private Boolean deleted = false;

  private String udid;

  private LocalDate createdDt;

  private LocalDate updatedDt;

  private String cardTypeId;

  private String adyenTokenId;

  private byte[] encrytedAccountNo;

  private byte[] encrytedExpiryMonth;

  private byte[] encrytedExpiryYear;

  private byte[] encrytedSecurityCode;

  private String fundingSource;

  private byte[] paymentData;

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ACCOUNT_ID", nullable = false)
  public Integer getId() {
    return id;
  }

  @Column(name = "CARD_REG_NO", nullable = false, length = 64)
  public String getCardRegNo() {
    return cardRegNo;
  }

  @Column(name = "PAYMENT_MODE", nullable = false, length = 16)
  public String getPaymentMode() {
    return paymentMode;
  }

  @Column(name = "FIRST_NAME", length = 32)
  public String getFirstName() {
    return firstName;
  }

  @Column(name = "LAST_NAME", length = 32)
  public String getLastName() {
    return lastName;
  }

  @Column(name = "MASKED_ACCOUNT_NO", nullable = false, length = 36)
  public String getMaskedAccountNo() {
    return maskedAccountNo;
  }

  @Column(name = "EXPIRATION_MONTH", nullable = false)
  public Short getExpirationMonth() {
    return expirationMonth;
  }

  @Column(name = "EXPIRATION_YEAR", nullable = false)
  public Short getExpirationYear() {
    return expirationYear;
  }

  @Column(name = "CARD_TYPE", length = 15)
  public String getCardType() {
    return cardType;
  }

  @Column(name = "TOKEN_ID", length = 100)
  public String getTokenId() {
    return tokenId;
  }

  @Column(name = "DELETED", nullable = false)
  public Boolean getDeleted() {
    return deleted;
  }

  @Column(name = "UDID", nullable = false, length = 128)
  public String getUdid() {
    return udid;
  }

  @Column(name = "CREATED_DT", nullable = false)
  public LocalDate getCreatedDt() {
    return createdDt;
  }

  @Column(name = "UPDATED_DT")
  public LocalDate getUpdatedDt() {
    return updatedDt;
  }

  @Column(name = "CARD_TYPE_ID", length = 8)
  public String getCardTypeId() {
    return cardTypeId;
  }

  @Column(name = "ADYEN_TOKEN_ID", length = 100)
  public String getAdyenTokenId() {
    return adyenTokenId;
  }

  @Column(name = "ENCRYTED_ACCOUNT_NO")
  public byte[] getEncrytedAccountNo() {
    return encrytedAccountNo;
  }

  @Column(name = "ENCRYTED_EXPIRY_MONTH")
  public byte[] getEncrytedExpiryMonth() {
    return encrytedExpiryMonth;
  }

  @Column(name = "ENCRYTED_EXPIRY_YEAR")
  public byte[] getEncrytedExpiryYear() {
    return encrytedExpiryYear;
  }

  @Column(name = "ENCRYTED_SECURITY_CODE")
  public byte[] getEncrytedSecurityCode() {
    return encrytedSecurityCode;
  }

  @Column(name = "FUNDING_SOURCE", length = 25)
  public String getFundingSource() {
    return fundingSource;
  }

  @Column(name = "PAYMENT_DATA")
  public byte[] getPaymentData() {
    return paymentData;
  }
}
