package com.cabcharge.card.migration.ibs.entities;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/** DTO for {@link PmtbProduct} */
@Entity
@Data
@Getter
@Setter
@ToString
@NoArgsConstructor
@Slf4j
@NamedNativeQuery(
    name = "findAllV1Product",
    query =
        "select product_no, card_no, expiry_date, name_on_product, card_holder_mobile, updated_dt from ibssys.pmtb_product \n"
            + "where \n"
            + "product_type_id='V1' \n"
            + "and expiry_date>to_date('20240101 00:00:00','yyyymmdd hh24:mi:ss')\n",
    resultSetMapping = "PmtbProductV2DtoMapping")
@SqlResultSetMapping(
    name = "PmtbProductV2DtoMapping",
    classes =
        @ConstructorResult(
            targetClass = PmtbProductV2Dto.class,
            columns = {
              @ColumnResult(name = "product_no", type = String.class),
              @ColumnResult(name = "card_no", type = String.class),
              @ColumnResult(name = "expiry_date", type = String.class),
              @ColumnResult(name = "name_on_product", type = String.class),
              @ColumnResult(name = "card_holder_mobile", type = String.class),
              @ColumnResult(name = "updated_dt", type = LocalDateTime.class)
            }))
public class PmtbProductV2Dto implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "product_no", nullable = false, length = 36)
  String productNo;

  @Column(name = "card_no", length = 36)
  String cardNo;

  @Column(name = "expiry_date", length = 36)
  String expiryDate;

  @Column(name = "name_on_product", length = 36)
  String nameOnProduct;

  @Column(name = "card_holder_mobile", length = 36)
  String cardHolderMobile;

  @Column(name = "updated_at")
  @Temporal(TemporalType.TIMESTAMP)
  private LocalDateTime updatedAt;

  // Constructors
  public PmtbProductV2Dto(
      String productNo,
      String cardNo,
      String expiryDate,
      String nameOnProduct,
      String cardHolderMobile,
      LocalDateTime updatedAt) {
    this.productNo = productNo;
    this.cardNo = cardNo;
    this.expiryDate = expiryDate;
    this.nameOnProduct = nameOnProduct;
    this.cardHolderMobile = cardHolderMobile;
    this.updatedAt = updatedAt;
  }

  public int getExpirationYear() {
    // ConvertexpiryDate to DateTime with format yyyy-MM-dd HH:mm:ss
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime localDateTime = LocalDateTime.parse(this.expiryDate, formatter);
    return localDateTime.getYear();
  }

  // expiryDate=2028-02-29 00:00:00.000
  // Get Month
  public int getExpirationMonth() {
    // ConvertexpiryDate to DateTime with format yyyy-MM-dd HH:mm:ss
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime localDateTime = LocalDateTime.parse(this.expiryDate, formatter);
    return localDateTime.getMonth().getValue();
  }

  public String getExpiryDate() {
    // ConvertexpiryDate to DateTime with format yyyy-MM-dd HH:mm:ss
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime localDateTime = LocalDateTime.parse(this.expiryDate, formatter);
    return String.format("%04d%02d", localDateTime.getYear(), localDateTime.getMonth().getValue());
  }
}
