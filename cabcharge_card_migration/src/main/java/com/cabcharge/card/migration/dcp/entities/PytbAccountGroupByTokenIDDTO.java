package com.cabcharge.card.migration.dcp.entities;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.*;

@Data
public class PytbAccountGroupByTokenIDDTO {

  private String maskedAccountNo;
  private String cardRegNo;
  private String first6Cardno;
  private String last4Cardno;

  private String tokenId;

  private int expirationMonth;

  private int expirationYear;
  private String cardTypeId;
  private String tokenPart1;

  private String tokenPart2;

  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;

  //    private String accountId;

  // Constructor
  //    public PytbAccountGroupByTokenIDDTO(String maskedAccountNo, String first6Cardno, String
  // last4Cardno, String tokenId, int expirationMonth, int expirationYear,String cardTypeId, String
  // tokenPart1, String tokenPart2, String accountId)
  //    {
  //        this.maskedAccountNo = maskedAccountNo;
  //        this.first6Cardno = first6Cardno;
  //        this.last4Cardno = last4Cardno;
  //        this.tokenId = tokenId;
  //        this.expirationMonth = expirationMonth;
  //        this.expirationYear = expirationYear;
  //        this.cardTypeId = cardTypeId;
  //        this.tokenPart1 = tokenPart1;
  //        this.tokenPart2 = tokenPart2;
  //        this.accountId = accountId;
  //
  //    }

  public PytbAccountGroupByTokenIDDTO(
      String maskedAccountNo,
      String cardRegNo,
      String first6Cardno,
      String last4Cardno,
      String tokenId,
      int expirationMonth,
      int expirationYear,
      String cardTypeId,
      String tokenPart1,
      String tokenPart2,
      LocalDateTime createdAt,
      LocalDateTime updatedAt) {
    this.maskedAccountNo = maskedAccountNo;
    this.cardRegNo = cardRegNo;
    this.first6Cardno = first6Cardno;
    this.last4Cardno = last4Cardno;
    this.tokenId = tokenId;
    this.expirationMonth = expirationMonth;
    this.expirationYear = expirationYear;
    this.cardTypeId = cardTypeId;
    this.tokenPart1 = tokenPart1;
    this.tokenPart2 = tokenPart2;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  public PytbAccountGroupByTokenIDDTO(String tokenId) {
    this.tokenId = tokenId;
  }
}
