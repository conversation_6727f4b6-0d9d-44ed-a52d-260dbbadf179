package com.cabcharge.card.migration.ptda.entities;

import jakarta.persistence.*;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Getter
@Setter
@Entity
@Slf4j
@Table(name = "PYTB_WD_TXN")
@NoArgsConstructor
@ToString
@NamedNativeQuery(
    name = "findWDTXNMobileNumbers",
    query =
        "select distinct pwt.account_id\n"
            +
            //                        ", pwt.booking_ref\n" +
            ", substr(pwt.booking_ref, 0, 8)  mobile\n"
            + "from dcp_sys.pytb_wd_txn pwt \n"
            + "where \n"
            + "pwt.account_id in (:accountIds)",
    resultSetMapping = "PytbWDTXNDTOMapping")
@SqlResultSetMapping(
    name = "PytbWDTXNDTOMapping",
    classes =
        @ConstructorResult(
            targetClass = PTDAPytbWDTxnDTO.class,
            columns = {
              @ColumnResult(name = "account_id", type = String.class),
              //                        @ColumnResult(name = "booking_ref", type = String.class),
              @ColumnResult(name = "mobile", type = String.class)
            }))
public class PTDAPytbWDTxnDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "account_id", nullable = false, length = 36)
  private String accountId;

  //
  //    @Column(name = "booking_ref", length = 100)
  //    private String bookingReference;

  @Column(name = "mobile", length = 16)
  private String mobile;

  public String getAccountId() {
    return accountId;
  }

  public void setAccountId(String accountId) {
    this.accountId = accountId;
  }

  //    public String getBookingReference() {
  //        return bookingReference;
  //    }
  //
  //    public void setBookingReference(String bookingReference) {
  //        this.bookingReference = bookingReference;
  //    }

  public String getMobile() {
    return mobile;
  }

  public void setMobile(String mobile) {
    this.mobile = mobile;
  }

  public PTDAPytbWDTxnDTO(String accountId, String mobile) {
    this.accountId = accountId;
    this.mobile = mobile;
  }
}
