package com.cabcharge.card.migration;

import com.cabcharge.card.migration.job.CardMigrationJob;
import lombok.AllArgsConstructor;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class StartupListener implements ApplicationListener<ApplicationReadyEvent> {

  private final CardMigrationJob cardMigrationJob;

  @Override
  public void onApplicationEvent(ApplicationReadyEvent event) {

    System.out.println(("***** onApplicationEvent triggered! *****"));

    try {
      cardMigrationJob.run();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }
}
