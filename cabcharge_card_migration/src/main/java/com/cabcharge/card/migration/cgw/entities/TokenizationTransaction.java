package com.cabcharge.card.migration.cgw.entities;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "tokenization_transaction", schema = "cabcharge_gateway_service")
public class TokenizationTransaction implements Serializable {
  private static final long serialVersionUID = 4964438236615014553L;
  private String id;

  private String requestedAmount;

  private String requestedAmountCurrency;

  private String locale;

  private String orderNumber;

  private String orderDetail;

  private String requestId;

  private String requestTimestamp;

  private String merchantAccountId;

  private String paymentMethod;

  private String transactionType;

  private String redirectUrl;

  private String ipAddress;

  private String requestSignature;

  private String pspName;

  private String attemptThreed;

  private String notificationUrl;

  private String notificationTransactionState;

  private String successRedirectUrl;

  private String failRedirectUrl;

  private String cancelRedirectUrl;

  private String processingRedirectUrl;

  private String fieldName1;

  private String fieldValue1;

  private String fieldName2;

  private String fieldValue2;

  private String accountNumber;

  private String cardType;

  private String expirationMonth;

  private String expirationYear;

  private String lastName;

  private String cardHolderName;

  private String cardHolderMobile;

  private String cardNumber;

  private String expiryDate;

  private String paReq;

  private LocalDateTime transactionDateTime;

  private String responseCode;

  private String responseMessage;

  private LocalDateTime createdAt;

  private LocalDateTime updatedAt;

  @Id
  @Column(name = "id", nullable = false)
  public String getId() {
    return id;
  }

  @Column(name = "requested_amount")
  public String getRequestedAmount() {
    return requestedAmount;
  }

  @Column(name = "requested_amount_currency")
  public String getRequestedAmountCurrency() {
    return requestedAmountCurrency;
  }

  @Column(name = "locale")
  public String getLocale() {
    return locale;
  }

  @Column(name = "order_number")
  public String getOrderNumber() {
    return orderNumber;
  }

  @Column(name = "order_detail")
  public String getOrderDetail() {
    return orderDetail;
  }

  @Column(name = "request_id")
  public String getRequestId() {
    return requestId;
  }

  @Column(name = "request_timestamp")
  public String getRequestTimestamp() {
    return requestTimestamp;
  }

  @Column(name = "merchant_account_id")
  public String getMerchantAccountId() {
    return merchantAccountId;
  }

  @Column(name = "payment_method")
  public String getPaymentMethod() {
    return paymentMethod;
  }

  @Column(name = "transaction_type")
  public String getTransactionType() {
    return transactionType;
  }

  @Column(name = "redirect_url")
  public String getRedirectUrl() {
    return redirectUrl;
  }

  @Column(name = "ip_address")
  public String getIpAddress() {
    return ipAddress;
  }

  @Column(name = "request_signature")
  public String getRequestSignature() {
    return requestSignature;
  }

  @Column(name = "psp_name")
  public String getPspName() {
    return pspName;
  }

  @Column(name = "attempt_threed")
  public String getAttemptThreed() {
    return attemptThreed;
  }

  @Column(name = "notification_url")
  public String getNotificationUrl() {
    return notificationUrl;
  }

  @Column(name = "notification_transaction_state")
  public String getNotificationTransactionState() {
    return notificationTransactionState;
  }

  @Column(name = "success_redirect_url")
  public String getSuccessRedirectUrl() {
    return successRedirectUrl;
  }

  @Column(name = "fail_redirect_url")
  public String getFailRedirectUrl() {
    return failRedirectUrl;
  }

  @Column(name = "cancel_redirect_url")
  public String getCancelRedirectUrl() {
    return cancelRedirectUrl;
  }

  @Column(name = "processing_redirect_url")
  public String getProcessingRedirectUrl() {
    return processingRedirectUrl;
  }

  @Column(name = "field_name1")
  public String getFieldName1() {
    return fieldName1;
  }

  @Column(name = "field_value1")
  public String getFieldValue1() {
    return fieldValue1;
  }

  @Column(name = "field_name2")
  public String getFieldName2() {
    return fieldName2;
  }

  @Column(name = "field_value2")
  public String getFieldValue2() {
    return fieldValue2;
  }

  @Column(name = "account_number")
  public String getAccountNumber() {
    return accountNumber;
  }

  @Column(name = "card_type")
  public String getCardType() {
    return cardType;
  }

  @Column(name = "expiration_month")
  public String getExpirationMonth() {
    return expirationMonth;
  }

  @Column(name = "expiration_year")
  public String getExpirationYear() {
    return expirationYear;
  }

  @Column(name = "last_name")
  public String getLastName() {
    return lastName;
  }

  @Column(name = "card_holder_name")
  public String getCardHolderName() {
    return cardHolderName;
  }

  @Column(name = "card_number")
  public String getCardNumber() {
    return cardNumber;
  }

  @Column(name = "expiry_date")
  public String getExpiryDate() {
    return expiryDate;
  }

  @Column(name = "pa_req")
  public String getPaReq() {
    return paReq;
  }

  @Column(name = "transaction_date_time")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
  public LocalDateTime getTransactionDateTime() {
    return transactionDateTime;
  }

  @Column(name = "response_code")
  public String getResponseCode() {
    return responseCode;
  }

  @Column(name = "response_message")
  public String getResponseMessage() {
    return responseMessage;
  }

  @Temporal(TemporalType.TIMESTAMP)
  @Column(name = "created_at")
  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  @Temporal(TemporalType.TIMESTAMP)
  @Column(name = "updated_at")
  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  @Column(name = "card_holder_mobile")
  public String getCardHolderMobile() {
    return cardHolderMobile;
  }
}
