package com.cabcharge.card.migration.ibs.repository;

import com.cabcharge.card.migration.ibs.entities.PmtbProductDto;
import com.cabcharge.card.migration.ibs.entities.PmtbProductV2Dto;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PmtbProductRepository extends JpaRepository<PmtbProductDto, Long> {

  @Query(nativeQuery = true, name = "findByProductTypeId")
  List<PmtbProductDto> findProductByProductTypeId(
      @Param("cardNoPattern") String cardNoPattern,
      @Param("expirationMonth") String expirationMonth,
      @Param("expirationYear") String expirationYear);

  @Query(nativeQuery = true, name = "findAllV1Product")
  List<PmtbProductV2Dto> findAllV1Product();
}
