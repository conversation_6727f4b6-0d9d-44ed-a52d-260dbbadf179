spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: oracle.jdbc.OracleDriver
    dcp:
      jdbc-url: ******************************= (address=(protocol=tcps)(port=1521)(host=***********))(connect_data=(service_name=gbe786bc9113b95_ptcnuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
      username: dcp_user
      password: ocicUat$210_st
    ibs:
      jdbc-url: ******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfsuat1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfsuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
      username: IBSAPPS
      password: ibs_Uatpps1611
    ptda:
      jdbc-url: ******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfsuat1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfsuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
      username: recuser
      password: ociUsr$rec2022
      driver-class-name: oracle.jdbc.driver.OracleDriver
