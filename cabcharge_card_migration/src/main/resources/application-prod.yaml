spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: oracle.jdbc.OracleDriver
    dcp:
      jdbc-url: ******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptccpro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptccpro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
      username: CDG_DENG_HUI
      password: Cdg0000278_00
#      username: dcp_user
#      password: ociPro$gDCP_255
    ibs:
      jdbc-url: ******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
      username: CDG_DENG_HUI
      password: Cdg0000278_ptfs01
    ptda:
      jdbc-url: ******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptdapro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptdapro_low.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
      username: CDG_DENG_HUI
      password: Cdg0000278_00
      driver-class-name: oracle.jdbc.driver.OracleDriver

cdg:
  ccp:
      redirecturl: https://api.zig.live/pmtdcp/payment/v1.0/cabcharge/register/complete?state=success
      cancelredirecturl: https://api.zig.live/pmtdcp/payment/v1.0/cabcharge/register/complete?state=cancel
      failredirecturl: https://api.zig.live/pmtdcp/payment/v1.0/cabcharge/register/complete?state=fail
      processingredirecturl: https://api.zig.live/pmtdcp/payment/v1.0/cabcharge/register/complete?state=processing
      successredirecturl: https://api.zig.live/pmtdcp/payment/v1.0/cabcharge/register/complete?state=success