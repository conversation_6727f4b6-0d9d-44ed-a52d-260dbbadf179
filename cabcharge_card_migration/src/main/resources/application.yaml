spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: oracle.jdbc.OracleDriver
    dcp:
      jdbc-url: ******************************= (address=(protocol=tcps)(port=1521)(host=***********))(connect_data=(service_name=gbe786bc9113b95_ptcnuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
      username: dcp_user
      password: ocicUat$210_st
    #      driver-class-name: oracle.jdbc.driver.OracleDriver
    ibs:
      jdbc-url: ******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfsuat1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfsuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
      username: FPT_FINTECH_USER
      password: FTB$Zrd_Dr89
      #      driverClassName: org.h2.Driver
    #      driver-class-name: oracle.jdbc.driver.OracleDriver
    ptda:
      jdbc-url: ******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptdapro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptdapro_low.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
      username: CDG_DENG_HUI
      password: abcdABCD1234
      driver-class-name: oracle.jdbc.driver.OracleDriver
    gateway:
      url: ***************************************************************************************************************
      username: dbadmin
      password: RrqTGYp3s3%R.5f<wig!.w|7Kcqt
      driverClassName: org.postgresql.Driver
  jpa:
    database-platform: org.hibernate.dialect.OracleDialect
    hibernate:
      ddl-auto: none
      properties:
        hibernate:
          jdbc:
            lob:
              non_contextual_creation: true
#  jpa:
#    hibernate:
#      ddl-auto: update
#    properties:
#      hibernate:
#        dialect: org.hibernate.dialect.OracleDialect
#    database-platform: org.hibernate.community.dialect.OraclecDialect
#
#  jpa:
#    properties:
#      hibernate:
#        dialect: org.hibernate.dialect.Oracle12cDialect

cdg:
  ccp:
    redirecturl: https://interapps-uat.cdgtaxi.com.sg/dcp-payment/rest/v1/complete?state=success
    cancelredirecturl: https://interapps-uat.cdgtaxi.com.sg/dcp-payment/rest/v1/complete?state=cancel
    failredirecturl: https://interapps-uat.cdgtaxi.com.sg/dcp-payment/rest/v1/complete?state=fail
    processingredirecturl: https://interapps-uat.cdgtaxi.com.sg/dcp-payment/rest/v1/complete?state=processing
    successredirecturl: https://interapps-uat.cdgtaxi.com.sg/dcp-payment/rest/v1/complete?state=success

