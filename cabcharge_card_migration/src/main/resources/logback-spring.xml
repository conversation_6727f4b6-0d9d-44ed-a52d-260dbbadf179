<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <property name="serviceName" value="cabchargegatewaycardmigration" />
  <property name="logPath" value="../logs" />

  <property name="logPattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p %X{hostName} --- [%15.15t] %-40.40c{1} : %m%n%ex" />

  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>${logPattern}</pattern>
    </encoder>
  </appender>

  <appender name="ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${logPath}/${serviceName}.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${logPath}/%d{yyyy-MM-dd}/${serviceName}-%d{HH}.log</fileNamePattern>
      <maxHistory>10</maxHistory>
      <cleanHistoryOnStart>true</cleanHistoryOnStart>
    </rollingPolicy>
    <encoder>
      <pattern>${logPattern}</pattern>
    </encoder>
  </appender>

  <logger name="ROOT" level="INFO">
    <appender-ref ref="ROLLING_FILE" />
    <appender-ref ref="CONSOLE" />
  </logger>
</configuration>
