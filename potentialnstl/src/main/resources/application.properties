spring.application.name=nstl

spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.type=com.zaxxer.hikari.HikariDataSource

spring.datasource.pym.jdbc-url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.pym.username=CDG_DENG_HUI
spring.datasource.pym.password=Cdg0000278_ptfs01



spring.datasource.cn3.jdbc-url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptccpro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptccpro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.cn3.username=CDG_DENG_HUI
spring.datasource.cn3.password=cdg0000278_4Am%5##234#


# HikariCP settings
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=30
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000
spring.datasource.hikari.poolName=paymentServicePoolSources

spring.jpa.hibernate.connection.provider_class=org.hibernate.hikaricp.internal.HikariCPConnectionProvider


# JPA specific configs
spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.use_sql=false
spring.jpa.properties.hibernate.id.new_generator_mappings=false
spring.jpa.properties.hibernate.search.autoregister_listeners=false
spring.jpa.properties.hibernate.bytecode.use_reflection_optimizer=false

# Enable logging to verify that HikariCP is used, the second entry is specific to HikariCP
logging.level.org.hibernate.SQL=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE




