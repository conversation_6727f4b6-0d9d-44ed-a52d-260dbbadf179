package com.cdgtaxi.nstl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class PortentialNstlApplication implements CommandLineRunner {

  @Autowired PotentialNSTLSvc potentialNSTLSvc;
  @Autowired VerifyPotentialNSTLSvc verifyPotentialNSTLSvc;

  public static void main(String[] args) {
    SpringApplication.run(PortentialNstlApplication.class, args);
  }

  /*
  verifyPotentialNSTLviaExcel:Args: VERIFYEXCEL
  generateReportOnlyOnce: Args E.g. "20241113 00:00:00" "20241114 00:00:00"  "invalid"
  generateReportForEachHour: Args. N.A.
   */
  @Override
  public void run(String... args) throws Exception {

    if (args.length == 1 && args[0].toUpperCase().contains("VERIFYEXCEL")) {
      verifyPotentialNSTLviaExcel();

    } else if (args.length == 2) {
      generateReportOnlyOnce(args);
    } else {
      generateReportForEachHour();
    }
  }

  private void verifyPotentialNSTLviaExcel() {
    System.out.println("Verify potential NSTL via Excel");
    verifyPotentialNSTLSvc.verify();
  }

  private void generateReportOnlyOnce(String[] args) {
    if (args.length != 2) {
      System.out.println("wrong parameter, fallback to no parameter method");
      generateReportForEachHour();
      return;
    }

    String startDT = args[0];
    String endDT = args[1];
    System.out.println("-------------------------------------");
    System.out.println("Generate report only once");
    System.out.println("startDT:" + startDT);
    System.out.println("endDT:" + endDT);
    potentialNSTLSvc.checkPortentialNSTL(startDT, endDT);
  }

  private void generateReportForEachHour() {
    // Get current date time with format of yyyyMMdd HH:00:00  (E.g.20241113 00:00:00)
    LocalDateTime roundedTime =
        LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss");

    String formattedDateTime = roundedTime.format(formatter);
    System.out.println("currentDT:" + formattedDateTime);

    String startDT = "";
    String endDT = "";
    // Loop for 24 hour for rounded time
    for (int i = 0; i < 24; i++) {
      startDT = roundedTime.format(formatter);
      endDT = roundedTime.plusHours(1).format(formatter);

      // Generate until previous hour
      if (roundedTime.plusHours(1).isAfter(LocalDateTime.now())) {
        break;
      }
      System.out.println("-------------------------------------");
      System.out.println("-------------------------------------");
      System.out.println("Check report for startDT:" + startDT + ", endDT:" + endDT);
      potentialNSTLSvc.checkPortentialNSTL(startDT, endDT);

      roundedTime = roundedTime.plusHours(1);
    }
  }
}
