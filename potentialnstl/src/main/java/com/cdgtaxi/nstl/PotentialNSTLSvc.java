package com.cdgtaxi.nstl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.cdgtaxi.nstl.cn3.entities.*;
import com.cdgtaxi.nstl.cn3.repositories.*;
import com.cdgtaxi.nstl.pym.entities.AllTripEntity;
import com.cdgtaxi.nstl.pym.models.PotentialNSTLDTO;
import com.cdgtaxi.nstl.pym.repositories.PotentialJOBDetialRepository;
import com.cdgtaxi.nstl.pym.repositories.TripDetialRepository;
import jakarta.persistence.*;
import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@AllArgsConstructor
@Service
public class PotentialNSTLSvc {

  @Autowired
  @Qualifier("pymEntityManagerFactory")
  private EntityManagerFactory entityManagerFactory;

  private final PotentialJOBDetialRepository potentialJOBDetailsRepository;
  private final TripDetialRepository tripDetialRepository;
  private final NetsJobDetailsEntityRepository netsJobDetailsEntityRepository;
  private final KrisJobDetailsEntityRepository krisJobDetailsEntityRepository;
  private final CofVirtualCabchargeJobDetailsEntityRepository
      cofVirtualCabchargeJobDetailsEntityRepository;

  private final CabChargeNPXJOBDetailsEntityRepository cabChargeNPXJOBDetailsEntityRepository;
  private final PaylahJobDetailsEntityRepository paylahJobDetailsEntityRepository;
  private final PytbNotificationDetailsEntityRepository pytbNotificationJobDetailsEntityRepository;

  //    public List<String> getPotentialNSTLJobByTimestamp(String startDT, String endDT) {
  //        List<PotentialNSTLEntity> potentialNSTLJobList =
  // potentialJOBDetailsRepository.findPotentialNSTLJobByTimestamp(startDT, endDT);
  //        System.out.println("getPotentialNSTLJobByTimestamp size:" +
  // potentialNSTLJobList.size());
  //        return
  // potentialNSTLJobList.stream().map(PotentialNSTLEntity::getJobNo).collect(Collectors.toList());
  //
  //    }
  //
  //    public List<AllTripEntity> getAllTripByStartEndTime(String startDT, String endDT) {
  //
  //        List<AllTripEntity> allTripList =
  // tripDetialRepository.findAllTripByStartEndTime(startDT,endDT);
  //        System.out.println("size:" + allTripList.size());
  //        return allTripList;
  //    }

  void checkPortentialNSTL(String startDT, String endDT) {

    // Step1: get all trips in the time range
    List<AllTripEntity> allTripEntityList =
        tripDetialRepository.findAllTripByStartEndTime(startDT, endDT);
    List<String> jobNoInAllTrips =
        allTripEntityList.stream().map(AllTripEntity::getJobNo).collect(Collectors.toList());
    System.out.println("allTripEntityList size:" + allTripEntityList.size());
    System.out.println("jobNoInAllTrips size:" + jobNoInAllTrips.size());

    // Prepare list for step2
    List<List<String>> subLists = splitListIntoSubLists(jobNoInAllTrips, 1000);
    List<String>[] allTripArray = new List[30];
    for (int i = 0; i < allTripArray.length; i++) {
      allTripArray[i] = new ArrayList<>(Arrays.asList("null"));
    }
    for (int i = 0; i < subLists.size(); i++) {
      allTripArray[i] = subLists.get(i);
    }

    // Step2: Check pinpad on board payment for Cabcharge and Credit card (NPX)
    List<String> jobNoListInCabChargeNPX = new ArrayList<>();

    List<CabChargeNPXJOBDetailsEntity> cabChargeNPXJobDetailsEntities =
        cabChargeNPXJOBDetailsEntityRepository.findAllJOBsInCabchargeNPX(
            allTripArray[0],
            allTripArray[1],
            allTripArray[2],
            allTripArray[3],
            allTripArray[4],
            allTripArray[5],
            allTripArray[6],
            allTripArray[7],
            allTripArray[8],
            allTripArray[9],
            allTripArray[10],
            allTripArray[11],
            allTripArray[12],
            allTripArray[13],
            allTripArray[14],
            allTripArray[15],
            allTripArray[16],
            allTripArray[17],
            allTripArray[18],
            allTripArray[19],
            allTripArray[20],
            allTripArray[21],
            allTripArray[22],
            allTripArray[23],
            allTripArray[24],
            allTripArray[25],
            allTripArray[26],
            allTripArray[27],
            allTripArray[28],
            allTripArray[29]);
    if (cabChargeNPXJobDetailsEntities.size() > 0) {
      jobNoListInCabChargeNPX.addAll(
          cabChargeNPXJobDetailsEntities.stream()
              .map(CabChargeNPXJOBDetailsEntity::getJobNo)
              .collect(Collectors.toList()));
    }
    System.out.println(
        "cabChargeNPXJobDetailsEntities size:" + cabChargeNPXJobDetailsEntities.size());

    // Step3: Check in app Paylah capture trx
    List<String> jobNoListInPaylah = new ArrayList<>();
    List<PaylahJobDetailsEntity> paylahJobDetailsEntities =
        paylahJobDetailsEntityRepository.findAllJOBsInPaylah(
            allTripArray[0],
            allTripArray[1],
            allTripArray[2],
            allTripArray[3],
            allTripArray[4],
            allTripArray[5],
            allTripArray[6],
            allTripArray[7],
            allTripArray[8],
            allTripArray[9],
            allTripArray[10],
            allTripArray[11],
            allTripArray[12],
            allTripArray[13],
            allTripArray[14],
            allTripArray[15],
            allTripArray[16],
            allTripArray[17],
            allTripArray[18],
            allTripArray[19],
            allTripArray[20],
            allTripArray[21],
            allTripArray[22],
            allTripArray[23],
            allTripArray[24],
            allTripArray[25],
            allTripArray[26],
            allTripArray[27],
            allTripArray[28],
            allTripArray[29]);

    if (paylahJobDetailsEntities.size() > 0) {
      List<String> jobNoList =
          paylahJobDetailsEntities.stream()
              .map(PaylahJobDetailsEntity::getJobNumber)
              .collect(Collectors.toList());
      jobNoListInPaylah.addAll(jobNoList);
    }

    System.out.println("jobNoListInPaylah size:" + jobNoListInPaylah.size());

    // Step4:Check in app Nets capture trx
    List<String> jobNoListInNETS = new ArrayList<>();

    List<NofJobDetailsEntity> nofJobDetailsEntities =
        netsJobDetailsEntityRepository.findAllJOBsInNETS(
            allTripArray[0],
            allTripArray[1],
            allTripArray[2],
            allTripArray[3],
            allTripArray[4],
            allTripArray[5],
            allTripArray[6],
            allTripArray[7],
            allTripArray[8],
            allTripArray[9],
            allTripArray[10],
            allTripArray[11],
            allTripArray[12],
            allTripArray[13],
            allTripArray[14],
            allTripArray[15],
            allTripArray[16],
            allTripArray[17],
            allTripArray[18],
            allTripArray[19],
            allTripArray[20],
            allTripArray[21],
            allTripArray[22],
            allTripArray[23],
            allTripArray[24],
            allTripArray[25],
            allTripArray[26],
            allTripArray[27],
            allTripArray[28],
            allTripArray[29]);

    if (nofJobDetailsEntities.size() > 0) {
      List<String> jobNoList =
          nofJobDetailsEntities.stream()
              .map(NofJobDetailsEntity::getJobNumber)
              .collect(Collectors.toList());
      jobNoListInNETS.addAll(jobNoList);
    }

    System.out.println("jobNoListInNETS size:" + jobNoListInNETS.size());

    // Step5:Check in app Kris capture trx
    List<String> jobNoListInKris = new ArrayList<>();
    List<KrisJobDetailsEntity> krisJobDetailsEntities =
        krisJobDetailsEntityRepository.findAllJOBsInKris(
            startDT,
            endDT,
            allTripArray[0],
            allTripArray[1],
            allTripArray[2],
            allTripArray[3],
            allTripArray[4],
            allTripArray[5],
            allTripArray[6],
            allTripArray[7],
            allTripArray[8],
            allTripArray[9],
            allTripArray[10],
            allTripArray[11],
            allTripArray[12],
            allTripArray[13],
            allTripArray[14],
            allTripArray[15],
            allTripArray[16],
            allTripArray[17],
            allTripArray[18],
            allTripArray[19],
            allTripArray[20],
            allTripArray[21],
            allTripArray[22],
            allTripArray[23],
            allTripArray[24],
            allTripArray[25],
            allTripArray[26],
            allTripArray[27],
            allTripArray[28],
            allTripArray[29]);
    if (krisJobDetailsEntities.size() > 0) {
      List<String> jobNoList =
          krisJobDetailsEntities.stream()
              .map(KrisJobDetailsEntity::getJobNumber)
              .collect(Collectors.toList());
      jobNoListInKris.addAll(jobNoList);
    }
    System.out.println("jobNoListInKris size:" + jobNoListInKris.size());

    // Step6:Check in app COF and Virtual Cabcharge capture trx
    List<String> jobNoListInCofVirtualCabcharge = new ArrayList<>();

    List<CofVirtualCabchargeJobDetailsEntity> cofVirtualCabchargeJobDetailsEntities =
        cofVirtualCabchargeJobDetailsEntityRepository.findAllJOBsInCofVirtualCabcharge(
            startDT,
            endDT,
            allTripArray[0],
            allTripArray[1],
            allTripArray[2],
            allTripArray[3],
            allTripArray[4],
            allTripArray[5],
            allTripArray[6],
            allTripArray[7],
            allTripArray[8],
            allTripArray[9],
            allTripArray[10],
            allTripArray[11],
            allTripArray[12],
            allTripArray[13],
            allTripArray[14],
            allTripArray[15],
            allTripArray[16],
            allTripArray[17],
            allTripArray[18],
            allTripArray[19],
            allTripArray[20],
            allTripArray[21],
            allTripArray[22],
            allTripArray[23],
            allTripArray[24],
            allTripArray[25],
            allTripArray[26],
            allTripArray[27],
            allTripArray[28],
            allTripArray[29]);

    if (cofVirtualCabchargeJobDetailsEntities.size() > 0) {
      List<String> jobNoList =
          cofVirtualCabchargeJobDetailsEntities.stream()
              .map(CofVirtualCabchargeJobDetailsEntity::getJobNo)
              .collect(Collectors.toList());
      jobNoListInCofVirtualCabcharge.addAll(jobNoList);
    }

    System.out.println(
        "jobNoListInCofVirtualCabcharge size:" + jobNoListInCofVirtualCabcharge.size());

    // Step 6x: Pytb Notification
    List<String> jobNoListInPytbNotification = new ArrayList<>();
    List<PytbNotificationJobDetailsEntity> pytbNotificationJobDetailsEntities =
        pytbNotificationJobDetailsEntityRepository.findAllJOBsInPytbNotification(
            allTripArray[0],
            allTripArray[1],
            allTripArray[2],
            allTripArray[3],
            allTripArray[4],
            allTripArray[5],
            allTripArray[6],
            allTripArray[7],
            allTripArray[8],
            allTripArray[9],
            allTripArray[10],
            allTripArray[11],
            allTripArray[12],
            allTripArray[13],
            allTripArray[14],
            allTripArray[15],
            allTripArray[16],
            allTripArray[17],
            allTripArray[18],
            allTripArray[19],
            allTripArray[20],
            allTripArray[21],
            allTripArray[22],
            allTripArray[23],
            allTripArray[24],
            allTripArray[25],
            allTripArray[26],
            allTripArray[27],
            allTripArray[28],
            allTripArray[29]);

    if (pytbNotificationJobDetailsEntities.size() > 0) {
      List<String> jobNoList =
          pytbNotificationJobDetailsEntities.stream()
              .map(PytbNotificationJobDetailsEntity::getJobNo)
              .collect(Collectors.toList());
      jobNoListInPytbNotification.addAll(jobNoList);
    }
    System.out.println("jobNoListInPytbNotification size:" + jobNoListInPytbNotification.size());

    // Step7:Rerun Step 1 query after update following from Step 2-6
    // excluded job number that having successful payment
    // merge all job no into a new List from Step 2 to step6
    List<String> jobNosuccessPaymentList = new ArrayList<>();
    jobNosuccessPaymentList.addAll(jobNoListInCabChargeNPX);
    jobNosuccessPaymentList.addAll(jobNoListInPaylah);
    jobNosuccessPaymentList.addAll(jobNoListInNETS);
    jobNosuccessPaymentList.addAll(jobNoListInKris);
    jobNosuccessPaymentList.addAll(jobNoListInCofVirtualCabcharge);
    jobNosuccessPaymentList.addAll(jobNoListInPytbNotification);

    System.out.println("jobNosuccessPaymentList size:" + jobNosuccessPaymentList.size());

    // .. Ignore step8, step9

    // Step 10: Query job details for reporting.
    // split into sublist from jobNosuccessPaymentList

    // testing start
    //        List<String> jobNosuccessPaymentList =
    // allTripEntityList.stream().map(AllTripEntity::getJobNo).collect(Collectors.toList());
    // testing end

    List<List<String>> subSuccessPaymentList = splitListIntoSubLists(jobNosuccessPaymentList, 1000);

    // Convert subSuccessPaymentList to List<String> array, initial with null
    List<String>[] array = new List[30];
    for (int i = 0; i < array.length; i++) {
      array[i] = new ArrayList<>(Arrays.asList("null"));
    }

    if (subSuccessPaymentList.size() > 30) {
      // throw exception
      throw new RuntimeException("Too many job number in success payment list");
    }

    for (int i = 0; i < subSuccessPaymentList.size(); i++) {
      array[i] = subSuccessPaymentList.get(i);
    }
    List<PotentialNSTLDTO> potentialNSTLDTOList = generateReport(startDT, endDT, array);
    System.out.println("potentialNSTLDTOList size:" + potentialNSTLDTOList.size());
    System.out.println("WriteNSTLToExcel startDT:" + startDT + " endDT:" + endDT);
    WriteNSTLToExcel(startDT, endDT, potentialNSTLDTOList);
    System.out.println("WriteNSTLToExcel Finished");
    return;
  }

  public List<PotentialNSTLDTO> generateReport(String startDT, String endDT, List<String>[] array) {

    // get entity manager from entityManagerFactory
    EntityManager entityManager = entityManagerFactory.createEntityManager();
    List<PotentialNSTLDTO> potentialNSTLReport = new ArrayList<>();
    try {
      String queryStr =
          "--potential NSTL\n"
              + "select --job_no,t.payment_mode,\n"
              + "--trunc(t.create_date)\n"
              + "to_char(completed_dt, 'YYYY')\"year\",to_char(completed_dt, 'MM')\"month\", \n"
              + "to_char(completed_dt,'dd') \"date\"--,to_char(completed_dt,'hh24') \"hour\"\n"
              + "--trunc(t.completed_dt)\n"
              + ",payment_mode,crca_type\n"
              + ",sum(txn_amount)amount--,payment_status\n"
              + ",count(*) cnt,sum(case when patch_code='LMBO' then 1 else 0 end) limbo_trip\n"
              + "--,sum(case when paid_job_no = 'Y' then 1 else 0 end)total_paid_job_no\n"
              + "--,c_dt\n"
              + "from ( \n"
              + "select t.job_no,t.completed_dt, t.payment_mode, t.txn_amount\n"
              + ",(case when s.job_no is not null then 'Y' else 'N' end) paid_job_no\n"
              + ",patch_code,to_char(t.create_date, 'yyyymmdd')c_dt\n"
              + ",t.approval_code,t.job_status\n"
              + "         ,(case\n"
              + "                when t.payment_mode = 'CRCA' and t.approval_code is null then 'COF'\n"
              + "                when t.payment_mode = 'CRCA' and t.approval_code is not null then 'ON_BOARD'\n"
              + "                else ''\n"
              + "         end) as CRCA_TYPE,t.payment_status\n"
              + ",s.recon_dt\n"
              + ",t.CARD_NO \n"
              + "from recsys.rctb_trip_intf t\n"
              + "left join recsys.rctb_setl s on t.job_no = s.job_no and s.recon_flag = 'R'\n"
              + "where\n"
              + "1=1\t\n"
              + "--and status='N'\n"
              + "and t.completed_dt>to_date(:startDT,'yyyymmdd hh24:mi:ss')\n"
              + "--and t.completed_dt>to_date('20241112 08:00:00','yyyymmdd hh24:mi:ss')\n"
              + "and t.completed_dt<to_date(:endDT,'yyyymmdd hh24:mi:ss')\n"
              + "--and patch_code='LMBO'\n"
              + "--and t.create_date>to_date(to_char(sysdate-1,'yyyymmdd')||' 00:00:00','yyyymmdd hh24:mi:ss')\n"
              + "--and t.create_date<to_date(to_char(sysdate-1,'yyyymmdd')||' 22:00:00','yyyymmdd hh24:mi:ss')\n"
              + "--and t.create_date>sysdate-7\n"
              + "--and t.create_date>to_date('20240201 00:00:00','yyyymmdd hh24:mi:ss')\n"
              + "--and t.completed_dt<to_date(to_char(sysdate-1,'yyyymmdd')||' 22:00:00','yyyymmdd hh24:mi:ss')\n"
              + "--and to_char(t.completed_dt,'dd')='24'\n"
              + "--and error_code is not null\n"
              + "--and error_code='NSTL'\n"
              + "and t.payment_mode in ('CABC','CRCA','DBSPOF','NOF','PLAH5','KRISH5')\n"
              + "--and t.payment_mode in ('CRCA')\n"
              + "--and t.payment_mode not in ('DBSPOF','NOF')\n"
              + "--and t.approval_code is null\n"
              + "and t.job_status='COMPLETED'\n"
              + "AND t.JOB_NO NOT LIKE '7%'\n"
              + "--AND t.JOB_NO NOT LIKE '52%'\n"
              + "--and patch_code is null\n"
              + "--and not exists (select * from recsys.rctb_setl s where s.job_no=t.job_no) and not (payment_mode in ('CABC', 'EVCH') and status = 'R')\n"
              + "AND ( t.job_no NOT IN (:jobNoList1)\n"
              + "AND t.job_no NOT IN (:jobNoList2)\n"
              + "AND t.job_no NOT IN (:jobNoList3)\n"
              + "AND t.job_no NOT IN (:jobNoList4)\n"
              + "AND t.job_no NOT IN (:jobNoList5)\n"
              + "AND t.job_no NOT IN (:jobNoList6)\n"
              + "AND t.job_no NOT IN (:jobNoList7)\n"
              + "AND t.job_no NOT IN (:jobNoList8)\n"
              + "AND t.job_no NOT IN (:jobNoList9)\n"
              + "AND t.job_no NOT IN (:jobNoList10)  \n"
              + "AND t.job_no NOT IN (:jobNoList11)  \n"
              + "AND t.job_no NOT IN (:jobNoList12)  \n"
              + "AND t.job_no NOT IN (:jobNoList13)  \n"
              + "AND t.job_no NOT IN (:jobNoList14)  \n"
              + "AND t.job_no NOT IN (:jobNoList15)  \n"
              + "AND t.job_no NOT IN (:jobNoList16)  \n"
              + "AND t.job_no NOT IN (:jobNoList17)  \n"
              + "AND t.job_no NOT IN (:jobNoList18)  \n"
              + "AND t.job_no NOT IN (:jobNoList19)  \n"
              + "AND t.job_no NOT IN (:jobNoList20)  \n"
              + "AND t.job_no NOT IN (:jobNoList21)  \n"
              + "AND t.job_no NOT IN (:jobNoList22)  \n"
              + "AND t.job_no NOT IN (:jobNoList23)  \n"
              + "AND t.job_no NOT IN (:jobNoList24)  \n"
              + "AND t.job_no NOT IN (:jobNoList25)  \n"
              + "AND t.job_no NOT IN (:jobNoList26)  \n"
              + "AND t.job_no NOT IN (:jobNoList27)  \n"
              + "AND t.job_no NOT IN (:jobNoList28)  \n"
              + "AND t.job_no NOT IN (:jobNoList29)  \n"
              + "AND t.job_no NOT IN (:jobNoList30)  )\n"
              + "ORDER BY t.COMPLETED_DT \n"
              + ")\n"
              + "group by to_char(completed_dt, 'YYYY'),to_char(completed_dt, 'MM'),\n"
              + "to_char(completed_dt,'dd')--,to_char(completed_dt,'hh24')\n"
              + "--,trunc(t.completed_dt)\n"
              + ",payment_mode,crca_type--,payment_status\n"
              + "--,c_dt\n"
              + "order by payment_mode,to_char(completed_dt, 'YYYY'),to_char(completed_dt, 'MM'),\n"
              + "to_char(completed_dt,'dd')--,to_char(completed_dt,'hh24')\n"
              + "--,trunc(t.completed_dt)\n"
              + ",crca_type \n";

      //            Query<PotentialNSTLDTO> potentialNSTLDTOMapping = (Query<PotentialNSTLDTO>)
      // entityManager.createNativeQuery(queryStr, "PotentialNSTLDTOMapping");
      //
      //            System.out.println(potentialNSTLDTOMapping);
      //
      System.out.println("generate Final Report");

      Query nativeQuery = entityManager.createNativeQuery(queryStr);
      nativeQuery.setParameter("startDT", startDT);
      nativeQuery.setParameter("endDT", endDT);
      for (int i = 0; i < array.length; i++) {
        nativeQuery.setParameter("jobNoList" + (i + 1), array[i]);
      }
      List<Object[]> resultList = nativeQuery.getResultList();

      potentialNSTLReport =
          resultList.stream()
              .map(
                  objects -> {
                    PotentialNSTLDTO entity = new PotentialNSTLDTO();
                    entity.setYear((String) objects[0]);
                    entity.setMonth((String) objects[1]);
                    entity.setDate((String) objects[2]);
                    entity.setPaymentMode((String) objects[3]);
                    entity.setCrcaType((String) objects[4]);
                    entity.setAmount(((BigDecimal) objects[5]).toString());
                    entity.setCnt(((BigDecimal) objects[6]).toString());
                    entity.setLimboTrip(((BigDecimal) objects[7]).toString());
                    return entity;
                  })
              .collect(Collectors.toList());

      potentialNSTLReport.forEach(System.out::println);

    } finally {
      entityManager.close();
    }
    return potentialNSTLReport;
  }

  public static List<List<String>> splitListIntoSubLists(
      List<String> originalList, int subListSize) {
    List<List<String>> subLists = new ArrayList<>();
    for (int i = 0; i < originalList.size(); i += subListSize) {
      int end = Math.min(i + subListSize, originalList.size());
      subLists.add(new ArrayList<>(originalList.subList(i, end)));
    }
    return subLists;
  }

  protected void WriteNSTLToExcel(String startDT, String endDT, List<PotentialNSTLDTO> list) {

    String fileName = "";
    String[] splitFileName = startDT.split(" ");
    if (splitFileName.length > 0) {
      fileName = splitFileName[0];
    }

    String sheetName = StringUtils.trimAllWhitespace(startDT).replace(":", "");
    System.out.println("sheetName:" + sheetName);

    String filePath = "PotentialNSTLReport_" + fileName + ".xlsx";
    String filePathBak = "PotentialNSTLReport_" + fileName + "_bak.xlsx";
    try {
      File sourceFile = new File(filePath);
      File destFile = new File(filePathBak);

      if (sourceFile.exists()) {
        // Load existing workbook

        // step1: make another copy
        System.out.println("report already exists, append sheet");

        // recreate dest file
        destFile.createNewFile();

        FileUtils.copyFile(sourceFile, destFile);

        // recreate source file
        sourceFile.createNewFile();

        // step2: write back to source file, append new sheet
        EasyExcel.write()
            .withTemplate(destFile)
            .file(sourceFile)
            .sheet(sheetName)
            .head(PotentialNSTLDTO.class)
            .doWrite(list);

      } else {
        System.out.println("create new report");
        // create new excel
        try (ExcelWriter excelWriter =
            EasyExcel.write(sourceFile.getName(), PotentialNSTLDTO.class).build()) {
          WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
          excelWriter.write(list, writeSheet);
        } catch (Exception e) {
          e.printStackTrace();
        }
      }

    } catch (Exception e) {
      e.printStackTrace();
    }
  }
}
