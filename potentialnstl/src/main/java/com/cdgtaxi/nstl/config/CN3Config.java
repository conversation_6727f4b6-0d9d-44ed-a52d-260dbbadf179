package com.cdgtaxi.nstl.config;

import jakarta.persistence.EntityManagerFactory;
import java.util.HashMap;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    entityManagerFactoryRef = "cn3EntityManagerFactory",
    transactionManagerRef = "cn3TransactionManager",
    basePackages = {"com.cdgtaxi.nstl.cn3"})
public class CN3Config {

  @Primary
  @Bean(name = "cn3DataSource")
  @ConfigurationProperties(prefix = "spring.datasource.cn3")
  public DataSource dataSource() {
    return DataSourceBuilder.create().build();
  }

  @Bean(name = "cn3EntityManagerFactory")
  public LocalContainerEntityManagerFactoryBean dcpEntityManagerFactory(
      @Qualifier("cn3DataSource") DataSource dataSource) {

    LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
    em.setDataSource(dataSource);
    em.setPackagesToScan("com.cdgtaxi.nstl.cn3.entities");

    HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
    em.setJpaVendorAdapter(vendorAdapter);

    HashMap<String, Object> properties = new HashMap<>();
    properties.put("hibernate.hbm2ddl.auto", "none");
    properties.put("hibernate.dialect", "org.hibernate.dialect.OracleDialect");
    em.setJpaPropertyMap(properties);
    return em;
  }

  @Primary
  @Bean(name = "cn3TransactionManager")
  public PlatformTransactionManager dcpTransactionManager(
      @Qualifier("cn3EntityManagerFactory") EntityManagerFactory entityManagerFactory) {
    return new JpaTransactionManager(entityManagerFactory);
  }
}
