package com.cdgtaxi.nstl.util;

import com.alibaba.excel.EasyExcel;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class ExcelSvc {

  public List<String> readCOFJobNo(String fileName) {
    // easyexcel to read job no from excel
    List<String> jobNoList = new ArrayList<>();

    PotentialNSTLDataListener daDataListener = new PotentialNSTLDataListener();

    EasyExcel.read(fileName, PotentialNSTLData.class, daDataListener).sheet().doRead();
    return daDataListener.getCOFJobNoList();
  }
}
