package com.cdgtaxi.nstl.util;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class PotentialNSTLDataListener extends AnalysisEventListener<PotentialNSTLData> {
  // This list will store the DTO objects
  private List<PotentialNSTLData> jobNoList = new ArrayList<>();

  @Override
  public void invoke(PotentialNSTLData data, AnalysisContext context) {
    System.out.println("Reading a row: " + data);
    jobNoList.add(data); // Add each row's data to the list
  }

  @Override
  public void doAfterAllAnalysed(AnalysisContext context) {
    System.out.println("All rows are read. Total: " + jobNoList.size());
  }

  public List<String> getCOFJobNoList() {
    return jobNoList.stream()
        .filter(k -> k.getPaymentMode().equalsIgnoreCase("CRCA"))
        .map(PotentialNSTLData::getJobNo)
        .collect(Collectors.toList());
  }
}
