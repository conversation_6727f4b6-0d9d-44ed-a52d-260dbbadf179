package com.cdgtaxi.nstl.util;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@ToString
@Setter
@Getter
public class PotentialNSTLData {

  @Id
  @Column(name = "JOB_NO")
  private String jobNo;

  @Column(name = "COMPLETED_DT")
  private String completedDt;

  @Column(name = "PAYMENT_MODE")
  private String paymentMode;

  @Column(name = "TXN_AMOUNT")
  private String txnAmount;

  @Column(name = "PAID_JOB_NO")
  private String paidJobNo;

  @Column(name = "PATCH_CODE")
  private String patchCode;

  @Column(name = "C_DT")
  private String cDt;

  @Column(name = "APPROVAL_CODE")
  private String approvalCode;

  @Column(name = "JOB_STATUS")
  private String jobStatus;

  @Column(name = "CRCA_TYPE")
  private String crcaType;

  @Column(name = "PAYMENT_STATUS")
  private String paymentStatus;

  @Column(name = "RECON_DT")
  private String reconDt;

  // Getters and Setters

  public String getJobNo() {
    return jobNo;
  }

  public void setJobNo(String jobNo) {
    this.jobNo = jobNo;
  }

  public String getCompletedDt() {
    return completedDt;
  }

  public void setCompletedDt(String completedDt) {
    this.completedDt = completedDt;
  }

  public String getPaymentMode() {
    return paymentMode;
  }

  public void setPaymentMode(String paymentMode) {
    this.paymentMode = paymentMode;
  }

  public String getTxnAmount() {
    return txnAmount;
  }

  public void setTxnAmount(String txnAmount) {
    this.txnAmount = txnAmount;
  }

  public String getPaidJobNo() {
    return paidJobNo;
  }

  public void setPaidJobNo(String paidJobNo) {
    this.paidJobNo = paidJobNo;
  }

  public String getPatchCode() {
    return patchCode;
  }

  public void setPatchCode(String patchCode) {
    this.patchCode = patchCode;
  }

  public String getCDt() {
    return cDt;
  }

  public void setCDt(String cDt) {
    this.cDt = cDt;
  }

  public String getApprovalCode() {
    return approvalCode;
  }

  public void setApprovalCode(String approvalCode) {
    this.approvalCode = approvalCode;
  }

  public String getJobStatus() {
    return jobStatus;
  }

  public void setJobStatus(String jobStatus) {
    this.jobStatus = jobStatus;
  }

  public String getCrcaType() {
    return crcaType;
  }

  public void setCrcaType(String crcaType) {
    this.crcaType = crcaType;
  }

  public String getPaymentStatus() {
    return paymentStatus;
  }

  public void setPaymentStatus(String paymentStatus) {
    this.paymentStatus = paymentStatus;
  }

  public String getReconDt() {
    return reconDt;
  }

  public void setReconDt(String reconDt) {
    this.reconDt = reconDt;
  }
}
