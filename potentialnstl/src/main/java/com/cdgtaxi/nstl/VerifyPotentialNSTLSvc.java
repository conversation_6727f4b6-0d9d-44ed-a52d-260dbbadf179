package com.cdgtaxi.nstl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.cdgtaxi.nstl.cn3.entities.EscJobDetailsEntity;
import com.cdgtaxi.nstl.cn3.entities.PytbWdTxnDetailsEntity;
import com.cdgtaxi.nstl.cn3.repositories.EscJobDetailsEntityRepository;
import com.cdgtaxi.nstl.cn3.repositories.PytbWdTxnDetailsEntityRepository;
import com.cdgtaxi.nstl.util.ExcelSvc;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class VerifyPotentialNSTLSvc {

  class PytbWdJobNoStatus {
    String jobNo;
    String preAuthStatus;
    String captureStatus;

    public PytbWdJobNoStatus(String jobNo, String preAuthStatus, String captureStatus) {
      this.jobNo = jobNo;
      this.preAuthStatus = preAuthStatus;
      this.captureStatus = captureStatus;
    }

    @Override
    public String toString() {
      return "PytbWdJobNoStatus{"
          + "jobNo='"
          + jobNo
          + '\''
          + ", preAuthStatus='"
          + preAuthStatus
          + '\''
          + ", captureStatus='"
          + captureStatus
          + '\''
          + '}';
    }
  }

  public final PytbWdTxnDetailsEntityRepository pytbWdTxnDetailsEntityRepository;
  public final EscJobDetailsEntityRepository escJobDetailsEntityRepository;

  public void verify() {
    System.out.println("VerifyPotentialNSTLSvc");
    List<String> jobNoListFromReport = new ArrayList<>();
    List<PytbWdJobNoStatus> pytbWdJobNoStatusList = new ArrayList<>();
    // Step1: get all potential nstl job no from daily excel file
    jobNoListFromReport = getExcelJobNoListFromRepot();

    System.out.println("Total COF Job No in Daily Excel Report: " + jobNoListFromReport.size());

    for (String jobNo : jobNoListFromReport) {
      PytbWdJobNoStatus pytbWdJobNoStatus = new PytbWdJobNoStatus(jobNo, "", "");
      pytbWdJobNoStatusList.add(pytbWdJobNoStatus);
    }

    // Step2: loop job no to check if it exist in the database
    List<PytbWdTxnDetailsEntity> allByJobNoInPytbWdEntries =
        pytbWdTxnDetailsEntityRepository.findAllByJobNoInPytbWd(jobNoListFromReport);

    // check if job no exist in the database
    for (PytbWdTxnDetailsEntity jobNoInPytbWdEntry : allByJobNoInPytbWdEntries) {
      System.out.println(jobNoInPytbWdEntry);
      for (PytbWdJobNoStatus pytbWdJobNoStatus : pytbWdJobNoStatusList) {
        if (pytbWdJobNoStatus.jobNo.equals(jobNoInPytbWdEntry.getJobNo())) {
          if (jobNoInPytbWdEntry.getTxnType().toLowerCase().contains("authorization")) {
            pytbWdJobNoStatus.preAuthStatus = "Success";
          } else if (jobNoInPytbWdEntry.getTxnType().toLowerCase().contains("capture")) {
            pytbWdJobNoStatus.captureStatus = jobNoInPytbWdEntry.getTxnState();
          }
        }
      }
    }

    System.out.println("Potential NSTL Analysis Result: ");
    System.out.println("-----------------------------------------------");
    System.out.println("-----------------------------------------------");
    // if exist, print out the job no
    // if not exist, print out the job no

    // check preAuth
    List<PytbWdJobNoStatus> cofNoPreAuthList =
        pytbWdJobNoStatusList.stream()
            .filter(pytbWdJobNoStatus -> !pytbWdJobNoStatus.preAuthStatus.equals("Success"))
            .collect(Collectors.toList());

    System.out.println("No preAuth: " + cofNoPreAuthList.size());
    cofNoPreAuthList.forEach(pytbWdJobNoStatus -> System.out.println(pytbWdJobNoStatus));

    // check no capture
    List<PytbWdJobNoStatus> cofNoCaptureList =
        pytbWdJobNoStatusList.stream()
            .filter(pytbWdJobNoStatus -> pytbWdJobNoStatus.preAuthStatus.equals("Success"))
            .filter(pytbWdJobNoStatus -> pytbWdJobNoStatus.captureStatus.isEmpty())
            .collect(Collectors.toList());
    System.out.println("No Capture: " + cofNoCaptureList.size());
    cofNoCaptureList.forEach(pytbWdJobNoStatus -> System.out.println(pytbWdJobNoStatus));

    // check capture failed
    List<PytbWdJobNoStatus> cofCaptureFailedList =
        pytbWdJobNoStatusList.stream()
            .filter(pytbWdJobNoStatus -> pytbWdJobNoStatus.preAuthStatus.equals("Success"))
            .filter(pytbWdJobNoStatus -> pytbWdJobNoStatus.captureStatus.equalsIgnoreCase("failed"))
            .collect(Collectors.toList());

    System.out.println("Capture Failed: " + cofCaptureFailedList.size());
    cofCaptureFailedList.forEach(pytbWdJobNoStatus -> System.out.println(pytbWdJobNoStatus));

    // check capture success
    List<PytbWdJobNoStatus> cofCaptureSuccessList =
        pytbWdJobNoStatusList.stream()
            .filter(pytbWdJobNoStatus -> pytbWdJobNoStatus.preAuthStatus.equals("Success"))
            .filter(pytbWdJobNoStatus -> !pytbWdJobNoStatus.captureStatus.isEmpty())
            .filter(
                pytbWdJobNoStatus -> !pytbWdJobNoStatus.captureStatus.equalsIgnoreCase("failed"))
            .collect(Collectors.toList());
    System.out.println("Capture Success: " + cofCaptureSuccessList.size());
    cofCaptureSuccessList.forEach(pytbWdJobNoStatus -> System.out.println(pytbWdJobNoStatus));

    System.out.println("-----------------------------------------------");
    System.out.println("-----------------------------------------------");
    System.out.println("Application POST NSTL Trigger conditon");
    List<EscJobDetailsEntity> allPostNSTLJobNoList =
        escJobDetailsEntityRepository.findAllByJobNo(
            cofNoCaptureList.stream()
                .map(pytbWdJobNoStatus -> pytbWdJobNoStatus.jobNo)
                .collect(Collectors.toList()));
    System.out.println("Total Job No in POST NSTL Trigger : " + allPostNSTLJobNoList.size());
    allPostNSTLJobNoList.forEach(escJobDetailsEntity -> System.out.println(escJobDetailsEntity));

    // write to excel
    generateReportForPOSTLNSTLTriggerCondition(allPostNSTLJobNoList);
  }

  private void generateReportForPOSTLNSTLTriggerCondition(
      List<EscJobDetailsEntity> allPostNSTLJobNoList) {

    String filePath = "/home/<USER>/Documents/nstl_recovery/";
    String fileName =
        "PotentialNSTL_POST_NSTL_Trigger_Condition_"
            + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())
            + ".xlsx";
    // generate excel by using easyexcel
    try (ExcelWriter excelWriter =
        EasyExcel.write(filePath + fileName, EscJobDetailsEntity.class).build()) {
      WriteSheet writeSheet = EasyExcel.writerSheet("cof").build();
      excelWriter.write(allPostNSTLJobNoList, writeSheet);
    }

    System.out.println("Excel Generated: " + filePath + fileName);
  }

  private List<String> getExcelJobNoListFromRepot() {

    List<String> idList = new ArrayList<>();

    // Read from excel
    String filePath = "/home/<USER>/Documents/splitJobNos/";
    String fileName = "Potential NSTL (20 November 2024).xlsx";

    // Use EasyExcel to read excel
    ExcelSvc excelSvc = new ExcelSvc();
    idList = excelSvc.readCOFJobNo(filePath + fileName);
    return idList;
  }
}
