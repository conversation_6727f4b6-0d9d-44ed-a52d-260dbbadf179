package com.cdgtaxi.nstl.cn3.repositories;

import com.cdgtaxi.nstl.cn3.entities.PytbWdTxnDetailsEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PytbWdTxnDetailsEntityRepository
    extends JpaRepository<PytbWdTxnDetailsEntity, String> {
  @Query(nativeQuery = true, name = "findAllByJobNoInPytbWd")
  List<PytbWdTxnDetailsEntity> findAllByJobNoInPytbWd(@Param("jobNoList") List<String> jobNoList);
}
