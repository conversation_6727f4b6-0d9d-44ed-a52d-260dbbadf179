package com.cdgtaxi.nstl.pym.entities;

import jakarta.persistence.*;
import lombok.*;

@Entity
@ToString
@NamedNativeQuery(
    name = "findPotentialNSTLTest",
    query =
        "SELECT ROW_NUMBER() OVER (ORDER BY 1)  AS id, ppt.\"year\", ppt.\"month\", ppt.\"date\", ppt.payment_mode, ppt.crca_type, ppt.amount, ppt.limbo_trip FROM (\n"
            + "--SELECT ROW_NUMBER() OVER (ORDER BY 1)  AS id, ppt.year, ppt.month, ppt.dd, ppt.payment_mode, ppt.CRCA_TYPE, ppt.amount, ppt.cnt, ppt.limbo_trip FROM (\n"
            + "SELECT\n"
            + "--ROW_NUMBER() OVER (ORDER BY completed_dt) AS id,\n"
            + "--job_no,t.payment_mode,\n"
            + "--trunc(t.create_date)\n"
            + "to_char(completed_dt, 'YYYY')\"year\",to_char(completed_dt, 'MM')\"month\",\n"
            + "to_char(completed_dt,'dd') \"date\"\n"
            + "--trunc(t.completed_dt)\n"
            + ",payment_mode,crca_type\n"
            + ",sum(txn_amount)amount--,payment_status\n"
            + ",count(*) cnt,sum(case when patch_code='LMBO' then 1 else 0 end) limbo_trip\n"
            + "--,sum(case when paid_job_no = 'Y' then 1 else 0 end)total_paid_job_no\n"
            + "--,c_dt\n"
            + "from (\n"
            + "select t.job_no,t.completed_dt, t.payment_mode, t.txn_amount\n"
            + ",(case when s.job_no is not null then 'Y' else 'N' end) paid_job_no\n"
            + ",patch_code,to_char(t.create_date, 'yyyymmdd')c_dt\n"
            + ",t.approval_code,t.job_status\n"
            + "         ,(case\n"
            + "                when t.payment_mode = 'CRCA' and t.approval_code is null then 'COF'\n"
            + "                when t.payment_mode = 'CRCA' and t.approval_code is not null then 'ON_BOARD'\n"
            + "                else ''\n"
            + "         end) as CRCA_TYPE,t.payment_status\n"
            + ",s.recon_dt\n"
            + "from recsys.rctb_trip_intf t\n"
            + "left join recsys.rctb_setl s on t.job_no = s.job_no and s.recon_flag = 'R'\n"
            + "where\n"
            + "1=1\n"
            + "--and status='N'\n"
            + "and t.completed_dt>to_date('20241112 08:00:00','yyyymmdd hh24:mi:ss')\n"
            + "and t.completed_dt<to_date('20241112 09:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and patch_code='LMBO'\n"
            + "--and t.create_date>to_date(to_char(sysdate-1,'yyyymmdd')||' 00:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and t.create_date<to_date(to_char(sysdate-1,'yyyymmdd')||' 22:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and t.create_date>sysdate-7\n"
            + "--and t.create_date>to_date('20240201 00:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and t.completed_dt<to_date(to_char(sysdate-1,'yyyymmdd')||' 22:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and to_char(t.completed_dt,'dd')='24'\n"
            + "--and error_code is not null\n"
            + "--and error_code='NSTL'\n"
            + "and t.payment_mode in ('CABC','CRCA','DBSPOF','NOF',\n"
            + "'PLAH5','KRISH5')\n"
            + "--and t.payment_mode in ('CRCA')\n"
            + "--and t.payment_mode not in ('DBSPOF','NOF')\n"
            + "and t.approval_code is null\n"
            + "and t.job_status='COMPLETED'\n"
            + "--AND t.JOB_NO LIKE '7%'\n"
            + "--and patch_code is null\n"
            + "--and not exists (select * from recsys.rctb_setl s where s.job_no=t.job_no) and not (payment_mode in ('CABC', 'EVCH') and status = 'R')\n"
            + "AND t.job_no not IN ('5600019018','5600019019','5600019136','5600019140','5600019178','5600019230','5600019238','5600019330','5600019379')\n"
            + "AND t.job_no not IN ('null')\n"
            + "AND t.job_no not IN ('null')\n"
            + "ORDER BY t.COMPLETED_DT\n"
            + ")\n"
            + "group by  \n"
            + "to_char(completed_dt, 'YYYY'),to_char(completed_dt, 'MM'),\n"
            + "to_char(completed_dt,'dd')\n"
            + "--,trunc(t.completed_dt)\n"
            + ",payment_mode,crca_type--,payment_status\n"
            + "--,c_dt\n"
            + "order by to_char(completed_dt, 'YYYY'),to_char(completed_dt, 'MM'),\n"
            + "to_char(completed_dt,'dd')\n"
            + "--,trunc(t.completed_dt)\n"
            + ",payment_mode,crca_type\n"
            + ") ppt",
    resultSetMapping = "PotentialNSTLMapping")
@NamedNativeQuery(
    name = "findPotentialNSTLJobByTimestamp",
    query =
        "SELECT ROW_NUMBER() OVER (ORDER BY 1)  AS id, ppt.* FROM ("
            + "select  --job_no,t.payment_mode,\n"
            + "--trunc(t.create_date)\n"
            + "to_char(completed_dt, 'YYYY')\"year\",to_char(completed_dt, 'MM')\"month\",\n"
            + "to_char(completed_dt,'dd') \"date\"\n"
            + "--trunc(t.completed_dt)\n"
            + ",payment_mode as payment_mode, crca_type as crca_type\n"
            + ",sum(txn_amount)amount--,payment_status\n"
            + ",count(*) cnt,sum(case when patch_code='LMBO' then 1 else 0 end) limbo_trip\n"
            + "--,sum(case when paid_job_no = 'Y' then 1 else 0 end)total_paid_job_no\n"
            + "--,c_dt\n"
            + "from (\n"
            + "select t.job_no,t.completed_dt, t.payment_mode, t.txn_amount\n"
            + ",(case when s.job_no is not null then 'Y' else 'N' end) paid_job_no\n"
            + ",patch_code,to_char(t.create_date, 'yyyymmdd')c_dt\n"
            + ",t.approval_code,t.job_status\n"
            + "         ,(case\n"
            + "                when t.payment_mode = 'CRCA' and t.approval_code is null then 'COF'\n"
            + "                when t.payment_mode = 'CRCA' and t.approval_code is not null then 'ON_BOARD'\n"
            + "                else ''\n"
            + "         end) as CRCA_TYPE,t.payment_status\n"
            + ",s.recon_dt\n"
            + "from recsys.rctb_trip_intf t\n"
            + "left join recsys.rctb_setl s on t.job_no = s.job_no and s.recon_flag = 'R'\n"
            + "where\n"
            + "1=1\n"
            + "--and status='N'\n"
            +
            //                        "and t.completed_dt>to_date(:startDT,'yyyymmdd hh24:mi:ss')\n"
            // +
            //                        "and t.completed_dt<to_date(:endDT,'yyyymmdd hh24:mi:ss')\n" +

            "and t.completed_dt>to_date(:startDT,'yyyymmdd hh24:mi:ss')\n"
            + "and t.completed_dt<to_date(:endDT,'yyyymmdd hh24:mi:ss')\n"
            + "--and patch_code='LMBO'\n"
            + "--and t.create_date>to_date(to_char(sysdate-1,'yyyymmdd')||' 00:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and t.create_date<to_date(to_char(sysdate-1,'yyyymmdd')||' 22:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and t.create_date>sysdate-7\n"
            + "--and t.create_date>to_date('20240201 00:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and t.completed_dt<to_date(to_char(sysdate-1,'yyyymmdd')||' 22:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and to_char(t.completed_dt,'dd')='24'\n"
            + "--and error_code is not null\n"
            + "--and error_code='NSTL'\n"
            + "and t.payment_mode in ('CABC','CRCA','DBSPOF','NOF',\n"
            + "'PLAH5','KRISH5')\n"
            + "--and t.payment_mode in ('CRCA')\n"
            + "--and t.payment_mode not in ('DBSPOF','NOF')\n"
            + "--and t.approval_code is null\n"
            + "and t.job_status='COMPLETED'\n"
            + "AND t.JOB_NO LIKE '7%'\n"
            + "--and patch_code is null\n"
            + "--and not exists (select * from recsys.rctb_setl s where s.job_no=t.job_no) and not (payment_mode in ('CABC', 'EVCH') and status = 'R')\n"
            + "AND ( t.job_no not IN (:jobNoList1 )\n"
            + "AND t.job_no not IN (:jobNoList2 )\n"
            + "AND t.job_no not IN (:jobNoList3 )\n"
            + "AND t.job_no not IN (:jobNoList4 )\n"
            + "AND t.job_no not IN (:jobNoList5 )\n"
            + "AND t.job_no not IN (:jobNoList6 )\n"
            + "AND t.job_no not IN (:jobNoList7 )\n"
            + "AND t.job_no not IN (:jobNoList8 )\n"
            + "AND t.job_no not IN (:jobNoList9 )\n"
            + "AND t.job_no not IN (:jobNoList10 )  \n"
            + "AND t.job_no not IN (:jobNoList11 )  \n"
            + "AND t.job_no not IN (:jobNoList12 )  \n"
            + "AND t.job_no not IN (:jobNoList13 )  \n"
            + "AND t.job_no not IN (:jobNoList14 )  \n"
            + "AND t.job_no not IN (:jobNoList15 )  \n"
            + "AND t.job_no not IN (:jobNoList16 )  \n"
            + "AND t.job_no not IN (:jobNoList17 )  \n"
            + "AND t.job_no not IN (:jobNoList18 )  \n"
            + "AND t.job_no not IN (:jobNoList19 )  \n"
            + "AND t.job_no not IN (:jobNoList20 )  \n"
            + "AND t.job_no not IN (:jobNoList21 )  \n"
            + "AND t.job_no not IN (:jobNoList22 )  \n"
            + "AND t.job_no not IN (:jobNoList23)   \n"
            + "AND t.job_no not IN (:jobNoList24 )  \n"
            + "AND t.job_no not IN (:jobNoList25 )  \n"
            + "AND t.job_no not IN (:jobNoList26 )  \n"
            + "AND t.job_no not IN (:jobNoList27 )  \n"
            + "AND t.job_no not IN (:jobNoList28 )  \n"
            + "AND t.job_no not IN (:jobNoList29 )  \n"
            + "AND t.job_no not IN (:jobNoList30 ) ) \n"
            + "ORDER BY t.COMPLETED_DT\n"
            + ")\n"
            + "group by to_char(completed_dt, 'YYYY'),to_char(completed_dt, 'MM'),\n"
            + "to_char(completed_dt,'dd')\n"
            + "--,trunc(t.completed_dt)\n"
            + ",payment_mode,crca_type--,payment_status\n"
            + "--,c_dt\n"
            + "order by to_char(completed_dt, 'YYYY'),to_char(completed_dt, 'MM'),\n"
            + "to_char(completed_dt,'dd')\n"
            + "--,trunc(t.completed_dt)\n"
            + ",payment_mode,crca_type \n"
            + " ) ppt",
    resultSetMapping = "PotentialNSTLMapping")
@SqlResultSetMapping(
    name = "PotentialNSTLMapping",
    classes =
        @ConstructorResult(
            targetClass = PotentialNSTLEntity.class,
            columns = {
              @ColumnResult(name = "year", type = String.class),
              @ColumnResult(name = "month", type = String.class),
              @ColumnResult(name = "date", type = String.class),
              @ColumnResult(name = "payment_mode", type = String.class),
              @ColumnResult(name = "crca_type", type = String.class),
              @ColumnResult(name = "amount", type = Double.class),
              @ColumnResult(name = "cnt", type = Integer.class),
              @ColumnResult(name = "limbo_trip", type = Integer.class)
            }))
public class PotentialNSTLEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", nullable = false)
  private Long id;

  @Column(name = "year")
  private String year;

  @Column(name = "month")
  private String month;

  @Column(name = "date")
  private String date;

  @Column(name = "payment_mode")
  private String paymentMode;

  @Column(name = "crca_type")
  private String crcaType;

  @Column(name = "amount")
  private Double amount;

  @Column(name = "cnt")
  private Integer cnt;

  @Column(name = "limbo_trip")
  private Integer limboTrip;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public PotentialNSTLEntity() {}

  // Getters and Setters
  public String getYear() {
    return year;
  }

  public void setYear(String year) {
    this.year = year;
  }

  public String getMonth() {
    return month;
  }

  public void setMonth(String month) {
    this.month = month;
  }

  public String getDate() {
    return date;
  }

  public void setDate(String date) {
    this.date = date;
  }

  public String getPaymentMode() {
    return paymentMode;
  }

  public void setPaymentMode(String paymentMode) {
    this.paymentMode = paymentMode;
  }

  public String getCrcaType() {
    return crcaType;
  }

  public void setCrcaType(String crcaType) {
    this.crcaType = crcaType;
  }

  public Double getAmount() {
    return amount;
  }

  public void setAmount(Double amount) {
    this.amount = amount;
  }

  public Integer getCnt() {
    return cnt;
  }

  public void setCnt(Integer cnt) {
    this.cnt = cnt;
  }

  public Integer getLimboTrip() {
    return limboTrip;
  }

  public void setLimboTrip(Integer limboTrip) {
    this.limboTrip = limboTrip;
  }

  // Override constructor
  public PotentialNSTLEntity(
      String year,
      String month,
      String date,
      String paymentMode,
      String crcaType,
      Double amount,
      Integer cnt,
      Integer limboTrip) {
    this.year = year;
    this.month = month;
    this.date = date;
    this.paymentMode = paymentMode;
    this.crcaType = crcaType;
    this.amount = amount;
    this.cnt = cnt;
    this.limboTrip = limboTrip;
  }
}
