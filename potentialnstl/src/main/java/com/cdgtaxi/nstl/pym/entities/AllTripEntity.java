package com.cdgtaxi.nstl.pym.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@NamedNativeQuery(
    name = "findAllTripByStartEndTime",
    query =
        "\n"
            + "select t.job_no,t.completed_dt, t.payment_mode, t.txn_amount\n"
            + ",(case when s.job_no is not null then 'Y' else 'N' end) paid_job_no\n"
            + ",patch_code,to_char(t.create_date, 'yyyymmdd')c_dt\n"
            + ",t.approval_code,t.job_status\n"
            + "         ,(case\n"
            + "                when t.payment_mode = 'CRCA' and t.approval_code is null then 'COF'\n"
            + "                when t.payment_mode = 'CRCA' and t.approval_code is not null then 'ON_BOARD'\n"
            + "                else ''\n"
            + "         end) as CRCA_TYPE,t.payment_status\n"
            + ",s.recon_dt\n"
            + ",t.CARD_NO \n"
            + "from recsys.rctb_trip_intf t\n"
            + "left join recsys.rctb_setl s on t.job_no = s.job_no and s.recon_flag = 'R'\n"
            + "where\n"
            + "1=1\t\n"
            + "--and status='N'\n"
            + "and t.completed_dt>to_date(:startDT,'yyyymmdd hh24:mi:ss')\n"
            + "--and t.completed_dt>to_date('20241112 08:00:00','yyyymmdd hh24:mi:ss')\n"
            + "and t.completed_dt<to_date(:endDT,'yyyymmdd hh24:mi:ss')\n"
            + "--and patch_code='LMBO'\n"
            + "--and t.create_date>to_date(to_char(sysdate-1,'yyyymmdd')||' 00:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and t.create_date<to_date(to_char(sysdate-1,'yyyymmdd')||' 22:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and t.create_date>sysdate-7\n"
            + "--and t.create_date>to_date('20240201 00:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and t.completed_dt<to_date(to_char(sysdate-1,'yyyymmdd')||' 22:00:00','yyyymmdd hh24:mi:ss')\n"
            + "--and to_char(t.completed_dt,'dd')='24'\n"
            + "--and error_code is not null\n"
            + "--and error_code='NSTL'\n"
            + "and t.payment_mode in ('CABC','CRCA','DBSPOF','NOF','PLAH5','KRISH5')\n"
            + "--and t.payment_mode in ('CRCA')\n"
            + "--and t.payment_mode not in ('DBSPOF','NOF')\n"
            + "--and t.approval_code is null\n"
            + "and t.job_status='COMPLETED'\n"
            + "AND t.JOB_NO NOT LIKE '7%'\n"
            + "--AND t.JOB_NO NOT LIKE '52%'\n"
            + "--and patch_code is null\n"
            + "--and not exists (select * from recsys.rctb_setl s where s.job_no=t.job_no) and not (payment_mode in ('CABC', 'EVCH') and status = 'R')\n"
            + "ORDER BY t.COMPLETED_DT\n",
    resultClass = AllTripEntity.class)
@Getter
@Setter
@Entity
@Table(name = "job_details")
public class AllTripEntity {

  @Id
  @Column(name = "job_no", nullable = false)
  private String jobNo;

  @Column(name = "completed_dt")
  private String completedDt;

  @Column(name = "payment_mode")
  private String paymentMode;

  @Column(name = "txn_amount")
  private Double txnAmount;

  @Column(name = "paid_job_no")
  private String paidJobNo;

  @Column(name = "patch_code")
  private String patchCode;

  @Column(name = "c_dt")
  private String cDt;

  @Column(name = "approval_code")
  private String approvalCode;

  @Column(name = "job_status")
  private String jobStatus;

  @Column(name = "crca_type")
  private String crcaType;

  @Column(name = "payment_status")
  private String paymentStatus;

  @Column(name = "recon_dt")
  private String reconDt;

  // Getters and Setters

  public String getJobNo() {
    return jobNo;
  }

  public void setJobNo(String jobNo) {
    this.jobNo = jobNo;
  }

  public String getCompletedDt() {
    return completedDt;
  }

  public void setCompletedDt(String completedDt) {
    this.completedDt = completedDt;
  }

  public String getPaymentMode() {
    return paymentMode;
  }

  public void setPaymentMode(String paymentMode) {
    this.paymentMode = paymentMode;
  }

  public Double getTxnAmount() {
    return txnAmount;
  }

  public void setTxnAmount(Double txnAmount) {
    this.txnAmount = txnAmount;
  }

  public String getPaidJobNo() {
    return paidJobNo;
  }

  public void setPaidJobNo(String paidJobNo) {
    this.paidJobNo = paidJobNo;
  }

  public String getPatchCode() {
    return patchCode;
  }

  public void setPatchCode(String patchCode) {
    this.patchCode = patchCode;
  }

  public String getCDt() {
    return cDt;
  }

  public void setCDt(String cDt) {
    this.cDt = cDt;
  }

  public String getApprovalCode() {
    return approvalCode;
  }

  public void setApprovalCode(String approvalCode) {
    this.approvalCode = approvalCode;
  }

  public String getJobStatus() {
    return jobStatus;
  }

  public void setJobStatus(String jobStatus) {
    this.jobStatus = jobStatus;
  }

  public String getCrcaType() {
    return crcaType;
  }

  public void setCrcaType(String crcaType) {
    this.crcaType = crcaType;
  }

  public String getPaymentStatus() {
    return paymentStatus;
  }

  public void setPaymentStatus(String paymentStatus) {
    this.paymentStatus = paymentStatus;
  }

  public String getReconDt() {
    return reconDt;
  }

  public void setReconDt(String reconDt) {
    this.reconDt = reconDt;
  }

  @Override
  public String toString() {
    return "JobEntity{"
        + "jobNo='"
        + jobNo
        + '\''
        + ", completedDt='"
        + completedDt
        + '\''
        + ", paymentMode='"
        + paymentMode
        + '\''
        + ", txnAmount="
        + txnAmount
        + ", paidJobNo='"
        + paidJobNo
        + '\''
        + ", patchCode='"
        + patchCode
        + '\''
        + ", cDt='"
        + cDt
        + '\''
        + ", approvalCode='"
        + approvalCode
        + '\''
        + ", jobStatus='"
        + jobStatus
        + '\''
        + ", crcaType='"
        + crcaType
        + '\''
        + ", paymentStatus='"
        + paymentStatus
        + '\''
        + ", reconDt='"
        + reconDt
        + '\''
        + '}';
  }
}
