package com.cdgtaxi.nstl.pym.repositories;

import com.cdgtaxi.nstl.pym.entities.AllTripEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TripDetialRepository extends JpaRepository<AllTripEntity, Long> {

  @Query(nativeQuery = true, name = "findAllTripByStartEndTime")
  List<AllTripEntity> findAllTripByStartEndTime(
      @Param("startDT") String startDT, @Param("endDT") String endDT);
}
