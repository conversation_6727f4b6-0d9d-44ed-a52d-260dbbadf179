package com.cdgtaxi.nstl.pym.repositories;

import com.cdgtaxi.nstl.pym.entities.PotentialNSTLEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PotentialJOBDetialRepository extends JpaRepository<PotentialNSTLEntity, String> {

  @Query(nativeQuery = true, name = "findPotentialNSTLJobByTimestamp")
  List<PotentialNSTLEntity> findPotentialNSTLJobByTimestamp(
      @Param("startDT") String startDT,
      @Param("endDT") String endDT,
      @Param("jobNoList1") List<String> jobNoList1,
      @Param("jobNoList2") List<String> jobNoList2,
      @Param("jobNoList3") List<String> jobNoList3,
      @Param("jobNoList4") List<String> jobNoList4,
      @Param("jobNoList5") List<String> jobNoList5,
      @Param("jobNoList6") List<String> jobNoList6,
      @Param("jobNoList7") List<String> jobNoList7,
      @Param("jobNoList8") List<String> jobNoList8,
      @Param("jobNoList9") List<String> jobNoList9,
      @Param("jobNoList10") List<String> jobNoList10,
      @Param("jobNoList11") List<String> jobNoList11,
      @Param("jobNoList12") List<String> jobNoList12,
      @Param("jobNoList13") List<String> jobNoList13,
      @Param("jobNoList14") List<String> jobNoList14,
      @Param("jobNoList15") List<String> jobNoList15,
      @Param("jobNoList16") List<String> jobNoList16,
      @Param("jobNoList17") List<String> jobNoList17,
      @Param("jobNoList18") List<String> jobNoList18,
      @Param("jobNoList19") List<String> jobNoList19,
      @Param("jobNoList20") List<String> jobNoList20,
      @Param("jobNoList21") List<String> jobNoList21,
      @Param("jobNoList22") List<String> jobNoList22,
      @Param("jobNoList23") List<String> jobNoList23,
      @Param("jobNoList24") List<String> jobNoList24,
      @Param("jobNoList25") List<String> jobNoList25,
      @Param("jobNoList26") List<String> jobNoList26,
      @Param("jobNoList27") List<String> jobNoList27,
      @Param("jobNoList28") List<String> jobNoList28,
      @Param("jobNoList29") List<String> jobNoList29,
      @Param("jobNoList30") List<String> jobNoList30);

  @Query(nativeQuery = true, name = "findPotentialNSTLTest")
  List<PotentialNSTLEntity> findPotentialNSTLTest();
}
