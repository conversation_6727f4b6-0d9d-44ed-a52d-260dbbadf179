plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.0'
    id 'io.spring.dependency-management' version '1.1.6'
    id 'com.diffplug.spotless' version '6.23.0'
}

group = 'com.cdgtaxi'
version = '0.0.1-SNAPSHOT'
ext {
    commonscodecVersion = '1.14'
}
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

spotless {
    kotlinGradle {
        targetExclude("**/*.gradle.kts")
    }

    java {
        targetExclude("**/generated/**/*.java")
        removeUnusedImports()
        importOrder()
        googleJavaFormat()
        endWithNewline()
        indentWithSpaces(2)
        replace("SonarQube Comments", "// NOSONAR", "//NOSONAR")
    }
}
tasks.build { dependsOn("spotlessApply") }

dependencies {
    // MyBatis Plus dependencies
    implementation 'com.baomidou:mybatis-plus-boot-starter:3.5.5'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation("org.springframework.boot:spring-boot-starter-quartz")
    implementation("org.springframework.boot:spring-boot-starter-cache")

    implementation("com.github.ulisesbocchio:jasypt-spring-boot-starter:2.1.2")


    implementation("org.slf4j:slf4j-api:2.0.7")
    implementation("ch.qos.logback:logback-classic:1.5.6")
    implementation("ch.qos.logback:logback-core:1.5.6")

    //spring-boot-starter-thymeleaf
    implementation('org.springframework.boot:spring-boot-starter-thymeleaf')

    implementation("com.opencsv:opencsv:5.9") {
        exclude group : "commons-collections", module: "commons-collections"
    }
    implementation("org.apache.commons:commons-collections4:4.4")
    implementation("org.apache.commons:commons-lang3:3.12.0")
    implementation("org.apache.httpcomponents:httpclient:4.5.13")
    implementation("commons-codec:commons-codec:$commonscodecVersion")

    implementation 'org.apache.commons:commons-csv:1.11.0'
    implementation 'org.apache.poi:poi:5.2.5'
    implementation 'org.apache.poi:poi-ooxml:5.2.5'
    implementation 'org.ehcache:ehcache:3.9.11'
    implementation 'commons-io:commons-io:2.16.1'

    //commons-csv
    implementation("org.apache.commons:commons-csv:1.11.0")

    //easyexcel-core
    implementation("com.alibaba:easyexcel:4.0.3")
    implementation("com.alibaba.fastjson2:fastjson2:2.0.51")

    implementation("com.zaxxer:HikariCP:5.0.1")
    implementation("org.springframework.boot:spring-boot-starter-actuator")

    compileOnly 'org.projectlombok:lombok'
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.1.0")
//    developmentOnly('org.springframework.boot:spring-boot-devtools'){
//        transitive = false
//    }

    runtimeOnly 'com.oracle.database.jdbc:ojdbc11'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    testImplementation("org.mockito:mockito-core:4.6.1")
}

tasks.named('test') {
    useJUnitPlatform()
}
