package com.cdgtaxi.recon.inbound.models;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.cdgtaxi.recon.inbound.entities.GojekReport;
import com.cdgtaxi.recon.inbound.entities.RCTBTripEntity;
import java.math.BigDecimal;
import java.util.Optional;
import lombok.Data;

@Data
@ContentRowHeight(20)
@HeadRowHeight(30)
@ColumnWidth(20)
public class ReportGojekCdgTripDTO {
  @ColumnWidth(10)
  private Long id;

  @ExcelProperty({"MDT Amount"})
  private String mdtAmount;

  @ExcelProperty({"Base Fare"})
  private String baseFare;

  @ExcelProperty({"Driver Earning"})
  private String driverEarning;

  @ExcelProperty({"CDG Cut"})
  private String cdgCut;

  @ExcelProperty({"Gojek Cut"})
  private String gojekCut;

  @ColumnWidth(10)
  @ExcelProperty({"Toll"})
  private String toll;

  @ExcelProperty({"Flash Incentive"})
  private String flashIncentive;

  @ExcelProperty({"Gojek Trip ID"})
  private String gojekTripId;

  @ColumnWidth(25)
  @ExcelProperty({"CDG Trip ID"})
  private String cdgTripId;

  @ExcelProperty({"Trip Date"})
  private String tripDate;

  @ExcelProperty({"CDG Vehicle No"})
  private String cdgVhno;

  @ExcelProperty({"CDG Driver ID"})
  private String cdgDriverId;

  @ExcelProperty({"Payment to CDG"})
  private String paymentToCdg;

  @ColumnWidth(25)
  @ExcelProperty({"Booking Reference"})
  private String bookingReference;

  @ExcelProperty({"Job No"})
  private String jobNo;

  @ExcelProperty({"Vehicle No"})
  private String vehicleNo;

  @ExcelProperty({"Job Status"})
  private String jobStatus;

  @ExcelProperty({"Entity"})
  private String entity;

  @ExcelProperty({"Booked Channel"})
  private String bookedChannel;

  @ExcelProperty({"CDG Trip Date"})
  private String completedDt;

  @ExcelProperty({"Payment Mode"})
  private String paymentMode;

  @ExcelProperty({"Total Amount"})
  private String totalAmount;

  @ExcelProperty({"Driver Levy"})
  private String driverLevy;

  @ExcelProperty({"Refund"})
  private String refund;

  @ExcelProperty({"Calc Driver Refund"})
  private String calcDriverRefund;

  @ExcelProperty({"DCP Request ID"})
  private String dcpRequestId;

  @ExcelProperty({"Difference"})
  private String difference;

  @ExcelProperty({"Remarks All"})
  private String remarksAll;

  public ReportGojekCdgTripDTO() {}

  public void setValuesByGojekReport(GojekReport gojekReport) {
    this.id = gojekReport.getId();
    this.mdtAmount = gojekReport.getMdtAmount();
    this.baseFare = gojekReport.getBaseFare();
    this.driverEarning = gojekReport.getDriverEarning();
    this.cdgCut = gojekReport.getCdgCut();
    this.gojekCut = gojekReport.getGojekCut();
    this.toll = gojekReport.getToll();
    this.flashIncentive = gojekReport.getFlashIncentive();
    this.gojekTripId = gojekReport.getGojekTripId();
    this.cdgTripId = gojekReport.getCdgTripId();
    this.tripDate = gojekReport.getTripDate();
    this.cdgVhno = gojekReport.getCdgVhno();
    this.cdgDriverId = gojekReport.getCdgDriverId();
    this.paymentToCdg = gojekReport.getPaymentToCdg();
  }

  public void setValuesByCdgTrip(Optional<RCTBTripEntity> cdgTripEntityOptional) {
    if (cdgTripEntityOptional.isPresent()) {
      RCTBTripEntity cdgTripEntity = (RCTBTripEntity) cdgTripEntityOptional.get();
      this.bookingReference = cdgTripEntity.getBookingReference();
      this.jobNo = cdgTripEntity.getJobNo();
      this.vehicleNo = cdgTripEntity.getVehicleNo();
      this.jobStatus = cdgTripEntity.getJobStatus();
      this.entity = cdgTripEntity.getEntity();
      this.bookedChannel = cdgTripEntity.getBookedChannel();
      this.completedDt = cdgTripEntity.getCompletedDt();
      this.paymentMode = cdgTripEntity.getPaymentMode();
      this.totalAmount = cdgTripEntity.getTotalAmount();
      this.driverLevy = cdgTripEntity.getDriverLevy();
      this.refund = cdgTripEntity.getRefund();
      this.calcDriverRefund = cdgTripEntity.getCalcDriverRefund();
      this.dcpRequestId = cdgTripEntity.getDcpRequestId();
      if (this.paymentToCdg == null) {
        this.difference = "N/A";
        this.remarksAll = "Not in Gojek List";
      } else {
        BigDecimal paymentToCdg = new BigDecimal(this.paymentToCdg);
        this.difference =
            this.totalAmount != null
                ? paymentToCdg.subtract(new BigDecimal(this.totalAmount)).toString()
                : "N/A";
        this.remarksAll = "Booking ID Tally";
        this.remarksAll =
            this.paymentMode.equalsIgnoreCase("CASH") ? "Cash Payment" : this.remarksAll;
      }
    } else {
      this.remarksAll = "Not in CDG list";
    }
  }
}
