package com.cdgtaxi.recon.inbound.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@TableName("rctb_trip_intf")
@Getter
@Setter
@ToString
public class RCTBTripEntity {
  @TableField("booking_reference")
  private String bookingReference;

  @TableId("job_no")
  private String jobNo;

  @TableField("vehicle_no")
  private String vehicleNo;

  @TableField("job_status")
  private String jobStatus;

  @TableField("entity")
  private String entity;

  @TableField("booked_channel")
  private String bookedChannel;

  @TableField("completed_dt")
  private String completedDt;

  @TableField("payment_mode")
  private String paymentMode;

  @TableField("total_amount")
  private String totalAmount;

  @TableField("driver_levy")
  private String driverLevy;

  @TableField("refund")
  private String refund;

  @TableField("calc_driver_refund")
  private String calcDriverRefund;

  @TableField("dcp_request_id")
  private String dcpRequestId;

  @TableField("difference")
  private String difference;

  @TableField("remarks_all")
  private String remarksAll;

  public boolean equals(Object o) {
    if (this.bookingReference != null && o instanceof GojekReport gojekReport) {
      return this.bookingReference.equals(gojekReport.getGojekTripId());
    } else if (this.dcpRequestId != null && o instanceof GojekReport gojekReport) {
      return this.dcpRequestId.contains(gojekReport.getCdgTripId());
    } else {
      return super.equals(o);
    }
  }
}
