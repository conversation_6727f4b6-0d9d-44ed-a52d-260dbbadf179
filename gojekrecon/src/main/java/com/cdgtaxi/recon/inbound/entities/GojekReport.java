package com.cdgtaxi.recon.inbound.entities;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@TableName("gojek_report")
@Getter
@Setter
@ToString
public class GojekReport {
  @TableId(type = IdType.AUTO)
  private Long id;

  @TableField("mdt_amount")
  private String mdtAmount;

  @TableField("base_fare")
  private String baseFare;

  @TableField("driver_earning")
  private String driverEarning;

  @TableField("cdg_cut")
  private String cdgCut;

  @TableField("gojek_cut")
  private String gojekCut;

  @TableField("toll")
  private String toll;

  @TableField("flash_incentive")
  private String flashIncentive;

  @TableField("gojek_trip_id")
  private String gojekTripId;

  @TableField("cdg_trip_id")
  private String cdgTripId;

  @TableField("trip_date")
  private String tripDate;

  @TableField("cdg_vhno")
  private String cdgVhno;

  @TableField("cdg_driver_id")
  private String cdgDriverId;

  @TableField("payment_to_cdg")
  private String paymentToCdg;
}
