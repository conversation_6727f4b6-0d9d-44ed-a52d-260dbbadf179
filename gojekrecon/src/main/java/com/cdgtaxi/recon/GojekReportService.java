package com.cdgtaxi.recon;

import com.cdgtaxi.recon.inbound.entities.GojekReport;
import com.cdgtaxi.recon.inbound.entities.RCTBTripEntity;
import com.cdgtaxi.recon.inbound.models.ReportGojekCdgTripDTO;
import com.cdgtaxi.recon.inbound.models.WeeklySummaryDTO;
import com.cdgtaxi.recon.inbound.mappers.AllTripEntityMapper;
import com.cdgtaxi.recon.utils.CsvService;
import com.cdgtaxi.recon.utils.ExcelService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class GojekReportService {

  private final AllTripEntityMapper cdgTripEntityMapper;
  private final CsvService csvService;
  private final ExcelService excelService;

  public List<GojekReport> loadGojekFile(String filePath) {
    return this.importCsvToDatabase(filePath);
  }

  public List<GojekReport> importCsvToDatabase(String filePath) {
    log.info("Importing CSV file: " + filePath);
    return this.csvService.importCsvToDatabase(filePath);
  }

  public String handleGojekRecon(String absolutePath, String downloadDirPath) {
    LocalDate gojekStartDate = null;
    LocalDate gojekEndDate = null;

    List<GojekReport> gojekReportList = new ArrayList<>();
    if (absolutePath.contains(".csv")) {
      log.info("Step1: Load Gojek CSV file: " + absolutePath);
      gojekReportList = this.loadGojekFile(absolutePath);
    } else {
      log.info("Step1: Load Gojek Excel file: " + absolutePath);
      gojekReportList = this.loadGojekExcelFile(absolutePath);
    }

    log.info("GojekReport Size: " + gojekReportList.size());
    if (gojekReportList.isEmpty()) {
      return null;
    }

    gojekReportList.stream().limit(10L).forEach(System.out::println);

    log.info("Step2:  Get min/max trip date from GojekReport tripDate");
    Optional<String> minTripDateOptional =
        gojekReportList.stream().map(GojekReport::getTripDate).min(String::compareTo);
    Optional<String> maxTripDateOptional =
        gojekReportList.stream().map(GojekReport::getTripDate).max(String::compareTo);
    minTripDateOptional.ifPresent(
        (minTripDatex) -> {
          log.info("Min trip date: " + minTripDatex);
        });
    maxTripDateOptional.ifPresent(
        (maxTripDatex) -> {
          log.info("Max trip date: " + maxTripDatex);
        });
    String minTripDateGojek = minTripDateOptional.orElse("");
    String maxTripDateGojek = maxTripDateOptional.orElse("");
    DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss");
    String minTripDate = "";
    String maxTripDate = "";
    String minPreviousWeeksTripDate = ""; // check for previous two weeks for now
    String maxPreviousWeeksTripDate = "";

    DateTimeFormatter[] formatters =
        new DateTimeFormatter[] {
          DateTimeFormatter.ofPattern("yyyy-MM-dd"), DateTimeFormatter.ofPattern("d/M/yyyy")
        };

    for (DateTimeFormatter formatter : formatters) {
      try {
        gojekStartDate = LocalDate.parse(minTripDateGojek, formatter);
        break;
      } catch (Exception e) {
      }
    }

    for (DateTimeFormatter formatter : formatters) {
      try {
        gojekEndDate = LocalDate.parse(maxTripDateGojek, formatter);
        break;
      } catch (Exception e) {
      }
    }

    try {
      minTripDate = gojekStartDate.atStartOfDay().format(outputFormat);
      minPreviousWeeksTripDate = gojekStartDate.minusDays(14).atStartOfDay().format(outputFormat);

      maxTripDate = gojekEndDate.plusDays(1).atStartOfDay().format(outputFormat);
      maxPreviousWeeksTripDate = gojekEndDate.plusDays(2).atStartOfDay().format(outputFormat);
    } catch (Exception e) {
      log.error("Error parsing date: " + e.getMessage());
    }

    log.info("Gojek Start Date: {}", minTripDate);
    log.info("Gojek End Date: {}", maxTripDate);
    log.info("Gojek Previous Week Start Date: {}", minPreviousWeeksTripDate);
    log.info("Gojek Previous Week End Date: {}", maxPreviousWeeksTripDate);

    log.info("Check All Trip in trip date: from {} to {} ", minTripDate, maxTripDate);
    log.info("Step3: Query CDG DB for all Gojek job for the week");
    List<RCTBTripEntity> cdgAllTripList =
        this.cdgTripEntityMapper.findAllTripByStartEndTime(minTripDate, maxTripDate);
    log.info("All CdgTripEntity Size:{}", cdgAllTripList.size());
    cdgAllTripList.stream().limit(10L).forEach(System.out::println);

    log.info("Step4: Add all GojekReport to ReportGojekCdgTripDTO");
    List<ReportGojekCdgTripDTO> reportGojekCdgTripList = new ArrayList<>();
    List<String> gojekTripIdNotInCdgTripIds = new ArrayList<>();

    //    gojekReportList.parallelStream()
    //        .forEach(
    //            gojekReport -> {
    //              String gojekTripId = gojekReport.getGojekTripId();
    //              String cdgTripId = gojekReport.getCdgTripId();
    //
    //              Optional<RCTBTripEntity> cdgTripEntityOptional =
    //                  findCdgTripEntityByGojekTripId(gojekTripId, cdgTripId, cdgAllTripList);
    //
    //              cdgTripEntityOptional.ifPresentOrElse(
    //                  cdgTripEntity ->
    //                      reportGojekCdgTripList.add(
    //                          combineTrips(gojekReport, Optional.of(cdgTripEntity))),
    //                  () -> gojekTripIdNotInCdgTripIds.add('O' + cdgTripId));
    //            });

    gojekReportList.forEach(
        gojekReport -> {
          String gojekTripId = gojekReport.getGojekTripId();
          String cdgTripId = gojekReport.getCdgTripId();

          Optional<RCTBTripEntity> cdgTripEntityOptional =
              findCdgTripEntityByGojekTripId(gojekTripId, cdgTripId, cdgAllTripList);

          cdgTripEntityOptional.ifPresentOrElse(
              cdgTripEntity ->
                  reportGojekCdgTripList.add(combineTrips(gojekReport, Optional.of(cdgTripEntity))),
              () -> gojekTripIdNotInCdgTripIds.add('O' + cdgTripId));
        });

    log.info("Gojek Trip Id Not In Cdg Trip Ids Size:{}", gojekTripIdNotInCdgTripIds.size());
    gojekTripIdNotInCdgTripIds.stream().limit(10).forEach(System.out::println);

    // continue to get previous week trips in cdg list
    // Split gojekTripIdNotInCdgTripIds to each 1000 records
    List<List<String>> gojekTripIdNotInCdgTripIdsList = new ArrayList<>();
    for (int i = 0; i < gojekTripIdNotInCdgTripIds.size(); i += 1000) {
      gojekTripIdNotInCdgTripIdsList.add(
          gojekTripIdNotInCdgTripIds.subList(
              i, Math.min(i + 1000, gojekTripIdNotInCdgTripIds.size())));
    }
    for (List<String> gojekTripIdNotInCdgTripIdsSubList : gojekTripIdNotInCdgTripIdsList) {
      List<RCTBTripEntity> cdgAllTripListPreviousWeek =
          this.cdgTripEntityMapper.findAllTripByCdgTripIdsInPreviousWeeks(
              gojekTripIdNotInCdgTripIdsSubList,
              minPreviousWeeksTripDate,
              maxPreviousWeeksTripDate);
      log.info("All cdgAllTripListPreviousWeek Size:{}", cdgAllTripListPreviousWeek.size());
      cdgAllTripListPreviousWeek.stream().limit(10L).forEach(System.out::println);

      reportGojekCdgTripList.addAll(
          this.combineTrips(
              gojekTripIdNotInCdgTripIds, gojekReportList, cdgAllTripListPreviousWeek));
    }

    log.info("reportGojekCdgTripList Size: {}", reportGojekCdgTripList.size());
    reportGojekCdgTripList.stream().limit(10L).forEach(System.out::println);

    List<String> gojekBookingRefs =
        reportGojekCdgTripList.stream()
            .map(ReportGojekCdgTripDTO::getGojekTripId)
            .collect(Collectors.toList());

    log.info("Step5: Add remaining cdg trip to reportGojekCdgTripList");
    List<RCTBTripEntity> copiedCdgAlllTripList = new ArrayList<>(cdgAllTripList);

    log.info("Copied cdg trip for remove duplicate");
    //        copiedCdgAlllTripList.removeAll(gojekReportList);

    // Convert gojekReportList to a HashSet for fast lookup
    Set<String> gojekReportListSet =
        gojekReportList.stream().map(g -> 'O' + g.getCdgTripId()).collect(Collectors.toSet());

    copiedCdgAlllTripList.removeIf(
        rctbTripEntity -> gojekReportListSet.contains(rctbTripEntity.getDcpRequestId()));

    log.info(
        "cdg trip after remove gojeck trip, remaining trip size: " + copiedCdgAlllTripList.size());
    copiedCdgAlllTripList.stream().limit(10L).forEach(System.out::println);

    for (RCTBTripEntity rctbTripEntity : copiedCdgAlllTripList) {
      reportGojekCdgTripList.add(this.combineTrips(null, Optional.ofNullable(rctbTripEntity)));
    }

    log.info(
        "reportGojekCdgTripList after add cdg remaining trips, Size: {}",
        reportGojekCdgTripList.size());

    log.info("Step6: Update remark_all for trips in previous weeks");
    // update records in reportGojekCdgTripList if exists in gojekTripIdNotInCdgTripIds
    reportGojekCdgTripList.stream()
        .filter(
            reportGojekCdgTripDTO ->
                gojekTripIdNotInCdgTripIds.contains(reportGojekCdgTripDTO.getDcpRequestId()))
        .forEach(
            reportGojekCdgTripDTO ->
                reportGojekCdgTripDTO.setRemarksAll("Booking ID Tally,trip in other weeks"));

    //      String gojekCdgTripId = reportGojekCdgTripDTO.getDcpRequestId();
    //      if (gojekTripIdNotInCdgTripIds.contains(gojekCdgTripId)) {
    //        reportGojekCdgTripDTO.setRemarksAll("Booking ID Tally,Trip in previous weeks");
    //      }
    //   }

    log.info("Step7: Group by remark_all and sum total_amount");
    List<WeeklySummaryDTO> weeklySummaryList = groupingByRemark(reportGojekCdgTripList);
    String cdgreconFile =
        this.excelService.createReport(
            downloadDirPath,
            reportGojekCdgTripList,
            weeklySummaryList,
            gojekStartDate,
            gojekEndDate);
    return cdgreconFile;
  }

  private Optional<RCTBTripEntity> findCdgTripEntityByGojekTripId(
      String gojekTripId, String cdgTripId, List<RCTBTripEntity> cdgRCTBTripList) {
    //            Optional<RCTBTripEntity> cdgTripEntityOptional = Optional.empty();
    //            cdgTripEntityOptional =
    //                    Optional.ofNullable(
    //                            cdgRCTBTripList.stream()
    //                                    .filter(r -> r.getBookingReference() != null)
    //                                    .filter(r -> r.getBookingReference().equals(gojekTripId))
    //                                    .findFirst()
    //                                    .orElse(null));
    //            // if cdgTripEntityOptional is empty then continue to get from cdgTripId
    //            if (cdgTripEntityOptional.isEmpty()) {
    //                cdgTripEntityOptional =
    //                        Optional.ofNullable(
    //                                cdgRCTBTripList.stream()
    //                                        .filter(r -> r.getDcpRequestId() != null)
    //                                        .filter(r -> r.getDcpRequestId().contains(cdgTripId))
    //                                        .findFirst()
    //                                        .orElse(null));
    //            }
    Optional<RCTBTripEntity> cdgTripEntityOptional =
        cdgRCTBTripList.stream()
            .filter(
                r ->
                    r.getBookingReference() != null && r.getBookingReference().equals(gojekTripId)
                        || r.getDcpRequestId() != null && r.getDcpRequestId().contains(cdgTripId))
            .findFirst();
    return cdgTripEntityOptional;
  }

  private List<GojekReport> loadGojekExcelFile(String absolutePath) {
    return this.excelService.importToDatabase(absolutePath);
  }

  private static List<WeeklySummaryDTO> groupingByRemark(
      List<ReportGojekCdgTripDTO> reportGojekCdgTripList) {
    Map<String, WeeklySummaryDTO> summaryByRemarkMap =
        reportGojekCdgTripList.stream()
            .collect(
                Collectors.groupingBy(
                    ReportGojekCdgTripDTO::getRemarksAll,
                    Collectors.collectingAndThen(
                        Collectors.toList(),
                        (list) -> {
                          BigDecimal totalAmount =
                              BigDecimal.valueOf(
                                  list.stream()
                                      .mapToDouble(
                                          (r) -> {
                                            return r.getTotalAmount() == null
                                                ? 0.0
                                                : Double.parseDouble(r.getTotalAmount());
                                          })
                                      .sum());

                          BigDecimal paymentToCdg =
                              BigDecimal.valueOf(
                                  list.stream()
                                      .mapToDouble(
                                          (r) -> {
                                            return r.getPaymentToCdg() == null
                                                ? 0.0
                                                : Double.parseDouble(r.getPaymentToCdg());
                                          })
                                      .sum());

                          Long cntBookingRef =
                              list.stream()
                                  .mapToLong(
                                      (r) -> {
                                        return (r.getBookingReference() == null
                                                    || r.getBookingReference().isEmpty())
                                                && r.getJobNo() == null
                                            ? 0L
                                            : 1L;
                                      })
                                  .sum();

                          long gojekItemCount =
                              list.stream()
                                  .mapToLong(
                                      (r) -> {
                                        return r.getId() != null ? 1L : 0L;
                                      })
                                  .sum();

                          long countDifference = gojekItemCount - cntBookingRef;
                          BigDecimal amountDifference = paymentToCdg.subtract(totalAmount);
                          return new WeeklySummaryDTO(
                              list.get(0).getRemarksAll(),
                              totalAmount.doubleValue(),
                              paymentToCdg.doubleValue(),
                              cntBookingRef,
                              amountDifference.doubleValue(),
                              gojekItemCount,
                              countDifference);
                        })));

    List<WeeklySummaryDTO> summaryByRemarkList = new ArrayList<>(summaryByRemarkMap.values());
    summaryByRemarkList.sort(Comparator.comparing(WeeklySummaryDTO::getRemarkAll));
    summaryByRemarkList.add(
        new WeeklySummaryDTO(
            "Total Results",
            summaryByRemarkList.stream().mapToDouble(WeeklySummaryDTO::getTotalAmount).sum(),
            summaryByRemarkList.stream().mapToDouble(WeeklySummaryDTO::getPaymentToCdg).sum(),
            summaryByRemarkList.stream().mapToLong(WeeklySummaryDTO::getBookingRef).sum(),
            summaryByRemarkList.stream().mapToDouble(WeeklySummaryDTO::getDifference).sum(),
            summaryByRemarkList.stream().mapToLong(WeeklySummaryDTO::getItemCount).sum(),
            summaryByRemarkList.stream().mapToLong(WeeklySummaryDTO::getDifferenceCount).sum()));

    summaryByRemarkList.stream().limit(10L).forEach(System.out::println);
    return summaryByRemarkList;
  }

  private ReportGojekCdgTripDTO combineTrips(
      GojekReport gojekReport, Optional<RCTBTripEntity> cdgTripEntityOptional) {
    ReportGojekCdgTripDTO reportGojekCdgTripDTO = new ReportGojekCdgTripDTO();
    if (gojekReport != null) {
      reportGojekCdgTripDTO.setValuesByGojekReport(gojekReport);
    }

    reportGojekCdgTripDTO.setValuesByCdgTrip(cdgTripEntityOptional);
    return reportGojekCdgTripDTO;
  }

  private Collection<? extends ReportGojekCdgTripDTO> combineTrips(
      List<String> gojekTripIdNotInCdgTripIds,
      List<GojekReport> gojekReportList,
      List<RCTBTripEntity> cdgRCTBripEntityList) {
    List<ReportGojekCdgTripDTO> reportGojekCdgTripList = new ArrayList<>();
    for (String gojekTripIdNotInCdgTripId : gojekTripIdNotInCdgTripIds) {
      Optional<GojekReport> gojekReportOptional =
          Optional.ofNullable(
              gojekReportList.stream()
                  .filter(r -> gojekTripIdNotInCdgTripId.contains(r.getCdgTripId()))
                  .findFirst()
                  .orElse(null));
      if (!gojekReportOptional.isEmpty()) {
        Optional<RCTBTripEntity> cdgTripEntityByGojekTripId =
            this.findCdgTripEntityByGojekTripId(
                gojekReportOptional.get().getGojekTripId(),
                gojekReportOptional.get().getCdgTripId(),
                cdgRCTBripEntityList);
        reportGojekCdgTripList.add(
            combineTrips(gojekReportOptional.get(), cdgTripEntityByGojekTripId));
      }
    }

    return reportGojekCdgTripList;
  }
}
