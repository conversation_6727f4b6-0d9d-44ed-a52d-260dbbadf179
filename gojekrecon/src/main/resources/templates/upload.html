<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>File Upload</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 40vh;
            background-color: #f8f9fa; /* Light background */
            font-family: Arial, sans-serif;
        }

        .upload-container {
            text-align: center;
            background: #fff;
            padding: 500px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        h1 {
            margin-bottom: 20px;
            font-size: 24px;
            color: #333;
        }

        form {
            display: inline-block;
        }

        input[type="file"] {
            width: 400px;
            margin-bottom: 15px;
            margin-left: 80px;
        }

        button {
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007bff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        button:hover {
            background-color: #0056b3;
        }

        /* Loading message and spinner styles */
        #loading {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .spinner {
            margin: 0 auto;
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        #loading-text {
            margin-top: 10px;
            font-size: 16px;
            color: #333;
        }
    </style>
    <script>
        // Show the "Please wait" message when the form is submitted
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            //disable submit button
            // document.getElementById('submit').style.display = 'none';
            // document.getElementById('submit').disabled = true;
            document.getElementById('submit').hidden = true;
        }
    </script>
</head>
<body>
<div class="upload-container">
    <h1>Welcome to CDG TAXI Payment</h1>
    <br>
    <br>
    <h3>Please Upload Your Gojek Excel File</h3>



    <!-- File upload form -->
    <form method="POST" action="/upload" enctype="multipart/form-data" onsubmit="showLoading(); ">
        <input type="file" name="file" required />
        <br>
        <button id="submit" type="submit">Upload</button>
    </form>
    <br>
    <br>
    <br>

    <!-- Loading spinner and message -->
    <div id="loading">
        <div class="spinner"></div>
        <div id="loading-text">Please wait while your file is being processed...</div>
    </div>
</div>
</body>
</html>
