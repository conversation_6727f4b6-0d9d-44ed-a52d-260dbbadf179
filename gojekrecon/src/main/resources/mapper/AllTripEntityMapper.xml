<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdgtaxi.recon.inbound.mappers.AllTripEntityMapper">

    <select id="findAllTripByCdgTripIdsInPreviousWeeks" resultType="com.cdgtaxi.recon.inbound.entities.RCTBTripEntity">
        select t.booking_reference,t.job_no,t.vehicle_no
        ,t.job_status,t.entity,t.booked_channel,TO_CHAR(t.completed_dt, 'YYYY-MM-DD') as completed_dt,t.payment_mode,t.total_amount,t.driver_levy,t.refund
        ,(t.total_amount-t.driver_levy) as calc_driver_refund
        ,ej.dcp_request_id as dcp_request_id
        ,'M2-AA2 (payment_to_cdg - total_amount)' as difference
        ,'' as REMARKS_ALL
        from recsys.rctb_trip_intf t
        LEFT JOIN recsys.ESC_JOB ej ON t.JOB_NO = ej.JOB_NO
        WHERE 1=1
        and t.booked_channel='GOJEK'
        and t.job_status='COMPLETED'
        and ej.dcp_request_id in
        <foreach collection="tripIds" item="tripId" open="(" separator="," close=")">
            #{tripId}
        </foreach>
        and t.completed_dt>=to_date(#{startDT},'yyyymmdd hh24:mi:ss')
        and t.completed_dt<=to_date(#{endDT},'yyyymmdd hh24:mi:ss')
    </select>

</mapper>
