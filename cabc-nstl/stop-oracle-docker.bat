@echo off
echo Stopping Oracle Database Docker container...
echo.

REM Stop Oracle database container
docker-compose -f docker-compose-oracle.yml down

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Oracle database container stopped successfully!
    echo.
    echo 💡 To start again: start-oracle-docker.bat
    echo 🗑️  To remove all data: docker-compose -f docker-compose-oracle.yml down -v
    echo.
) else (
    echo.
    echo ❌ Failed to stop Oracle database container!
    echo.
)

pause
