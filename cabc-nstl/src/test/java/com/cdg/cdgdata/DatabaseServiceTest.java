package com.cdg.cdgdata;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(locations = "classpath:application-test.properties")
class CdgDataApplicationTest {

//    @Test
    void contextLoads() {
        // This test will pass if the Spring context loads successfully
//        assertTrue(true);
    }
}
