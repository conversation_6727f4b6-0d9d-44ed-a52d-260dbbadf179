# Test configuration
spring.main.web-application-type=none
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# Test database
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# Test file paths
app.data.folder-path=./test-data/

# Disable all custom datasources for testing
app.datasource.ptfspro.url=
app.datasource.ptfspro-ibs.url=
app.datasource.ptccpro.url=
app.datasource.ptccpro-dcp.url=
app.datasource.ptccpro-tm.url=
app.datasource.ptccpro-pay.url=
app.datasource.ptcnuat1.url=
app.datasource.ptdapro.url=
app.datasource.ptcnuat2.url=
app.datasource.ptdapro-dcp.url=
