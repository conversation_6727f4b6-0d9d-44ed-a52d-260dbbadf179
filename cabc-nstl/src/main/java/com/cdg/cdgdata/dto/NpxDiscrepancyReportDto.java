package com.cdg.cdgdata.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpxDiscrepancyReportDto {
    private List<NpxDiscrepancyDto> stagingOnlyRecords;
    private List<NpxDiscrepancyDto> setlOnlyRecords;
    private String fileName;
    private String reportDate;
    private int stagingOnlyCount;
    private int setlOnlyCount;
    private int totalDiscrepancies;
    private String status;
    private String message;
}
