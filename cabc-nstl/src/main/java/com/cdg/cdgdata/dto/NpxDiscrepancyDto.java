package com.cdg.cdgdata.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpxDiscrepancyDto {
    private String fileId;
    private String financialInstitutionId;
    private BigDecimal txnAmount;
    private LocalDateTime createdDt;
    private String recordType;
    private String product;
    private String transactionCd;
    private LocalDateTime transactionDt;
    private String corporationId;
    private String entity02Id;
    private String entity03Id;
    private String entity04Id;
    private String entity05Id;
    private BigDecimal amount01No;
    private BigDecimal amount02No;
    private BigDecimal amount03No;
    private BigDecimal amount04No;
    private BigDecimal amount05No;
    private BigDecimal fee01No;
    private BigDecimal fee02No;
    private BigDecimal fee03No;
    private BigDecimal fee04No;
    private BigDecimal fee05No;
    private String cardIssuerId;
    private String reversalCd;
    private String responseCd;
    private String etc01Tx;
    private String origTid;
    private String origTimestamp;
    private String origSeqNo;
    private String etc05Tx;
    private String canNo;
    private String sequenceNo;
    private String referenceTx;
    private String voidFl;
    private LocalDateTime reconDate;
    private String reconStatus;
    private String discrepancyType; // "STAGING_ONLY" or "SETL_ONLY"
}
