package com.cdg.cdgdata.controller;

import com.cdg.cdgdata.dto.CabcError51ResponseDto;
import com.cdg.cdgdata.service.CabcError51Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/cabc-error51")
@Slf4j
public class CabcError51Controller {

    @Autowired
    private CabcError51Service cabcError51Service;

    /**
     * Process CABC Error 51 data for a specific date
     * 
     * @param targetDay Date in format dd/MM/yyyy (e.g., "09/06/2025")
     * @return CabcError51ResponseDto containing processed data and file information
     */
    @PostMapping("/process")
    public ResponseEntity<CabcError51ResponseDto> processData(@RequestParam String targetDay) {
        try {
            log.info("Received request to process CABC Error 51 data for date: {}", targetDay);
            
            CabcError51ResponseDto response = cabcError51Service.processData(targetDay);
            
            if ("SUCCESS".equals(response.getStatus())) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            log.error("Error processing CABC Error 51 request", e);
            
            CabcError51ResponseDto errorResponse = new CabcError51ResponseDto();
            errorResponse.setStatus("ERROR");
            errorResponse.setMessage("Internal server error: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("CABC Error 51 Service is running");
    }
}
