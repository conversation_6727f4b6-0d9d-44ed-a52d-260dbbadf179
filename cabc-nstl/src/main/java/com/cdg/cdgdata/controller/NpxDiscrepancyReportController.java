package com.cdg.cdgdata.controller;

import com.cdg.cdgdata.dto.NpxDiscrepancyReportDto;
import com.cdg.cdgdata.service.NpxDiscrepancyReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/npx-discrepancy")
@Slf4j
public class NpxDiscrepancyReportController {

    @Autowired
    private NpxDiscrepancyReportService npxDiscrepancyReportService;

    /**
     * Generate NPX discrepancy report for a specific date
     * 
     * @param reportDate Date in format dd/MM/yyyy (e.g., "07/06/2025")
     * @return NpxDiscrepancyReportDto containing discrepancy data and file information
     */
    @PostMapping("/generate-report")
    public ResponseEntity<NpxDiscrepancyReportDto> generateReport(@RequestParam String reportDate) {
        try {
            log.info("Received request to generate NPX discrepancy report for date: {}", reportDate);
            
            NpxDiscrepancyReportDto response = npxDiscrepancyReportService.generateDiscrepancyReport(reportDate);
            
            if ("SUCCESS".equals(response.getStatus())) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            log.error("Error generating NPX discrepancy report", e);
            
            NpxDiscrepancyReportDto errorResponse = new NpxDiscrepancyReportDto();
            errorResponse.setStatus("ERROR");
            errorResponse.setMessage("Internal server error: " + e.getMessage());
            errorResponse.setReportDate(reportDate);
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("NPX Discrepancy Report Service is running");
    }
}
