package com.cdg.cdgdata;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

@SpringBootApplication
@EnableConfigurationProperties
public class CdgDataApplication implements CommandLineRunner {

    public static void main(String[] args) {
        SpringApplication.run(CdgDataApplication.class, args);
    }

//    @Bean
//    public CommandLineRunner commandLineRunner() {
//        return args -> {
//            // This will execute the main data processing logic
//            DataProcessingRunner.run();
//        };
//    }

    @Override
    public void run(String... args) throws Exception {
// This will execute the main data processing logic
        System.out.println("Running main program");
        DataProcessingRunner.run();
    }
}
