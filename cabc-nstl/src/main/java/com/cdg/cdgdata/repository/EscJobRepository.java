package com.cdg.cdgdata.repository;

import com.cdg.cdgdata.entity.EscJob;
import com.cdg.cdgdata.dto.JobCcDataDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EscJobRepository extends JpaRepository<EscJob, String> {

    @Query("SELECT new com.cdg.cdgdata.dto.JobCcDataDto(e.jobNo, e.ccNumber, e.paxContact) " +
           "FROM EscJob e " +
           "WHERE e.jobNo IN :jobNumbers")
    List<JobCcDataDto> findJobCcDataByJobNumbers(@Param("jobNumbers") List<String> jobNumbers);
}
