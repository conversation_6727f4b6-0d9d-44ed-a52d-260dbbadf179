package com.cdg.cdgdata.repository;

import com.cdg.cdgdata.entity.EscTripDetails;
import com.cdg.cdgdata.dto.TripDataDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EscTripDetailsRepository extends JpaRepository<EscTripDetails, Long> {

    @Query("SELECT new com.cdg.cdgdata.dto.TripDataDto(" +
           "CONCAT(" +
           "  FUNCTION('TO_CHAR', e.endDt, 'YYYYMMDD'), " +
           "  FUNCTION('LPAD', FUNCTION('TO_CHAR', e.amountPaid * 100), 12, '0'), " +
           "  FUNCTION('TO_CHAR', FUNCTION('ROUND', e.startY, 4)), " +
           "  FUNCTION('TO_CHAR', FUNCTION('ROUND', e.startX, 4)), " +
           "  FUNCTION('TO_CHAR', FUNCTION('ROUND', e.endY, 4)), " +
           "  FUNCTION('TO_CHAR', FUNCTION('ROUND', e.endX, 4))" +
           "), " +
           "e.jobNo) " +
           "FROM EscTripDetails e " +
           "WHERE e.jobNo IN :jobNumbers")
    List<TripDataDto> findTripDataByJobNumbers(@Param("jobNumbers") List<String> jobNumbers);
}
