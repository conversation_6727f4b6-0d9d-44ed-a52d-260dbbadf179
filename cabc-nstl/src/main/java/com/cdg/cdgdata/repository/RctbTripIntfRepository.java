package com.cdg.cdgdata.repository;

import com.cdg.cdgdata.entity.RctbTripIntf;
import com.cdg.cdgdata.dto.NstlDataDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface RctbTripIntfRepository extends JpaRepository<RctbTripIntf, Long> {

    @Query("SELECT new com.cdg.cdgdata.dto.NstlDataDto(r.jobNo, r.completedDt, r.reconDate) " +
           "FROM RctbTripIntf r " +
           "WHERE r.paymentMode IN ('CABC', 'EVCH') " +
           "AND r.reconDate >= :startDate " +
           "AND r.reconDate <= :endDate " +
           "AND r.status = 'E' " +
           "AND r.errorCode = 'NSTL' " +
           "ORDER BY r.completedDt ASC")
    List<NstlDataDto> findNstlData(@Param("startDate") LocalDateTime startDate, 
                                   @Param("endDate") LocalDateTime endDate);

    @Query("SELECT r.jobNo FROM RctbTripIntf r " +
           "WHERE r.paymentMode IN ('CABC', 'EVCH') " +
           "AND r.reconDate >= :startDate " +
           "AND r.reconDate <= :endDate " +
           "AND r.status = 'E' " +
           "AND r.errorCode = 'NSTL' " +
           "ORDER BY r.completedDt ASC")
    List<String> findNstlJobNumbers(@Param("startDate") LocalDateTime startDate, 
                                    @Param("endDate") LocalDateTime endDate);
}
