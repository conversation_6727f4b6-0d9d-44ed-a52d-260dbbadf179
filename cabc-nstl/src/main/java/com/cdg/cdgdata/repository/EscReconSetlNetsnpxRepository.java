package com.cdg.cdgdata.repository;

import com.cdg.cdgdata.entity.EscReconSetlNetsnpx;
import com.cdg.cdgdata.dto.NpxDiscrepancyDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface EscReconSetlNetsnpxRepository extends JpaRepository<EscReconSetlNetsnpx, Long> {

    /**
     * Find records that exist in setl but not in staging table (setl only discrepancies)
     */
    @Query("SELECT new com.cdg.cdgdata.dto.NpxDiscrepancyDto(" +
           "r.fileId, r.financialInstitutionId, r.txnAmount, r.createdDt, r.recordType, " +
           "r.product, r.transactionCd, r.transactionDt, r.corporationId, r.entity02Id, " +
           "r.entity03Id, r.entity04Id, r.entity05Id, r.amount01No, r.amount02No, " +
           "r.amount03No, r.amount04No, r.amount05No, r.fee01No, r.fee02No, " +
           "r.fee03No, r.fee04No, r.fee05No, r.cardIssuerId, r.reversalCd, " +
           "r.responseCd, r.etc01Tx, r.origTid, r.origTimestamp, r.origSeqNo, " +
           "r.etc05Tx, r.canNo, r.sequenceNo, r.referenceTx, r.voidFl, " +
           "r.reconDate, r.reconStatus, 'SETL_ONLY') " +
           "FROM EscReconSetlNetsnpx r " +
           "WHERE r.reconDate >= :startDate " +
           "AND r.reconDate <= :endDate " +
           "AND NOT EXISTS (" +
           "  SELECT 1 FROM EscReconSetlNetsnpxStg s " +
           "  WHERE s.fileId = r.fileId " +
           "  AND s.financialInstitutionId = r.financialInstitutionId " +
           "  AND s.txnAmount = r.txnAmount " +
           "  AND s.transactionDt = r.transactionDt " +
           "  AND s.origTid = r.origTid " +
           "  AND s.origTimestamp = r.origTimestamp " +
           "  AND s.origSeqNo = r.origSeqNo" +
           ") " +
           "ORDER BY r.reconDate, r.fileId")
    List<NpxDiscrepancyDto> findSetlOnlyDiscrepancies(@Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate);

    /**
     * Count setl only discrepancies
     */
    @Query("SELECT COUNT(r) " +
           "FROM EscReconSetlNetsnpx r " +
           "WHERE r.reconDate >= :startDate " +
           "AND r.reconDate <= :endDate " +
           "AND NOT EXISTS (" +
           "  SELECT 1 FROM EscReconSetlNetsnpxStg s " +
           "  WHERE s.fileId = r.fileId " +
           "  AND s.financialInstitutionId = r.financialInstitutionId " +
           "  AND s.txnAmount = r.txnAmount " +
           "  AND s.transactionDt = r.transactionDt " +
           "  AND s.origTid = r.origTid " +
           "  AND s.origTimestamp = r.origTimestamp " +
           "  AND s.origSeqNo = r.origSeqNo" +
           ")")
    long countSetlOnlyDiscrepancies(@Param("startDate") LocalDateTime startDate,
                                    @Param("endDate") LocalDateTime endDate);

    /**
     * Find all setl records for a specific date range
     */
    @Query("SELECT r FROM EscReconSetlNetsnpx r " +
           "WHERE r.reconDate >= :startDate " +
           "AND r.reconDate <= :endDate " +
           "ORDER BY r.reconDate, r.fileId")
    List<EscReconSetlNetsnpx> findByReconDateBetween(@Param("startDate") LocalDateTime startDate,
                                                     @Param("endDate") LocalDateTime endDate);
}
