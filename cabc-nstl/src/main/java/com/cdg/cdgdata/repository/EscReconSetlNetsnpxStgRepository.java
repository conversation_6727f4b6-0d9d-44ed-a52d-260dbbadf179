package com.cdg.cdgdata.repository;

import com.cdg.cdgdata.entity.EscReconSetlNetsnpxStg;
import com.cdg.cdgdata.dto.NpxDiscrepancyDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface EscReconSetlNetsnpxStgRepository extends JpaRepository<EscReconSetlNetsnpxStg, Long> {

    /**
     * Find records that exist in staging but not in setl table (staging only discrepancies)
     * Temporarily simplified to avoid complex DTO constructor issues
     */
    @Query("SELECT s FROM EscReconSetlNetsnpxStg s " +
           "WHERE s.createdDt >= :startDate " +
           "AND s.createdDt <= :endDate " +
           "AND NOT EXISTS (" +
           "  SELECT 1 FROM EscReconSetlNetsnpx r " +
           "  WHERE r.fileId = s.fileId " +
           "  AND r.financialInstitutionId = s.financialInstitutionId " +
           "  AND r.txnAmount = s.txnAmount " +
           "  AND r.transactionDt = s.transactionDt " +
           "  AND r.origTid = s.origTid " +
           "  AND r.origTimestamp = s.origTimestamp " +
           "  AND r.origSeqNo = s.origSeqNo" +
           ") " +
           "ORDER BY s.createdDt, s.fileId")
    List<EscReconSetlNetsnpxStg> findStagingOnlyDiscrepancies(@Param("startDate") LocalDateTime startDate,
                                                               @Param("endDate") LocalDateTime endDate);

    /**
     * Count staging only discrepancies
     */
    @Query("SELECT COUNT(s) " +
           "FROM EscReconSetlNetsnpxStg s " +
           "WHERE s.createdDt >= :startDate " +
           "AND s.createdDt <= :endDate " +
           "AND NOT EXISTS (" +
           "  SELECT 1 FROM EscReconSetlNetsnpx r " +
           "  WHERE r.fileId = s.fileId " +
           "  AND r.financialInstitutionId = s.financialInstitutionId " +
           "  AND r.txnAmount = s.txnAmount " +
           "  AND r.transactionDt = s.transactionDt " +
           "  AND r.origTid = s.origTid " +
           "  AND r.origTimestamp = s.origTimestamp " +
           "  AND r.origSeqNo = s.origSeqNo" +
           ")")
    long countStagingOnlyDiscrepancies(@Param("startDate") LocalDateTime startDate,
                                       @Param("endDate") LocalDateTime endDate);

    /**
     * Find all staging records for a specific date range
     */
    @Query("SELECT s FROM EscReconSetlNetsnpxStg s " +
           "WHERE s.createdDt >= :startDate " +
           "AND s.createdDt <= :endDate " +
           "ORDER BY s.createdDt, s.fileId")
    List<EscReconSetlNetsnpxStg> findByCreatedDtBetween(@Param("startDate") LocalDateTime startDate,
                                                         @Param("endDate") LocalDateTime endDate);
}
