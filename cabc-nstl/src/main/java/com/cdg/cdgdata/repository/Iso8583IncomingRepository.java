package com.cdg.cdgdata.repository;

import com.cdg.cdgdata.entity.Iso8583Incoming;
import com.cdg.cdgdata.dto.IsoDataDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface Iso8583IncomingRepository extends JpaRepository<Iso8583Incoming, Long> {

    // Temporarily disabled complex query that was causing startup issues
    // @Query("SELECT new com.cdg.cdgdata.dto.IsoDataDto(" +
    //        "CONCAT(" +
    //        "  FUNCTION('TO_CHAR', FUNCTION('CAST', i.txnDate, 'date'), 'YYYYMMDD'), " +
    //        "  i.txnAmt, " +
    //        "  FUNCTION('TO_CHAR', FUNCTION('ROUND', FUNCTION('SUBSTR', i.tripInformation, 25, 15), 4)), " +
    //        "  FUNCTION('TO_CHAR', FUNCTION('ROUND', FUNCTION('SUBSTR', i.tripInformation, 40, 15), 4)), " +
    //        "  FUNCTION('TO_CHAR', FUNCTION('ROUND', FUNCTION('SUBSTR', i.tripInformation, 55, 15), 4)), " +
    //        "  FUNCTION('TO_CHAR', FUNCTION('ROUND', FUNCTION('SUBSTR', i.tripInformation, 70, 15), 4))" +
    //        "), " +
    //        "i.pan, i.msgId, FUNCTION('CAST', i.txnDate, 'date')) " +
    //        "FROM Iso8583Incoming i " +
    //        "WHERE i.msgId >= :lastRecord " +
    //        "AND i.tripInformation IS NOT NULL " +
    //        "AND FUNCTION('LENGTH', i.tripInformation) > 87 " +
    //        "ORDER BY i.msgId ASC")
    @Query("SELECT i FROM Iso8583Incoming i WHERE i.msgId >= :lastRecord ORDER BY i.msgId ASC")
    List<Iso8583Incoming> findIsoDataFromLastRecord(@Param("lastRecord") Long lastRecord);

    @Query("SELECT i FROM Iso8583Incoming i " +
           "WHERE i.pan LIKE CONCAT('%', :panSuffix) " +
           "AND i.txnDate >= :startDate " +
           "AND i.txnDate <= :endDate " +
           "ORDER BY i.pan DESC")
    List<Iso8583Incoming> findByPanSuffixAndDateRange(@Param("panSuffix") String panSuffix,
                                                       @Param("startDate") java.time.LocalDateTime startDate,
                                                       @Param("endDate") java.time.LocalDateTime endDate);

    @Query("SELECT i FROM Iso8583Incoming i " +
           "WHERE i.txnAmt = :txnAmt " +
           "AND i.expiryDate = :expiryDate " +
           "AND i.txnDate >= :startDate " +
           "AND i.txnDate <= :endDate")
    List<Iso8583Incoming> findByTxnAmtAndExpiryDateAndDateRange(@Param("txnAmt") String txnAmt,
                                                                 @Param("expiryDate") String expiryDate,
                                                                 @Param("startDate") java.time.LocalDateTime startDate,
                                                                 @Param("endDate") java.time.LocalDateTime endDate);
}
