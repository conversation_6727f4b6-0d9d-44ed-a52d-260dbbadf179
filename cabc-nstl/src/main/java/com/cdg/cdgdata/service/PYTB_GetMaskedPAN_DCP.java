package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PYTB_GetMaskedPAN_DCP {

	public static void GetData() throws IOException {

		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");

		try {
			Connection con = Helper.GetConnection("ptccpro_dcp");
			Statement stmt = con.createStatement();

			// Load data
			ArrayList<String[]> list = Helper.ReadDataSource("PYTB_GETPYTB_MASKEDPAN_DCP.txt");
			int total = list.size();

			int chunk = 1;
			int cnt = 0;
			String dcp = "";
			for (int counter = 0; counter < list.size(); counter++) {

				cnt++;
				dcp = "'" + list.get(counter)[0] + "'," + dcp;
				if (chunk == cnt || counter + 1 == total) {

					dcp = dcp.substring(0, dcp.length() - 1);

					 
					  String sqlString = new StringBuilder()
					  .append("select td.booking_ref, td.account_id,td.txn_type, pa.masked_account_no from dcp_sys.pytb_wd_txn td join dcp_sys.pytb_account pa on td.account_id = pa.account_id ")
					  .append(" where txn_type in ('authorization') and ")
					  .append(" td.booking_ref in (" + dcp + ") ").toString();
					

					System.out.println(String.valueOf(counter) + " " +  sqlString);

					ResultSet rs = stmt.executeQuery(sqlString);
					ResultSetMetaData rsmd = rs.getMetaData();

					int columnsNumber = rsmd.getColumnCount();

					String columns = "";
					String records = "";

					while (rs.next()) {
						if (columns == "") {
							for (int i = 1; i < columnsNumber + 1; i++) {
								columns += rsmd.getColumnName(i) + "\t";
							}
							;
							//myWriter.write(columns + "\r\n");
						}

						for (int i = 1; i < columnsNumber + 1; i++) {

							records += rs.getString(i) + "\t";
						}
						;
						myWriter.write(records + "\r\n");
						records = "";
					}
					;

					dcp = "";
					cnt = 0;
				}

			}

			myWriter.close();
		    con.close();

		} catch (Exception e) {
			log.error("Error occurred", e);
		}

	}

}
