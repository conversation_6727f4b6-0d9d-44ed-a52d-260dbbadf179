package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


@Slf4j
public class RCTB_NPX_DBS_TID {
	
	private static final DecimalFormat decfor = new DecimalFormat("0.00");  
	public static ArrayList<Object[]> setlList = new ArrayList<Object[]>();
	public static  ArrayList<String> setlcolumnList = new ArrayList<String>();
	public static  ArrayList<Integer> setlcolumnTypeList = new ArrayList<Integer>();
	public static ArrayList<ArrayList<String>> map = new ArrayList<ArrayList<String>>();
	public static  ArrayList<String> dbsList = new ArrayList<String>();
	public static  Map<String, String> dbsMap = new HashMap<>();
	public static  ArrayList<String> rctbUniqList = new ArrayList<String>();

	public static String dbsPath = "C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\NPX report for MinShi\\DBS DUMP\\";
	
	public static void GetData() throws IOException {
		
		String dbsFileName = "";
		String rctbFileName = "";
	    
		map = Helper.ReadNPXDBSTIDMap();
		
		String[] pairs = { 
				//"29 JUL TO 7 AUGv1.csv,NETSNPX_20230802.CSV,"
				//"29 JUL TO 7 AUGv1.csv,NETSNPX_20230801.CSV,"
				//"25 JUL TO 8 AUGv1.csv,NETSNPX_20230731.CSV,"
				//"25 JUL TO 8 AUGv1.csv,NETSNPX_20230803.CSV,",
				//"25 JUL TO 8 AUGv1.csv,NETSNPX_20230804.CSV,",
				//"25 JUL TO 8 AUGv1.csv,NETSNPX_20230806.CSV,",
				//"25 JUL TO 8 AUGv1.csv,NETSNPX_20230807.CSV,",
				//"25 JUL TO 8 AUGv1.csv,NETSNPX_20230808.CSV,"
				//"4 AUG TO 8 AUGv2.csv,NETSNPX_20230806.CSV"
				//"3 AUG TO 8 AUGv2.csv,NETSNPX_20230804.CSV"
				//"23 JUL TO 24 JUL.csv,NETSNPX_20230724.CSV,"
				//"7 AUG TO 12 AUG.csv,NETSNPX_20230809.CSV,",
				//"7 AUG TO 12 AUG.csv,NETSNPX_20230810.CSV,"
				//"7 AUG TO 12 AUG.csv,NETSNPX_20230811.CSV,"
				//"7 AUG TO 12 AUG.csv,NETSNPX_20230812.CSV,"
				//"19 JUL TO 21 JUL.csv,NETSNPX_20230720.CSV,"
				//"19 JUL TO 31 JUL.csv,NETSNPX_20230721.CSV"
				//"19 JUL TO 31 JUL.csv,NETSNPX_20230722.CSV"
				//"19 JUL TO 31 JUL.csv,NETSNPX_20230730.CSV",
				//"19 JUL TO 31 JUL.csv,NETSNPX_20230731.CSV"
				//"11 AUG TO 15 AUGv1.csv,NETSNPX_20230813.CSV",
				//"11 AUG TO 15 AUG.csv,NETSNPX_20230814.CSV",
				//"11 AUG TO 15 AUG.csv,NETSNPX_20230815.CSV"
				//"14 AUG TO 19 AUG.csv,NETSNPX_20230816.CSV",
				//"14 AUG TO 19 AUG.csv,NETSNPX_20230817.CSV",
				//"14 AUG TO 19 AUG.csv,NETSNPX_20230818.CSV",
				//"14 AUG TO 19 AUG.csv,NETSNPX_20230819.CSV" 
				//"14 AUG TO 19 AUG.csv,NETSNPX_20230820.CSV"
				"Jun 29 - Jul 3.csv,NETSNPX_20240702.CSV"
		};
		
		
        for (String p : pairs) 
        {
        	
        	dbsFileName = p.split(",")[0];
        	rctbFileName = p.split(",")[1];
        	
    	    GetRctbSetl(rctbFileName);    
    	    ReadDBS(dbsFileName);
    	    
    	    String fileday = rctbFileName.substring(8,16);
    	    String excelFilename = "NPX_DBS_" + fileday + ".xlsx";
    	    WriteExcel(setlcolumnList, setlcolumnTypeList, setlList, excelFilename);
        }
		

 
	}
	

	

	public static void GetRctbSetl(String filename) throws IOException {
		
		setlcolumnList.clear();
		setlcolumnTypeList.clear();
		setlList.clear();
		
		String sqlString = new StringBuilder()
				.append(" select JOB_NO,ENTITY,TXN_DATE,CARD_NO, TRIM(APPROVAL_CODE) ,TXN_AMOUNT,TID,STAN,CARD_TYPE "
						+ " from rctb_setl  ")
				.append(" where file_id = '" + filename + "' " )
				.append(" AND CARD_TYPE in ('M','V') ")
				//.append(" where setl_pk in ( 263769762,263769430)  " )
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  setlcolumnList.add(rsmd.getColumnName(i+1).toString());
						  setlcolumnTypeList.add(rsmd.getColumnType(i+1));
						};
					
						setlcolumnList.add("DBS_TID");
						setlcolumnTypeList.add(12);
						setlcolumnList.add("UNIQ");
						setlcolumnTypeList.add(12);
						setlcolumnList.add("EXIST");
						setlcolumnTypeList.add(12);
						setlcolumnList.add("Posted Date");
						setlcolumnTypeList.add(12);
						setlcolumnList.add("Txn Date");
						setlcolumnTypeList.add(12);
						setlcolumnList.add("Batch Date");
						setlcolumnTypeList.add(12);
						columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  setlList.add(recList);
				  
			  };

			con.close();
			log.info("read SQL rctb_setl done. => " + filename);	

		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		
	}
	
	
	public static void ReadDBS(String dbsFileName) throws IOException {
		
		dbsList.clear();
		String line = "";  
		String splitBy = ",";  
		try   
		{  
			try (BufferedReader br = new BufferedReader(new FileReader(dbsPath + dbsFileName))) 
			{
				while ((line = br.readLine()) != null)   //returns a Boolean value  
				{  
					if(!line.contains("Product,Account No,Posted Date")) {
						String[] record = line.split(splitBy);    // use comma as separator  
						String rec = (record[9] + record[13].substring(1,5) + record[13].substring(16,20) + record[14] + record[15] ).replace("\"", ""); 
						String details = record[2] + "," + record[3] + "," + record[10] + ",";
						dbsList.add(rec);
						dbsMap.put(rec,  details);
					}
				}
			}  
		}   
			catch (IOException e)   
		{  
				e.printStackTrace();  
		}  
		
		
		log.info("read DBS file done. => " + dbsFileName);
		
	}
	
	public static void WriteDBS(String dbsFileName) throws IOException {
		
		String filenameUniq = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filenameUniq + "_" + dbsFileName );
		
		
		for (int i = 0; i < dbsList.size(); i++) { 
			myWriter.write(dbsList.get(i) + "\r\n");
		}
		
		myWriter.close();
		log.info("write DBS file uniq done.");
		
	}
	
	public static void WriteExcel(ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList, String filename) throws FileNotFoundException, IOException {
		Workbook wb = new XSSFWorkbook();
		Sheet sheet1 = wb.createSheet("Sheet1");
		Boolean withFalse = false;

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		log.info("Writing summary excel...");
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i+1);
	        headerCell.setCellValue(columnList.get(i));
	        headerCell.setCellStyle(style);
		
		};
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			//subtract the additional fields from the database fields columnList.size()-6
			for (int j = 0; j < columnList.size()-6; j++) { 
		        Cell recordCell = recordRow.createCell(j+1);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(2)) {
		        		recordCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		recordCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	recordCell.setCellValue("");
		        }

			};
			Cell dbsTidCell = recordRow.createCell(10);
			String dbsTid = GetDBSTID(recordObj[6].toString(),recordObj[8].toString());
			dbsTidCell.setCellValue(dbsTid);
			Cell uniqCell = recordRow.createCell(11);
			Cell existCell = recordRow.createCell(12);
			Cell postdtCell = recordRow.createCell(13);
			Cell txndtCell = recordRow.createCell(14);
			Cell batchdtCell = recordRow.createCell(15);
			
			String uniq = dbsTid + recordObj[3].toString().substring(0,4) + recordObj[3].toString().substring(12,16) + recordObj[4].toString() + decfor.format(Double.parseDouble(recordObj[5].toString()));
			uniqCell.setCellValue(uniq);
			rctbUniqList.add(uniq);

			if(dbsMap.containsKey(uniq)) {      //if(dbsList.contains(uniq)) {
				existCell.setCellValue("TRUE");
				String[] details = dbsMap.get(uniq).split(",");
				postdtCell.setCellValue(details[0]);
				txndtCell.setCellValue(details[1]);
				batchdtCell.setCellValue(details[2]);
			}
			else {
				existCell.setCellValue("FALSE");
				withFalse = true;
			}
			
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<columnList.size()+1;i++) {
			sheet1.autoSizeColumn(i);
		}
		

		if (withFalse) {
			filename=filename.replace(".xlsx", "_false.xlsx");
		}
		else
		{
			filename=filename.replace(".xlsx", "_no_false.xlsx");
		}
		
		try (OutputStream fileOut = new FileOutputStream(Constants.FolderPath +  filename)) {
		    wb.write(fileOut);
		    wb.close();
		}
 	}
	
	public static String GetDBSTID(String netsTID, String scheme) {
		String dbsTID = "";
		
		int netsIdx = map.get(0).indexOf(netsTID);
		
		switch (scheme) {
		case "A":
			dbsTID = map.get(1).get(netsIdx);
			break;
		case "V":
			dbsTID = map.get(2).get(netsIdx);
			break;
		case "M":
			dbsTID = map.get(3).get(netsIdx);
			break;
		case "J":
			dbsTID = map.get(4).get(netsIdx);
			break;
		}
		
		return dbsTID;
	}
	
	public static void WriteDBSUniq() throws FileNotFoundException, IOException {
		Workbook wb = new XSSFWorkbook();
		Sheet sheet1 = wb.createSheet("Sheet1");

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		log.info("Writing DBS summary excel...");
		
        Cell headerCell1 = headerRow.createCell(1);
        headerCell1.setCellValue("DBS TID");
        headerCell1.setCellStyle(style);
		
        Cell headerCell2 = headerRow.createCell(2);
        headerCell2.setCellValue("Payer Details");
        headerCell2.setCellStyle(style);
        
        Cell headerCell3 = headerRow.createCell(3);
        headerCell3.setCellValue("AuthCode");
        headerCell3.setCellStyle(style);
        
        Cell headerCell4 = headerRow.createCell(4);
        headerCell4.setCellValue("TransAmt(SGD)");
        headerCell4.setCellStyle(style);
        
        Cell headerCell5 = headerRow.createCell(5);
        headerCell5.setCellValue("EXIST IN NPX");
        headerCell5.setCellStyle(style);
		
        String value = "";
		//WRITE THE RECORDS
		for (int i = 0; i < dbsList.size()-1; i++) { 
			Row recordRow = sheet1.createRow(i+1);
			String recordObj = dbsList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			if (recordObj.length()<22) {
				Cell recordCell = recordRow.createCell(1);
				recordCell.setCellValue(recordObj);
			}
			else {
				for (int j = 1; j < 6; j++) { 
			        Cell recordCell = recordRow.createCell(j);
			        switch (j) {
			        
			        	case 1:
			        		//value = (recordObj.substring(0,8));
			        		recordCell.setCellValue(recordObj.substring(0,8));
			        		break;
			        		
			        	case 2:
			        		//value = recordObj.substring(8,12)+ "xxxxxxxx" + recordObj.substring(12,16);
			        		recordCell.setCellValue(recordObj.substring(8,12)+ "xxxxxxxx" + recordObj.substring(12,16));
			        		break;
			        		
			        	case 3:
			        		//value = recordObj.substring(16,22) ;
			        		recordCell.setCellValue(recordObj.substring(16,22));
			        		break;
			        		
			        	case 4:
			        		//value = recordObj.substring(16,22) ;
			        		recordCell.setCellValue(recordObj.substring(22,recordObj.length()));
			        		break;
			        		
			        	case 5:
			    			if(rctbUniqList.contains(recordObj)) {
			    				recordCell.setCellValue("TRUE");
			    			}
			    			else {
			    				recordCell.setCellValue("FALSE");
			    			}
			        		break;
			        }
			        

				};
			}

 
			
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<4;i++) {
			sheet1.autoSizeColumn(i);
		}
		

		String filename = Helper.GenerateFileName();
		try (OutputStream fileOut = new FileOutputStream(Constants.FolderPath +  filename + ".xlsx")) {
		    wb.write(fileOut);
		    wb.close();
		}
 	}
	
	/*
	 * public static void GetData2() throws IOException {
	 * 
	 * String dbsFileName = "";
	 * 
	 * ReadDBS(dbsFileName); WriteDBSUniq(); //WriteDBS();
	 * 
	 * }
	 * 
	 * public static void GetData() throws IOException {
	 * 
	 * String dbsFileName = ""; String rctbFileName = "";
	 * 
	 * map = Helper.ReadNPXDBSTIDMap("NPX_DBS_TID_MAP.txt");
	 * 
	 * GetRctbSetl(rctbFileName); ReadDBS(dbsFileName); WriteDBS(dbsFileName);
	 * 
	 * String fileday = rctbFileName.substring(8,16); String excelFilename =
	 * "NPX_DBS_" + fileday + ".xlsx"; WriteExcel(setlcolumnList,
	 * setlcolumnTypeList, setlList, excelFilename);
	 * 
	 * //WriteDBSUniq();
	 * 
	 * }
	 */
	
}


