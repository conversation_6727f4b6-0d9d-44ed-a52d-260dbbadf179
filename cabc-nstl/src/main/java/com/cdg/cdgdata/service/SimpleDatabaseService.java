package com.cdg.cdgdata.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * Simple Database Service that uses lazy loading
 * This replaces the complex multi-datasource configuration
 */
@Service("simpleDatabaseService")
@Slf4j
public class SimpleDatabaseService {

    @Autowired
    private DataSource dataSource;  // Uses the primary datasource

    @Autowired
    private JdbcTemplate jdbcTemplate;  // Uses the primary JdbcTemplate

    /**
     * Get the primary JdbcTemplate
     * For now, all database operations use the primary datasource
     */
    public JdbcTemplate getJdbcTemplate() {
        return jdbcTemplate;
    }

    /**
     * Get JdbcTemplate for a specific database
     * Currently returns the primary template, but can be extended for multiple databases
     */
    public JdbcTemplate getJdbcTemplate(String databaseName) {
        log.debug("Requested JdbcTemplate for database: {}, returning primary", databaseName);
        return jdbcTemplate;
    }

    /**
     * Get a direct connection for legacy code compatibility
     */
    public Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }

    /**
     * Get connection for a specific database (legacy compatibility)
     */
    public Connection getConnection(String databaseName) throws SQLException {
        log.debug("Requested connection for database: {}, returning primary", databaseName);
        return getConnection();
    }

    /**
     * Test database connectivity
     */
    public boolean testConnection() {
        try {
            jdbcTemplate.execute("SELECT 1 FROM DUAL");
            log.info("Database connection test successful");
            return true;
        } catch (Exception e) {
            log.error("Database connection test failed", e);
            return false;
        }
    }

    /**
     * Test connectivity for a specific database
     */
    public boolean testConnection(String databaseName) {
        log.debug("Testing connection for database: {}, using primary", databaseName);
        return testConnection();
    }

    /**
     * Execute a query and return the result count
     */
    public int executeQuery(String sql) {
        try {
            return jdbcTemplate.update(sql);
        } catch (Exception e) {
            log.error("Error executing query: {}", sql, e);
            throw new RuntimeException("Database query failed", e);
        }
    }

    /**
     * Execute a query on a specific database
     */
    public int executeQuery(String databaseName, String sql) {
        log.debug("Executing query on database: {}, using primary", databaseName);
        return executeQuery(sql);
    }
}
