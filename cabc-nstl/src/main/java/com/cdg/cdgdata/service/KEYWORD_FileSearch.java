package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Iterator;
import java.util.stream.Stream;


@Slf4j
public class KEYWORD_FileSearch {
	
	static String filename;
	static FileWriter myWriter;
	//static String keyword;
	static String[] keywords;
	
	public static void GetData(String path) throws IOException {
		
		filename = Helper.GenerateFileName();
		myWriter = new FileWriter(Constants.FolderPath + filename + ".csv");
		keywords= Helper.ReadList("KEYWORDS.txt");
 
		myWriter.write("File,Row,Keyword,Code \r\n");
	      // Creating try-catch block and   ---- Constants.FolderPath + "/FileContent/"
        try (Stream<Path> filepath
             = Files.walk(Paths.get(path)))
 
        {
            filepath.forEach(x -> {
				try {
					DoSomething(x);
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			});
        }
 
        // Catch block to handle exceptions
        catch (IOException e) {
            throw new IOException("Directory Not Present!");
        }
        
		myWriter.close();

	}
	
	public static void DoSomething(Path path) throws IOException {
		//System.out.println(path.toString());
		
		File file = new File(path.toString());
		boolean isFile;
		
		if (file.isFile()) {
			//myWriter.write(path.toString() + "\r\n" );
			
			FileReader fr = new FileReader(file); // reads the file
			BufferedReader br = new BufferedReader(fr); // creates a buffering character input stream

			String line;
			Integer row=1;

			while ((line = br.readLine()) != null) {
				for (String k : keywords) {
					if (line.toLowerCase().contains(k.toLowerCase())) {
						myWriter.write(path.toString() + "," + row + "," + k + "," + line + "\r\n" );
					}

				}
				row++;
			}
			fr.close(); // closes the stream and release the resource
			
			
			
		}; 
		
	}

}
