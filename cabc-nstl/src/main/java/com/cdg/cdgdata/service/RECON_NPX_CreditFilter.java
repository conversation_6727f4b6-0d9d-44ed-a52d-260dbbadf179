package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RECON_NPX_CreditFilter {

	public static ArrayList<String> GetFiles() {
		ArrayList<String> list = new ArrayList<String>();

		File folder = new File(Constants.FolderPath + "/NPX/");
		File[] listOfFiles = folder.listFiles();

		for (File file : listOfFiles) {
			if (file.isFile()) {
				list.add(file.getName());
			}
		}

		return list;
	}

	public static ArrayList<String> ReadNPXRecon(String filename) {

		ArrayList<String> result = new ArrayList<String>();

		try {
			File file = new File(Constants.FolderPath + "/NPX/" + filename); // creates a new file instance
			FileReader fr = new FileReader(file); // reads the file
			BufferedReader br = new BufferedReader(fr); // creates a buffering character input stream

			String line;
			while ((line = br.readLine()) != null) {
				result.add(line);
			}
			fr.close(); // closes the stream and release the resources

		} catch (IOException e) {
			e.printStackTrace();
		}

		return result;
	}

 

	public static void GetData() throws IOException {

		String filename = "";
		String npx_file = "";

		try {

			// Load files
			ArrayList<String> filelist = GetFiles();

			for (int i = 0; i < filelist.size(); i++) {
				filename = filelist.get(i);
				npx_file = filename;

				ArrayList<String> npxreconContent = new ArrayList<String>();
				npxreconContent = ReadNPXRecon(filename);

				// PrintToFile(cdgoutContent);

				FilterCredit(npxreconContent,npx_file);

			}

		} catch (Exception e) {
			log.error("Error occurred", e);
		}

	}

	public static void PrintToFile(ArrayList<String> input) throws IOException {

		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");

		for (int i = 0; i < input.size(); i++) {

			myWriter.write(input.get(i) + "\r\n");

		}

		myWriter.close();

	}

	public static void FilterCredit( ArrayList<String> npxrecon, String npx_file) throws IOException {


		FileWriter myWriter = new FileWriter(Constants.FolderPath + "/NPX/RESULT/" + npx_file);

		String npx_rec = "";
 

		for (int i = 0; i < npxrecon.size(); i++) {
 
			npx_rec = npxrecon.get(i).toString();
 
			  if (npx_rec.contains("H,STDRPT01") || npx_rec.contains("Scheme Credit/Debit") || npx_rec.substring(0,1).equals("T") )
			  {
				  myWriter.write(npx_rec + "\r\n"); 
			  }

		}

		myWriter.close();

	}
	
	
 
	

}
