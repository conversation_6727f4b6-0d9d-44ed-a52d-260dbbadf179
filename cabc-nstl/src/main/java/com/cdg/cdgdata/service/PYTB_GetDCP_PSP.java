package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PYTB_GetDCP_PSP {

	public static void GetData() throws IOException {

		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");

		try {
			Connection con = Helper.GetConnection("ptccpro_dcp");
			Statement stmt = con.createStatement();

			// Load data
			ArrayList<String[]> list = Helper.ReadDataSource("PYTB_GETDCP_PSP.txt");
			int total = list.size();

			int chunk = 1000;
			int cnt = 0;
			String psp = "";
			for (int counter = 0; counter < list.size(); counter++) {

				cnt++;
				psp = "'" + list.get(counter)[0] + "'," + psp;
				if (chunk == cnt || counter + 1 == total) {

					psp = psp.substring(0, psp.length() - 1);

					
						
					  String sqlString = new StringBuilder()
					  .append("SELECT BOOKING_REF, REQUESTED_AMOUNT, TXN_TYPE, TXN_STATE, PARENT_TXN_ID, TXN_ID, PROVIDER_REF FROM pytb_wd_txn "
					  )
					  //.append("WHERE TXN_TYPE IN ('capture-authorization' )  ")
					  .append("WHERE ( PARENT_TXN_ID in (" + psp + ") OR TXN_ID in (" + psp + ") )").toString();

					  //.append("AND TXN_TYPE IN ('capture-authorization' )  ").toString();
					  
					  //.append("AND PARENT_TXN_ID in (" + psp + ") ").toString();

						 
					 
					 

					System.out.println(String.valueOf(counter) + " " +  sqlString);

					ResultSet rs = stmt.executeQuery(sqlString);
					ResultSetMetaData rsmd = rs.getMetaData();

					int columnsNumber = rsmd.getColumnCount();

					String columns = "";
					String records = "";

					while (rs.next()) {
						if (columns == "") {
							for (int i = 1; i < columnsNumber + 1; i++) {
								columns += rsmd.getColumnName(i) + "\t";
							}
							;
							myWriter.write(columns + "\r\n");
						}

						for (int i = 1; i < columnsNumber + 1; i++) {

							records += rs.getString(i) + "\t";
						}
						;
						myWriter.write(records + "\r\n");
						records = "";
					}
					;

					psp = "";
					cnt = 0;
				}

			}

			myWriter.close();
			con.close();

		} catch (Exception e) {
			log.error("Error occurred", e);
		}

	}

}
