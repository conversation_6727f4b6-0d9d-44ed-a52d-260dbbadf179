package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ESC_GetFirst3Taxi {


	public static void GetData() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
	
			try{		  	  
				  Connection con= Helper.GetConnection("ptccpro");    
				  Statement stmt=con.createStatement();  
				  
				 //Load data
				 ArrayList<String[]> list = Helper.ReadDataSource("ESC_First3Taxi.txt");

				  
			      for (int counter = 0; counter < list.size(); counter++) { 
			    	  
			    	  String sqlString = new StringBuilder()
			    	            .append("SELECT * FROM esc_payment_transaction P ")
			    	            .append("WHERE vehicle_id = '" + list.get(counter)[0] + "' ")
			    	           .append(" AND src = 'ABL' ")
			    	            //.append(" and payment_method = 'NPX' ")
			    	           //.append("and log_dt >= TO_DATE(\'01/09/2023 12:00:00\',\'DD/MM/YYYY HH24:MI:SS\')")
			    	            .append("order by p.log_dt desc ")
			    	            .append("FETCH FIRST 2 ROWS ONLY")
			    	            .toString();
	 				  
			    	  ResultSet rs=stmt.executeQuery(sqlString);
					  ResultSetMetaData rsmd = rs.getMetaData();

					  int columnsNumber = rsmd.getColumnCount();
				
					  String columns = "";
					  String records = "";

					  log.info("Value: {}", sqlString);
					  
					  while(rs.next())
					  {     
						  if(columns=="")
						  {
							  for (int i = 1; i < columnsNumber+1; i++) {
								  columns+=rsmd.getColumnName(i) + "\t";    
								};
								myWriter.write(columns + "\r\n");
						  }
						  

						  for (int i = 1; i < columnsNumber+1; i++) {

							   records+=rs.getString(i) + "\t";
						  };
						  myWriter.write(records + "\r\n");
						  records="";
					  };
				    	  
			    	  
			      }   	
				


				  

				  myWriter.close();
				  con.close();
				  
				  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	
	
}
