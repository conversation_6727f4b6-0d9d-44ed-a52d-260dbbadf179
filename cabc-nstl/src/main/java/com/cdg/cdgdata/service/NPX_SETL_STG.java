package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NPX_SETL_STG {

	//public static ArrayList<String> npxReports = new ArrayList<String>();
	public static ArrayList<String> setlTIDSTAN = new ArrayList<String>();
	public static ArrayList<String> stgTIDSTAN = new ArrayList<String>();
	public static ArrayList<String> discrepancy = new ArrayList<String>();

	public static void GetData() throws IOException {
				

		
		String[] npxReports = { //"NETSNPX_20230707.CSV", 
								"NETSNPX_20230707.CSV",
								};
		
		
		for (String i : npxReports) {
				GetNPXStage(i);
				//GetSetle(i);
				CheckDiscrepancy(i);
		}
		
		GetDetails();
		
			 
	}
	
	public static void GetDetails() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		
		String TID = "";
		String STAN = "";
		String FILE_ID = "";
		String CARD_TYPE = "";
		String JOB_NO = "";
		
		try{
			
			  Connection con= Helper.GetConnection("ptccpro");    
			  Statement stmt=con.createStatement();  
			  Statement stmt2=con.createStatement(); 
			  
				for (int i = 0; i < discrepancy.size(); i++) {
					System.out.println(discrepancy.get(i));
					String value = discrepancy.get(i);
					String[] details = value.split(",");
					TID = details[0];
					STAN = details[1];
					FILE_ID = details[2];
					String records = "";
					
			    	  String sqlString = new StringBuilder()
			    	            .append("SELECT TERMINAL_ID, STAN, CAN_NO, financial_institution_id,  FILE_ID FROM cn2_recon.esc_recon_setl_netsnpx_stg P ")
			    	            .append("WHERE FILE_ID = '" + FILE_ID + "'")
			    	            .append(" AND TERMINAL_ID = '" + TID + "' " + " AND STAN = '" + STAN + "' ")
			    	            .toString();
					  			    	  
			          log.info("Value: {}", sqlString);
			          		    	  
			    	  ResultSet rs=stmt.executeQuery(sqlString);
			    	  ResultSetMetaData rsmd = rs.getMetaData();
			    	  int columnsNumber = rsmd.getColumnCount();
			    	  
					  while(rs.next())
					  {			  
						  //CARD_TYPE = rs.getString(1);
						  for (int p = 1; p < columnsNumber+1; p++) {
							   records+=rs.getString(p) + "\t";
						  };
						  
						  //myWriter.write(records + "\r\n");
						  //records="";
					  }
					  
					   
					  
			    	  String sqlString2 = new StringBuilder()
			    	            .append("SELECT JOB_NO,	TRANS_AMOUNT,	TRANS_DT,	FARE_GST,	FARE_ADMIN,	FARE_AMOUNT,	VEHICLE_ID,	REMARkS, TID, system_trace_audit_no, APPROVAL_CODE FROM esc_payment_transaction ")
			    	            .append(" WHERE tid = '" + TID + "' " + " AND system_trace_audit_no = '" + STAN + "' ")
			    	            .append(" AND TRANS_TYPE in ('0210','0230' ) " )
			    	            .append(" AND PAYMENT_METHOD = 'NPX'" )
			    	            .toString();
					  			    	  
			          log.info("Value: {}", sqlString2);
			          		    	  
			    	  ResultSet rs2=stmt2.executeQuery(sqlString2);
			    	  ResultSetMetaData rsmd2 = rs2.getMetaData();
			    	  int columnsNumber2 = rsmd2.getColumnCount();
			    	  
					  while(rs2.next())
					  {			  
						  //JOB_NO = rs2.getString(1);
						  for (int q = 1; q < columnsNumber2+1; q++) {

							   records+=rs2.getString(q) + "\t";
						  };
						  
						  myWriter.write(records + "\r\n");
						  records="";
					  }
					
					 
				}
		    	  



			
			  myWriter.close();
			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
		

		
	}

	public static void CheckDiscrepancy(String fileid) {
		
		for (int i = 0; i < stgTIDSTAN.size(); i++) {
			boolean exist = false;
			String TIDSTAN = stgTIDSTAN.get(i);
			exist = setlTIDSTAN.contains(TIDSTAN);

			if (exist == false) {
				discrepancy.add(TIDSTAN.substring(0,8) + "," + TIDSTAN.substring(8,14) + "," + fileid + ",");
			}
		
		}
		
	}
	
	public static void GetNPXStage(String fileid) {
		
		stgTIDSTAN.clear();
		
		try{
			
			  Connection con= Helper.GetConnection("ptccpro");    
			  Statement stmt=con.createStatement();  
		    	  
		    	  String sqlString = new StringBuilder()
		    	            .append("SELECT TERMINAL_ID||STAN FROM cn2_recon.esc_recon_setl_netsnpx_stg P ")
		    	            .append("WHERE FILE_ID = '" + fileid + "'")
		    	            .append(" AND financial_institution_id in ('MasterCard', 'VISA') ")
		    	            //.append(" AND financial_institution_id in ('AMEX') ")
		    	            .toString();
 				  			    	  
		          log.info("Value: {}", sqlString);
		          		    	  
		    	  ResultSet rs=stmt.executeQuery(sqlString);

				  while(rs.next())
				  {			  
					  stgTIDSTAN.add(rs.getString(1));
				  }
			

			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
	}
	
	
	public static void GetSetle(String fileid) {
		
		setlTIDSTAN.clear();
		
		try{
			
			  Connection con= Helper.GetConnection("ptfspro");    
			  Statement stmt=con.createStatement();  
		    	  
		    	  String sqlString = new StringBuilder()
		    	            .append("SELECT TID||STAN FROM rctb_setl ")
		    	            .append("WHERE FILE_ID = '" + fileid + "' " )
		    	            .append("  and card_type in ('M','V','A','J')" )
		    	            //.append("  and card_type in ('A')" )
		    	            .toString();
 				  			    	  
		          log.info("Value: {}", sqlString);
		          		    	  
		    	  ResultSet rs=stmt.executeQuery(sqlString);

				  while(rs.next())
				  {			  
					  setlTIDSTAN.add(rs.getString(1));
				  }
			

			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
	}
}
