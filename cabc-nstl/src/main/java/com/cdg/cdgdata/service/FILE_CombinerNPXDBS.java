package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FILE_CombinerNPXDBS {
	
	final static String fileCombineNPXDBS = "/FileCombineNPXDBS/";
	final static String fileCombineNPXDBS_result = fileCombineNPXDBS + "Result/";
	
	public static void GetData() throws IOException {
		String filename = "";

		try {

			// Load files
			ArrayList<String> filelist = GetFiles();
			ArrayList<String> fileContent = new ArrayList<String>();

			for (int i = 0; i < filelist.size(); i++) {
				filename = filelist.get(i);

				File file = new File(Constants.FolderPath + fileCombineNPXDBS + filename); // creates a new file instance
				FileReader fr = new FileReader(file); // reads the file
				BufferedReader br = new BufferedReader(fr); // creates a buffering character input stream

				String line;
				while ((line = br.readLine()) != null) {
					fileContent.add(line);
				}
				fr.close(); // closes the stream and release the resource

			}
			
			String resultFile = Helper.GenerateFileName();
			FileWriter myWriter = new FileWriter(Constants.FolderPath + fileCombineNPXDBS_result + resultFile + ".txt");

			String file_rec = "";
	 

			for (int i = 0; i < fileContent.size(); i++) {
	 
				file_rec = fileContent.get(i).toString();
	 
				myWriter.write(file_rec + "\r\n"); 

			}

			myWriter.close();

		} catch (Exception e) {
			log.error("Error occurred", e);
		}
		
	}

	
	
	public static ArrayList<String> GetFiles() {
		ArrayList<String> list = new ArrayList<String>();

		File folder = new File(Constants.FolderPath + fileCombineNPXDBS);
		File[] listOfFiles = folder.listFiles();

		for (File file : listOfFiles) {
			if (file.isFile()) {
				list.add(file.getName());
			}
		}

		return list;
	}

	
	

}
