package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PYTB_ParentTxnBookingRef {
	
	public static void GetData() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");

		
		try{		  	  
			Connection con= Helper.GetConnection("ptccpro_dcp");  
			  Statement stmt=con.createStatement();  
			  
			 //Load data
			 ArrayList<String[]> list = Helper.ReadDataSource("PYTB_PARENT_BOOKINGREF.txt");

			  
		      for (int counter = 0; counter < list.size(); counter++) { 
		    	  
		    	  String sqlString = new StringBuilder()
		    	            .append("SELECT * FROM pytb_wd_txn P ")
		    	            .append("WHERE Txn_id in ('" + list.get(counter)[2] + "') ")
		    	            .append("OR Parent_txn_id in ('" + list.get(counter)[2] + "') ")
		    	            .append("OR Booking_REF in ('" + list.get(counter)[3] + "') ")
		    	            .append("order by P.WD_TXN_ID")
		    	            .toString();
 				  
		    	  ResultSet rs=stmt.executeQuery(sqlString);
				  ResultSetMetaData rsmd = rs.getMetaData();

				  int columnsNumber = rsmd.getColumnCount();
			
				  String columns = "";
				  String records = "";

			
				  while(rs.next())
				  {     
					  if(columns=="")
					  {
						  myWriter.write("JOB_NO \t");
						  for (int i = 1; i < columnsNumber+1; i++) {
							  columns+=rsmd.getColumnName(i) + "\t";    
							};
							myWriter.write(columns + "\r\n");
					  }
					  
					  myWriter.write(list.get(counter)[1] + "\t");
					  for (int i = 1; i < columnsNumber+1; i++) {

						   records+=rs.getString(i) + "\t";
					  };
					  myWriter.write(records + "\r\n");
					  records="";
				  };
		    	  
		    	  
		      }   	
			


			  

			  myWriter.close();
			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	

}


