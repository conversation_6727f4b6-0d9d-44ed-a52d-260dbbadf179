package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Alipa_Type_Filter {

	public static ArrayList<String> GetFiles() {
		ArrayList<String> list = new ArrayList<String>();

		File folder = new File(Constants.FolderPath + "/ALIPAY/");
		File[] listOfFiles = folder.listFiles();

		for (File file : listOfFiles) {
			if (file.isFile()) {
				list.add(file.getName());
			}
		}

		return list;
	}

	public static ArrayList<String> ReadCDGOUT(String filename) {

		ArrayList<String> result = new ArrayList<String>();

		try {
			File file = new File(Constants.FolderPath + "/CABC/" + filename); // creates a new file instance
			FileReader fr = new FileReader(file); // reads the file
			BufferedReader br = new BufferedReader(fr); // creates a buffering character input stream

			String line;
			while ((line = br.readLine()) != null) {
				result.add(line);
			}
			fr.close(); // closes the stream and release the resources

		} catch (IOException e) {
			e.printStackTrace();
		}

		return result;
	}

	public static ArrayList<String> ReadRCTBSetl(String file_id) {

		ArrayList<String> result = new ArrayList<String>();

		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();

			String sqlString = new StringBuilder().append("SELECT job_no FROM rctb_setl ")
					.append("WHERE file_id = '" + file_id + "' ")
					// .append("WHERE job_no = '" + "727777081" + "' ")
					.toString();

			log.info("Value: {}", sqlString);

			ResultSet rs = stmt.executeQuery(sqlString);

			while (rs.next()) {
				result.add(rs.getString(1));
			}
			;

			con.close();

		} catch (SQLException e) {
			e.printStackTrace();
		}

		return result;
	}

	public static void GetData() throws IOException {

		String filename = "";
		String ali_file = "";

		try {

			// Load files
			ArrayList<String> filelist = GetFiles();

			for (int i = 0; i < filelist.size(); i++) {
				filename = filelist.get(i);

				ArrayList<String> cdgoutContent = new ArrayList<String>();
				cdgoutContent = ReadCDGOUT(filename);

			}

		} catch (Exception e) {
			log.error("Error occurred", e);
		}

	}

	public static void PrintToFile(ArrayList<String> input) throws IOException {

		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");

		for (int i = 0; i < input.size(); i++) {

			myWriter.write(input.get(i) + "\r\n");

		}

		myWriter.close();

	}

	public static void Compare(ArrayList<String> rctb, ArrayList<String> cdgout, String cabc_file) throws IOException {

		// String filename = Helper.GenerateFileName();
		String filename = cabc_file.replace("CABC", "CAB");
		FileWriter myWriter = new FileWriter(Constants.FolderPath + "/CABC/RESULT/" + filename.replace("txt", "txt"));  //filename.replace("txt", "csv"));

		String job = "";
		String card_no = "";
		String cdgout_rec = "";
  

		for (int i = 0; i < cdgout.size(); i++) {
			boolean exist = false;
			cdgout_rec = cdgout.get(i).toString();
			job = cdgout_rec.substring(3, 13).replaceFirst("^0*", "");
			card_no = cdgout_rec.substring(52, 68).replace(" ", "");

			exist = rctb.contains(job);

			if (exist == false) {
				
				  if (cdgout_rec.substring(0,3).equals("DTL") || cdgout_rec.substring(0,3).equals("HDR") || cdgout_rec.substring(0,3).equals("TRL")) 
				  {
					  //myWriter.write(job + "," + card_no + "\r\n"); 
					  myWriter.write(cdgout_rec + "\r\n");
				  }
				 

			}

		}

		myWriter.close();

	}

}
