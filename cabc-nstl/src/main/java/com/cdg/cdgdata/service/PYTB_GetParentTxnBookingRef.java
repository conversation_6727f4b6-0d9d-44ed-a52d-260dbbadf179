package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PYTB_GetParentTxnBookingRef {


	public static void GetData() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");

		
		try{		  	  

			  
			 //Load data
			 ArrayList<String[]> list = Helper.ReadDataSource("PYTB_JOBNO.txt");
			 StringBuilder jobList = new StringBuilder();
			 for (int counter = 0; counter < list.size(); counter++) {
				 jobList.append("'" + list.get(counter)[0] + "',");
			 }
	    	  
			 String jobs = jobList.toString();
			 jobs = jobs.substring(0,jobs.length()-1);
	    	  String sqlString = new StringBuilder()
	    	            .append("select j.JOB_NO, t.parent_txn_id, t.booking_ref, j.STATUS, j.LINK_JOB_NO_CN3 ")
	    	            .append("from pytb_wd_txn t, esc_job j ,esc_booking b ")
	    	            .append("where t.booking_ref = j.DCP_REQUEST_ID ")
	    	            .append("and b.BOOKING_ID = j.BOOKING_ID ")
	    	            .append("and j.JOB_NO in ( ")
	    	            .append(jobs)
	    	            .append(" )")
	    	            .toString();
			  
	    	  Connection con= Helper.GetConnection("ptccpro_dcp");  
			  Statement stmt=con.createStatement();  
			  
	    	  ResultSet rs=stmt.executeQuery(sqlString);
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();
		
			  String columns = "";
			  String records = "";

		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {
					  for (int i = 1; i < columnsNumber+1; i++) {
						  columns+=rsmd.getColumnName(i) + "\t";    
						};
						myWriter.write(columns + "\r\n");
				  }
				  

				  for (int i = 1; i < columnsNumber+1; i++) {

					   records+=rs.getString(i) + "\t";
				  };
				  myWriter.write(records + "\r\n");
				  records="";
			  };
		    	  
		    	  

			


			  

			  myWriter.close();
			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	
	
}
