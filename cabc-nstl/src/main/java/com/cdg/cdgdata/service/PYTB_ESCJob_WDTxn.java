package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PYTB_ESCJob_WDTxn {
	
	public static void GetData() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		
		try{		  	  
			Connection con= Helper.GetConnection("ptccpro_dcp");  
			  Statement stmt=con.createStatement();  
			  
			 //Load data
			 ArrayList<String[]> list = Helper.ReadDataSource("PYTB_ESCJob_WDTxn.txt");
			 
			 ArrayList<String[]> job_dcpid = new ArrayList<String[]>();

		      for (int counter = 0; counter < list.size(); counter++) { 
		    	  
		    	  String sqlString = new StringBuilder()
		    	            .append("select j.JOB_NO, j.DCP_REQUEST_ID from  esc_job j ")
		    	            .append(" where j.JOB_NO = '" + list.get(counter)[0] + "' ")
		    	            .append(" and link_job_no_cn3 is null ")
		    	            .toString();
 				  
		    	  //log.info("Value: {}", sqlString);
		    	  ResultSet rs=stmt.executeQuery(sqlString);
				  while(rs.next())
				  {   
					  String rec = rs.getString(1) + "-" + rs.getString(2);
					  String[] data = null;
					  data = rec.split("-"); 
					  job_dcpid.add(data);
				  }
		    	  
		      }
			  
		      for (int counter = 0; counter < job_dcpid.size(); counter++) { 
		    	  
		    	  String sqlString = new StringBuilder()
		    	            .append("select t.* from pytb_wd_txn t ")
		    	            .append(" where t.booking_ref  = '" + job_dcpid.get(counter)[1] + "' ")
		    	            .append("order by t.WD_TXN_ID")
		    	            .toString();
 				  
				  log.info("Value: {}", sqlString);
				  
		    	  ResultSet rs=stmt.executeQuery(sqlString);
				  ResultSetMetaData rsmd = rs.getMetaData();

				  int columnsNumber = rsmd.getColumnCount();
			
				  String columns = "";
				  String records = "";

			
				  while(rs.next())
				  {     
					  if(columns=="")
					  {
						  myWriter.write("JOB_NO \t");
						  for (int i = 1; i < columnsNumber+1; i++) {
							  columns+=rsmd.getColumnName(i) + "\t";    
							};
							myWriter.write(columns + "\r\n");
					  }
					  
					  myWriter.write(job_dcpid.get(counter)[0] + "\t");
					  for (int i = 1; i < columnsNumber+1; i++) {

						   records+=rs.getString(i) + "\t";
					  };
					  myWriter.write(records + "\r\n");
					  records="";
				  };
		    	  
		    	  
		      }   	
			


			  

			  myWriter.close();
			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	

}


