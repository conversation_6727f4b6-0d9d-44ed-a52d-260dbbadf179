package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FILE_Combiner {
	
	
	public static void GetData() throws IOException {
		String filename = "";

		try {

			// Load files
			ArrayList<String> filelist = GetFiles();
			ArrayList<String> fileContent = new ArrayList<String>();

			for (int i = 0; i < filelist.size(); i++) {
				filename = filelist.get(i);

				File file = new File(Constants.FolderPath + "/FileCombine/" + filename); // creates a new file instance
				FileReader fr = new FileReader(file); // reads the file
				BufferedReader br = new BufferedReader(fr); // creates a buffering character input stream

				String line;
				int row = 0;
				while ((line = br.readLine()) != null) {
					//fileContent.add(line + "|" + filename);
					if (row>0) {
						fileContent.add(line);
					}
					row++;
				}
				fr.close(); // closes the stream and release the resource

			}
			
			String resultFile = Helper.GenerateFileName();
			FileWriter myWriter = new FileWriter(Constants.FolderPath + "/FileCombine/RESULT/" + resultFile + ".txt");

			String file_rec = "";
	 

			for (int i = 0; i < fileContent.size(); i++) {
	 
				file_rec = fileContent.get(i).toString();
	 
				myWriter.write(file_rec + "\r\n"); 

			}

			myWriter.close();

		} catch (Exception e) {
			log.error("Error occurred", e);
		}
		
	}

	
	
	public static ArrayList<String> GetFiles() {
		ArrayList<String> list = new ArrayList<String>();

		File folder = new File(Constants.FolderPath + "/FileCombine/");
		File[] listOfFiles = folder.listFiles();

		for (File file : listOfFiles) {
			if (file.isFile()) {
				list.add(file.getName());
			}
		}

		return list;
	}

	
	
	
	/*
	 * public static void GetData() throws IOException {
	 * 
	 * //String data = null; String filename = Helper.GenerateFileName(); FileWriter
	 * myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
	 * 
	 * try{ myWriter.
	 * write("FILE_ID,Record Type,PRODUCT,TRANSACTION_CD,TRANSACTION_DT,FINANCIAL_INSTITUTI,CORPORATION_ID,ENTITY_02_I,ENTITY_03_ID,ENTITY_04_ID,ENTITY_05_ID,AMOUNT_01_NO,AMOUNT_02_NO,AMOUNT_03_NO,AMOUNT_04_NO,AMOUNT_05_NO,FEE_01_NO,FEE_02_NO,FEE_03_NO,FEE_04_NO,FEE_05_NO,DATE_01_DT,DATE_02_DT,DATE_03_DT,DATE_04_DT,DATE_05_DT,CARD_ISSUER_ID,REVERSAL_CD,RESPONSE_CD,ETC_01_TX,ORIG_TID,ORIG_TIMESTAMP,ORIG_SEQ_NO,ETC_05_TX,CAN_NO,SEQUENCE_NO,REFERENCE_TX,VOID_FL\r\n"
	 * ); File path = new File(Constants.FolderPath + "FileCombine\\"); File []
	 * files = path.listFiles(); for (int i = 0; i < files.length; i++){ if
	 * (files[i].isFile()){ //this line weeds out other directories/folders String
	 * fname = files[i].getName();
	 * 
	 * FileReader fr=new FileReader(files[i]); //reads the file BufferedReader
	 * br=new BufferedReader(fr); //creates a buffering character input stream
	 * 
	 * String line; while((line=br.readLine())!=null) { myWriter.write(fname + "," +
	 * line + "\r\n"); } fr.close(); //closes the stream and release the resources
	 * 
	 * log.info("Processing file: {}", files[i]); } }
	 * 
	 * myWriter.close();
	 * 
	 * 
	 * }catch(Exception e){ log.error("Error occurred", e);}
	 * 
	 * }
	 */

}
