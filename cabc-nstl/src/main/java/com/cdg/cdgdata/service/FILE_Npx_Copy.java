package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;


@Slf4j
public class FILE_Npx_Copy {
	

	public static void GetData() throws IOException {
		
		String lastfile = "";
		String filename = "";
		String filecontroller = "FILE_NPX_COPY.txt";
		
	    LocalDate curDate = LocalDate.now();
	    
		int year;
		int month;
		int day;
		
		
		
		try{
			
			File file=new File(Constants.FolderPath + filecontroller);      
			FileReader fr=new FileReader(file);   //reads the file  
			BufferedReader br=new BufferedReader(fr);  //creates a buffering character input stream  
	 
			String line;  
			while((line=br.readLine())!=null)  
			{  
				lastfile = line;
			}  
			fr.close();    //closes the stream and release the resources  

			year = Integer.parseInt(lastfile.substring(31,35));
		    month = Integer.parseInt(lastfile.substring(35,37));
		    day = Integer.parseInt(lastfile.substring(37,39));
		    
		    LocalDate lastfileday = LocalDate.of(year, month, day);
		    Period period = Period.between(curDate, lastfileday);
		    int diff = Math.abs(period.getDays());
		    
		    
		    for (int counter = 1; counter <= diff-1; counter++) { 
			    LocalDate newDate = LocalDate.of(year, month, day).plusDays(counter);  
			    
			    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
			    String fileday = newDate.format(formatter);
	            
	 
			    filename = "EFTPOS_CASHCARD_C3631_STDRPT01_" + fileday + "_NEW.csv";
			    
		        Path source = Paths.get("W:\\NPX\\archive\\" + filename);
			    Path destination = Paths.get("C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\NPX report for MinShi\\" + filename);

			    if(Files.exists(source) && !Files.exists(destination))
			    {
			        Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING);
				     
				    FileWriter myWriter = new FileWriter(Constants.FolderPath + filecontroller);
				    myWriter.write(filename);
				    myWriter.close();
				    
				    log.info("Value: {}", filename);
			    }

		    }
		    

		
		}catch(Exception e){ log.error("Error occurred", e);}

}

}
