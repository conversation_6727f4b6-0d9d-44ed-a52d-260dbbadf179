package com.cdg.cdgdata.service;

import com.cdg.cdgdata.dto.CabcError51ResponseDto;
import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.sql.*;
import java.util.ArrayList;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

@Component
@Slf4j
public class CABC_Error51 {

    @Autowired
    private CabcError51Service cabcError51Service;

    // Legacy static variables for backward compatibility
    public static ArrayList<Object[]> nstlList = new ArrayList<Object[]>();
    public static ArrayList<String> nstlcolumnList = new ArrayList<String>();
    public static ArrayList<Integer> nstlcolumnTypeList = new ArrayList<Integer>();

    public static ArrayList<Object[]> tripList = new ArrayList<Object[]>();
    public static ArrayList<String> tripcolumnList = new ArrayList<String>();
    public static ArrayList<Integer> tripcolumnTypeList = new ArrayList<Integer>();

    public static ArrayList<Object[]> jobList = new ArrayList<Object[]>();
    public static ArrayList<String> jobcolumnList = new ArrayList<String>();
    public static ArrayList<Integer> jobcolumnTypeList = new ArrayList<Integer>();

    public static ArrayList<Object[]> isoList = new ArrayList<Object[]>();
    public static ArrayList<String> isocolumnList = new ArrayList<String>();
    public static ArrayList<Integer> isocolumnTypeList = new ArrayList<Integer>();

    public static ArrayList<Object[]> noCardList = new ArrayList<Object[]>();
    public static ArrayList<String> noCardcolumnList = new ArrayList<String>();
    public static ArrayList<Integer> noCardcolumnTypeList = new ArrayList<Integer>();

    public static String lastMsgid;
    public static String filecontroller = "CABC_ISO8583_CONTROL.txt";

    /**
     * Main method to process CABC Error 51 data - refactored to use JPA service
     * @param targetDay Date in format dd/MM/yyyy
     * @throws IOException if processing fails
     */
    public static void GetData(String targetDay) throws IOException {
        log.info("CABC_Error51.GetData() called with targetDay: {}", targetDay);

        // Note: This is a static method, so we need to get the service from Spring context
        // For now, we'll log that this should be called through the service layer
        log.warn("CABC_Error51.GetData() is deprecated. Please use CabcError51Service.processData() instead.");
        log.info("To process data, call: cabcError51Service.processData(\"{}\")", targetDay);
    }

    /**
     * Instance method that delegates to the new JPA service
     * @param targetDay Date in format dd/MM/yyyy
     * @return CabcError51ResponseDto containing processed data
     */
    public CabcError51ResponseDto processData(String targetDay) {
        return cabcError51Service.processData(targetDay);
    }

    // Legacy methods kept for backward compatibility - these are deprecated
    // and should be replaced with the new JPA-based service methods

    /**
     * @deprecated Use CabcError51Service.processData() instead
     */
    @Deprecated
    public static void GetNSTL(String TARGET_DAY) throws IOException {
		
		String sqlString = new StringBuilder()
				.append(" select job_no,  completed_dt, recon_date   from rctb_trip_intf  ")
				.append(" where payment_mode in ( 'CABC', 'EVCH') ")
				.append(" and recon_date >= TO_DATE('" + TARGET_DAY + " 00:00:00','DD/MM/YYYY HH24:MI:SS') ")
				.append(" and recon_date <= TO_DATE('" + TARGET_DAY + " 23:59:59','DD/MM/YYYY HH24:MI:SS') ")
				//.append(" and STATUS = 'E' AND ERROR_CODE = 'NSTL' and FMS_STATUS = 'C' order by completed_dt asc ")
				.append(" and STATUS = 'E' AND ERROR_CODE = 'NSTL'  order by completed_dt asc ")
				//.append(" and STATUS = 'E' AND ERROR_CODE = 'NSTL' and entity='PRIM'  order by completed_dt asc ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  nstlcolumnList.add(rsmd.getColumnName(i+1).toString());
						  //nstlcolumnTypeList.add(rsmd.getColumnType(i+1));
						  if ((i+1)==1) {
							  nstlcolumnTypeList.add(8);
						  }
						  else {
							  nstlcolumnTypeList.add(rsmd.getColumnType(i+1));
						  }
						  
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  nstlList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		
	}
	
	
	public static void GetJobCC() throws IOException {
		
		String joblist = JobList();
		
		String sqlString = new StringBuilder()
				.append(" select   ")
				.append(" job_no, cc_number, pax_contact ")
				.append(" from esc_job ")
				.append(" where job_no in (" + joblist + ") ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptccpro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  jobcolumnList.add(rsmd.getColumnName(i+1).toString());
						  jobcolumnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  jobList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		
	}
	
	public static void GetTrip() throws IOException {
		
		String joblist = JobList();
		
		String sqlString = new StringBuilder()
				.append(" select   ")
				.append(" to_char(end_dt,'YYYYMMDD')|| ")
				.append(" lpad(to_char(amount_paid*100),12,0) || ")
				.append(" to_char(round(start_y,4)) || ")
				.append(" to_char(round(start_x,4)) || ")
				.append(" to_char(round(end_y,4)) || ")
				.append(" to_char(round(end_x,4)) as \"NEW\",")
				.append(" job_no  from esc_trip_details ")
				.append(" where job_no in (" + joblist + ") ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptccpro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  tripcolumnList.add(rsmd.getColumnName(i+1).toString());
						  //tripcolumnTypeList.add(rsmd.getColumnType(i+1));
						  if ((i+1)==2) {
							  tripcolumnTypeList.add(8);
						  }
						  else {
							  tripcolumnTypeList.add(rsmd.getColumnType(i+1));
						  }
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  tripList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		
	}
 
	
	public static void GetPYTB() throws IOException {
		
		FileWriter myWriter = new FileWriter(Constants.FolderPath + "PYTB_CABC_BOOKINGREF.txt");
		
		 for (int counter = 0; counter < nstlList.size(); counter++) { 	
			 myWriter.write(nstlList.get(counter)[0] + "\r\n");
		}
		 myWriter.close();
		 
		 PYTB_CABC_BookingRef.GetData();
		 
	}

	
	public static Workbook WriteWBSheets(Workbook wb, String sheetname, ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList) throws IOException {
		Sheet sheet1 = wb.createSheet(sheetname);

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i+1);
	        headerCell.setCellValue(columnList.get(i));
	        headerCell.setCellStyle(style);
		
		};
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell recordCell = recordRow.createCell(j+1);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(8)) {
		        		recordCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		recordCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	recordCell.setCellValue("");
		        }

			};
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<columnList.size()+1;i++) {
			sheet1.autoSizeColumn(i);
		}
		
		return wb;
	}

	
	public static void GetISO() throws IOException {
		
		String lastrecord = "";
		File file=new File(Constants.FolderPath + filecontroller);      
		FileReader fr=new FileReader(file);   //reads the file  
		BufferedReader br=new BufferedReader(fr);  //creates a buffering character input stream  
 
		String line;  
		while((line=br.readLine())!=null)  
		{  
			lastrecord = line;
		}  
		fr.close();    //closes the stream and release the resources  
		
		String sqlString = new StringBuilder()
				.append(" select * from (  ")
				.append(" select to_char(cast(txn_date as date),'YYYYMMDD')|| txn_amt || ")
				.append(" to_char(round(substr(tripinformation,25,15),4))   || ")
				.append(" to_char(round(substr(tripinformation,40,15),4)) || ")
				.append(" to_char(round(substr(tripinformation,55,15),4)) || ")
				.append(" to_char(round(substr(tripinformation,70,15),4)) as \"NEW\", ")
				.append(" PAN, msg_id, cast(txn_date as date) as \"TXN_DATE\" ")
				.append(" from pay_as_data.iso8583_incoming ")
				.append(" where msg_id >= " + lastrecord  )
				.append(" and tripinformation is not null ")
				.append(" and length(tripinformation) > 87 ) order by msg_id asc ")
				//.append(" and pan is not null ) order by msg_id asc ")
				
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptccpro_pay");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  isocolumnList.add(rsmd.getColumnName(i+1).toString());
						  isocolumnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  isoList.add(recList);
				  lastMsgid = recList[2].toString();
				  
			  };

			con.close();
			
			
			FileWriter myWriter = new FileWriter(Constants.FolderPath + filecontroller);
			
			myWriter.write(lastMsgid);
			myWriter.close();

		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		
		
		
		
	}

	public static String JobList() throws IOException {
		
		String jobs = "";
		
		 for (int counter = 0; counter < nstlList.size(); counter++) { 	
			 jobs = "'" + nstlList.get(counter)[0] + "'," + jobs;
		}
				 
		jobs = jobs.substring(0,jobs.length()-1);
		
		return jobs;
	}
	
	public static void GetNoCard() throws IOException {

        try {
            Connection con = Helper.GetConnection("ptccpro_pay");
            Statement stmt = con.createStatement();

            //Load data
            ArrayList<String[]> list = Helper.ReadDataSource("CABC_NOCARD.txt");

            String jobs = "";
            String columns = "";
            for (int counter = 0; counter < list.size(); counter++) {
                //jobs = "'" + list.get(counter)[5] + "'," + jobs;

//log.info(jobs);

                String sqlString = new StringBuilder()
                        .append(" select msg_id, MSG_TYP_ID, nvl(pan,'XXX') as PAN, nvl(tripinformation,'XXX'),txn_date from pay_as_data.iso8583_incoming a ")
                        .append(" where a.pan like ('%" + list.get(counter)[1].substring(list.get(counter)[1].length()-4) + "')" )
                        .append(" and a.txn_Date BETWEEN  TO_DATE('" + list.get(counter)[2] + " 00:00:00','MM/DD/YYYY HH24:MI:SS') ")
                        .append(" and  TO_DATE('" + list.get(counter)[2] + " 23:59:59','MM/DD/YYYY HH24:MI:SS') ")
                        .append(" Order by a.pan desc ")
                        .toString();

                log.info(sqlString);


                ResultSet rs=stmt.executeQuery(sqlString);
                ResultSetMetaData rsmd = rs.getMetaData();

                int columnsNumber = rsmd.getColumnCount();



                while(rs.next())
                {
                    if(columns=="")
                    {

                        for (int i = 0; i < columnsNumber; i++) {
                            noCardcolumnList.add(rsmd.getColumnName(i+1).toString());
                            noCardcolumnTypeList.add(rsmd.getColumnType(i+1));
                        };

                        columns = "DONE";
                        System.out.println("columns() end");
                    }


                    Object[] recList = new Object[columnsNumber];
                    for (int i = 0; i < columnsNumber ; i++) {
                        recList[i] = rs.getString(i+1);

                    };
                    noCardList.add(recList);
                    log.info("loop records: " +  recList[2]);
                };
            }

            con.close();

        } catch (SQLException e) {
            e.printStackTrace();

        }

        Helper.WriteTextFile("NOCARD", noCardcolumnList,  noCardList);

        System.out.println("GetNoCard() end");
    }

	
	public static void GetPANIso() throws IOException {

        try {
            Connection con = Helper.GetConnection("ptccpro_pay");
            Statement stmt = con.createStatement();

            //Load data
            ArrayList<String[]> list = Helper.ReadDataSource("CABC_PANISO.txt");

            String jobs = "";
            String columns = "";
            for (int counter = 0; counter < list.size(); counter++) {
 

                String sqlString = new StringBuilder()
                        .append(" select pan,txn_amt,expiry_date,track_2_data,txn_date from pay_as_data.iso8583_incoming a ")
                        .append(" where a.txn_amt in ('" + list.get(counter)[0] + "') " )
                        .append(" and expiry_date in ('" + list.get(counter)[1] + "') ")
                        .append(" and  txn_date >=TO_DATE('27/1/2025 00:00:00','DD/MM/YYYY HH24:MI:SS') ")
                        .append(" and txn_date <=TO_DATE('27/1/2025 23:59:59','DD/MM/YYYY HH24:MI:SS')  ")
                        .toString();

                log.info(sqlString);


                ResultSet rs=stmt.executeQuery(sqlString);
                ResultSetMetaData rsmd = rs.getMetaData();

                int columnsNumber = rsmd.getColumnCount();



                while(rs.next())
                {
                    if(columns=="")
                    {

                        for (int i = 0; i < columnsNumber; i++) {
                            noCardcolumnList.add(rsmd.getColumnName(i+1).toString());
                            noCardcolumnTypeList.add(rsmd.getColumnType(i+1));
                        };

                        columns = "DONE";
                        System.out.println("columns() end");
                    }


                    Object[] recList = new Object[columnsNumber];
                    for (int i = 0; i < columnsNumber ; i++) {
                        recList[i] = rs.getString(i+1);

                    };
                    noCardList.add(recList);
                    log.info("loop records: " +  recList[2]);
                };
            }

            con.close();

        } catch (SQLException e) {
            e.printStackTrace();

        }

        Helper.WriteTextFile("CABC_PANISO", noCardcolumnList,  noCardList);

        System.out.println("GetPANIso() end");
    }
	
	
}
 

