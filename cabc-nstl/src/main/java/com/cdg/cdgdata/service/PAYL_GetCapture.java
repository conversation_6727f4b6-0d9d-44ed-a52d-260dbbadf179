package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


@Slf4j
public class PAYL_GetCapture {

	public static ArrayList<Object[]> nstlList = new ArrayList<Object[]>();
	public static  ArrayList<String> nstlcolumnList = new ArrayList<String>();
	public static  ArrayList<Integer> nstlcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> tripList = new ArrayList<Object[]>();
	public static  ArrayList<String> tripcolumnList = new ArrayList<String>();
	public static  ArrayList<Integer> tripcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> pytblList = new ArrayList<Object[]>();
	public static  ArrayList<String> pytblcolumnList = new ArrayList<String>();
	public static  ArrayList<Integer> pytblcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> isoList = new ArrayList<Object[]>();
	public static  ArrayList<String> isocolumnList = new ArrayList<String>();
	public static  ArrayList<Integer> isocolumnTypeList = new ArrayList<Integer>();
	
	public static Workbook wbfinal = new XSSFWorkbook();
	

	public static void GetData(String TARGET_DAY) throws IOException {
		GetNSTL(TARGET_DAY);
		
		GetPYTB();
		//GetISO();
		
		WriteWBSheets(wbfinal, "NSTL", nstlcolumnList, nstlcolumnTypeList, nstlList);
		//WriteWBSheets(wbfinal, "TRIP", tripcolumnList, tripcolumnTypeList, tripList);
		//WriteWBSheets(wbfinal, "ISO", isocolumnList, isocolumnTypeList, isoList);
				
		String filename = Helper.GenerateFileName();
		try (OutputStream fileOut = new
		FileOutputStream(Constants.FolderPath + filename + ".xlsx")) { wbfinal.write(fileOut);
		wbfinal.close(); }
		
	    log.info("PAYL_GetCapture done.");

	}
	
	public static void GetNSTL(String TARGET_DAY) throws IOException {
		
		String sqlString = new StringBuilder()
				.append(" select job_no, completed_dt, recon_date   from rctb_trip_intf  ")
				.append(" where payment_mode in ( 'DBSPOF', 'PLAH5') ")
				.append(" and recon_date >= TO_DATE('" + TARGET_DAY + " 00:00:00','DD/MM/YYYY HH24:MI:SS') ")
				//.append(" and recon_date <= TO_DATE('" + TARGET_DAY + " 23:59:59','DD/MM/YYYY HH24:MI:SS') ")
				//.append(" and STATUS = 'E' AND ERROR_CODE = 'NSTL' and FMS_STATUS = 'C' order by completed_dt asc ")
				.append(" and STATUS = 'E' AND ERROR_CODE = 'NSTL'  order by completed_dt asc ")
				//.append(" and STATUS = 'E' AND ERROR_CODE = 'NSTL' and entity='PRIM'  order by completed_dt asc ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  nstlcolumnList.add(rsmd.getColumnName(i+1).toString());
						  //nstlcolumnTypeList.add(rsmd.getColumnType(i+1));
						  if ((i+1)==1) {
							  nstlcolumnTypeList.add(8);
						  }
						  else {
							  nstlcolumnTypeList.add(rsmd.getColumnType(i+1));
						  }
						  
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  nstlList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		
	}
	

	public static void GetTMTBCapture() throws IOException {
		
		FileWriter myWriter = new FileWriter(Constants.FolderPath + "PYTB_PAYL_BOOKINGREF.txt");
		
		 for (int counter = 0; counter < nstlList.size(); counter++) { 	
			 myWriter.write(nstlList.get(counter)[0] + "\r\n");
		}
		 myWriter.close();
		 
		 PYTB_PAYL_BookingRef.GetData();
		 
	}
	
	public static void GetPYTB() throws IOException {
		
		FileWriter myWriter = new FileWriter(Constants.FolderPath + "PYTB_PAYL_BOOKINGREF.txt");
		
		 for (int counter = 0; counter < nstlList.size(); counter++) { 	
			 myWriter.write(nstlList.get(counter)[0] + "\r\n");
		}
		 myWriter.close();
		 
		 PYTB_PAYL_BookingRef.GetData();
		 
	}

	
	public static Workbook WriteWBSheets(Workbook wb, String sheetname, ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList) throws IOException {
		Sheet sheet1 = wb.createSheet(sheetname);

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i+1);
	        headerCell.setCellValue(columnList.get(i));
	        headerCell.setCellStyle(style);
		
		};
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell recordCell = recordRow.createCell(j+1);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(8)) {
		        		recordCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		recordCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	recordCell.setCellValue("");
		        }

			};
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<columnList.size()+1;i++) {
			sheet1.autoSizeColumn(i);
		}
		
		return wb;
	}


	public static String JobList() throws IOException {
		
		String jobs = "";
		
		 for (int counter = 0; counter < nstlList.size(); counter++) { 	
			 jobs = "'" + nstlList.get(counter)[0] + "'," + jobs;
		}
				 
		jobs = jobs.substring(0,jobs.length()-1);
		
		return jobs;
	}
	
}
 

