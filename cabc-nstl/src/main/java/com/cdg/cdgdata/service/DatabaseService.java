package com.cdg.cdgdata.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

// Temporarily disabled - conflicts with SimpleDatabaseService
// @Service
@Slf4j
public class DatabaseService {

    private final Map<String, JdbcTemplate> jdbcTemplates = new HashMap<>();

    @Autowired
    public DatabaseService(
            @Qualifier("ptfsproJdbcTemplate") JdbcTemplate ptfsproJdbcTemplate,
            @Qualifier("ptfsproIbsJdbcTemplate") JdbcTemplate ptfsproIbsJdbcTemplate,
            @Qualifier("ptccproJdbcTemplate") JdbcTemplate ptccproJdbcTemplate,
            @Qualifier("ptccproDcpJdbcTemplate") JdbcTemplate ptccproDcpJdbcTemplate,
            @Qualifier("ptccproTmJdbcTemplate") JdbcTemplate ptccproTmJdbcTemplate,
            @Qualifier("ptccproPayJdbcTemplate") JdbcTemplate ptccproPayJdbcTemplate,
            @Qualifier("ptcnuat1JdbcTemplate") JdbcTemplate ptcnuat1JdbcTemplate,
            @Qualifier("ptdaproJdbcTemplate") JdbcTemplate ptdaproJdbcTemplate,
            @Qualifier("ptcnuat2JdbcTemplate") JdbcTemplate ptcnuat2JdbcTemplate,
            @Qualifier("ptdaproDcpJdbcTemplate") JdbcTemplate ptdaproDcpJdbcTemplate) {
        
        jdbcTemplates.put("PTFSPRO", ptfsproJdbcTemplate);
        jdbcTemplates.put("PTFSPRO_IBS", ptfsproIbsJdbcTemplate);
        jdbcTemplates.put("PTCCPRO", ptccproJdbcTemplate);
        jdbcTemplates.put("PTCCPRO_DCP", ptccproDcpJdbcTemplate);
        jdbcTemplates.put("PTCCPRO_TM", ptccproTmJdbcTemplate);
        jdbcTemplates.put("PTCCPRO_PAY", ptccproPayJdbcTemplate);
        jdbcTemplates.put("PTCNUAT1", ptcnuat1JdbcTemplate);
        jdbcTemplates.put("PTDAPRO", ptdaproJdbcTemplate);
        jdbcTemplates.put("PTCNUAT2", ptcnuat2JdbcTemplate);
        jdbcTemplates.put("PTDAPRO_DCP", ptdaproDcpJdbcTemplate);
    }

    /**
     * Get JdbcTemplate for a specific database
     * @param databaseName Database name (e.g., "PTFSPRO", "PTCCPRO", etc.)
     * @return JdbcTemplate for the specified database
     */
    public JdbcTemplate getJdbcTemplate(String databaseName) {
        JdbcTemplate template = jdbcTemplates.get(databaseName.toUpperCase());
        if (template == null) {
            throw new IllegalArgumentException("Unknown database: " + databaseName);
        }
        return template;
    }

    /**
     * Get Connection for a specific database (for backward compatibility)
     * @param databaseName Database name
     * @return Connection object
     * @throws SQLException if connection fails
     */
    public Connection getConnection(String databaseName) throws SQLException {
        JdbcTemplate template = getJdbcTemplate(databaseName);
        return template.getDataSource().getConnection();
    }

    /**
     * Legacy method to maintain compatibility with existing Helper.GetConnection calls
     * Maps the old connection logic to new database names
     */
    public Connection GetConnection(String connectionType) throws SQLException {
        String databaseName;
        
        switch (connectionType.toUpperCase()) {
            case "PTFSPRO":
            case "RECUSER":
                databaseName = "PTFSPRO";
                break;
            case "PTFSPRO_IBS":
            case "IBSUSER":
                databaseName = "PTFSPRO_IBS";
                break;
            case "PTCCPRO":
            case "CN2USER":
                databaseName = "PTCCPRO";
                break;
            case "PTCCPRO_DCP":
            case "DCP_USER":
                databaseName = "PTCCPRO_DCP";
                break;
            case "PTCCPRO_TM":
            case "CN2TMUSER":
                databaseName = "PTCCPRO_TM";
                break;
            case "PTCCPRO_PAY":
            case "PAY_AS_USER":
                databaseName = "PTCCPRO_PAY";
                break;
            case "PTCNUAT1":
                databaseName = "PTCNUAT1";
                break;
            case "PTDAPRO":
                databaseName = "PTDAPRO";
                break;
            case "PTCNUAT2":
            case "CN2TMAPPS":
                databaseName = "PTCNUAT2";
                break;
            case "PTDAPRO_DCP":
                databaseName = "PTDAPRO_DCP";
                break;
            default:
                log.warn("Unknown connection type: {}, defaulting to PTFSPRO", connectionType);
                databaseName = "PTFSPRO";
                break;
        }
        
        return getConnection(databaseName);
    }

    /**
     * Execute a query and return the result count
     * @param databaseName Database name
     * @param sql SQL query
     * @return Number of rows affected/returned
     */
    public int executeQuery(String databaseName, String sql) {
        try {
            JdbcTemplate template = getJdbcTemplate(databaseName);
            return template.update(sql);
        } catch (Exception e) {
            log.error("Error executing query on database {}: {}", databaseName, sql, e);
            throw new RuntimeException("Database query failed", e);
        }
    }

    /**
     * Test database connectivity
     * @param databaseName Database name
     * @return true if connection is successful
     */
    public boolean testConnection(String databaseName) {
        try {
            JdbcTemplate template = getJdbcTemplate(databaseName);
            template.execute("SELECT 1 FROM DUAL");
            log.info("Database connection test successful for: {}", databaseName);
            return true;
        } catch (Exception e) {
            log.error("Database connection test failed for: {}", databaseName, e);
            return false;
        }
    }
}
