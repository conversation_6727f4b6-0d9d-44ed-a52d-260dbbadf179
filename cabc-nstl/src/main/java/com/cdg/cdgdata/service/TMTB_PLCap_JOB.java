package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


@Slf4j
public class TMTB_PLCap_JOB {


	public static void GetData() throws IOException {
		
 
		ArrayList<Object[]> resultList = new ArrayList<Object[]>();
		ArrayList<String> columnList = new ArrayList<String>();
		ArrayList<Integer> columnTypeList = new ArrayList<Integer>();
		
		try {
			Connection con = Helper.GetConnection("ptccpro_rst");
			Statement stmt = con.createStatement();

			 //Load data
			 ArrayList<String[]> list = Helper.ReadDataSource("TMTB_PLCAP_JOB.txt");
			 int total = list.size();
			 
			 int chunk = 1000;
			 int cnt = 0;
			 String jobs = "";
			 String columns = "";
			 for (int counter = 0; counter < list.size(); counter++) { 
				 
				 cnt++;
				 jobs = "'" + list.get(counter)[0] + "'," + jobs;
				 if(chunk==cnt || counter+1 == total)
				 {
					 
					 jobs = jobs.substring(0,jobs.length()-1);
					 
						/*
						 * String sqlString = new StringBuilder()
						 * .append("SELECT * FROM rctb_trip_intf ") .append(" WHERE job_no in (" + jobs
						 * + ") ") .toString();
						 */
					 
					 String sqlString = new StringBuilder()
							 .append("SELECT pl.* ")
							 .append(" from cn2tmsys.tmtb_pl_txn_log pl ")
							 .append(" WHERE pl.job_number in (" + jobs + ") ")
							 .append(" AND pl.msg_type in ('pl_capture') ")
							 .toString();
					 
					 log.info(sqlString);
					 
			    	  ResultSet rs=stmt.executeQuery(sqlString);
					  ResultSetMetaData rsmd = rs.getMetaData();

					  int columnsNumber = rsmd.getColumnCount();
				


				
					  while(rs.next())
					  {     
						  if(columns=="")
						  {

							  for (int i = 0; i < columnsNumber; i++) {
								  columnList.add(rsmd.getColumnName(i+1).toString());
								  columnTypeList.add(rsmd.getColumnType(i+1));
								};
								
							columns = "DONE";
						  }
						  

						  Object[] recList = new Object[columnsNumber];
						  for (int i = 0; i < columnsNumber ; i++) {
							  recList[i] = rs.getString(i+1);  
							   
						  };
						  resultList.add(recList);
						  
					  };
					 
					 
					 jobs = "";
					 cnt=0;
				 }

			 }
				
			con.close();

		} catch (SQLException e) {
			e.printStackTrace();
		}


		WriteExcel(columnList,columnTypeList,resultList);
		
		Helper.WriteTextFile("DBSPOF", columnList, resultList);
		   
		log.info("TMTB_TripExcel_PLTXN done.");
	}

	
 public static void WriteExcel(ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList) throws FileNotFoundException, IOException {
		Workbook wb = new XSSFWorkbook();
		Sheet sheet1 = wb.createSheet("Sheet1");

		//WRITE THE COLUMN NAMES
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i);
	        headerCell.setCellValue(columnList.get(i));
		
		};
		
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i);
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell headerCell = recordRow.createCell(j);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(2)) {
		        		headerCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		headerCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	headerCell.setCellValue("");
		        }

			};
		
		};
		
		
		String filename = Helper.GenerateFileName();
		
		try (OutputStream fileOut = new FileOutputStream(Constants.FolderPath + filename + ".xlsx")) {
		    wb.write(fileOut);
		    wb.close();
		}
 }
 
 
 
}




