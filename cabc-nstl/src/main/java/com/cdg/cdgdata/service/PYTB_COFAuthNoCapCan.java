package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PYTB_COFAuthNoCapCan {

	public static void GetData() throws IOException {

		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		String dayX = "9";
		String dayY = ""; 
		String monthX = "2";
		
		try {
			Connection con = Helper.GetConnection("ptccpro_dcp");   // ptccpro_dcp      ptdapro_dcp
			Statement stmt = con.createStatement();

			for (int counter = Integer.parseInt(dayX); counter < 15; counter++) {

				  dayX = Integer.toString(counter);
				  dayY = Integer.toString(counter+2 );
		    	  String sqlString = new StringBuilder()
		    			  .append("select aut.txn_id, capcan.parent_txn_id, '" + dayX + "-Feb-2024' from ")
		    			  .append("(select distinct(txn_id) as txn_id from  pytb_wd_txn where txn_type = 'authorization' ")
		    			  .append("and payment_mode = 'COF' and txn_state = 'success' and txn_id is not null ")
		    			  .append("and created_dt >= TO_DATE('" + dayX + "/" + monthX + "/2024 00:00:00','DD/MM/YYYY HH24:MI:SS') ")
		    			  .append("and created_dt <= TO_DATE('" + dayX + "/" + monthX + "/2024 23:59:59','DD/MM/YYYY HH24:MI:SS') ")
		    			  .append(") aut ")
		    			  .append("left join ")
		    			  .append("(select distinct(parent_txn_id) as parent_txn_id from  pytb_wd_txn ")
		    			  .append("where payment_mode = 'COF' and txn_state = 'success' ")
		    			  .append("and created_dt >= TO_DATE('" + dayX + "/" + monthX + "/2024 00:00:00','DD/MM/YYYY HH24:MI:SS') ")
		    			  .append("and created_dt <= TO_DATE('" + dayY + "/" + monthX + "/2024 23:59:59','DD/MM/YYYY HH24:MI:SS') ")
		    			  .append(") capcan ")
		    			  .append("on aut.txn_id  = capcan.parent_txn_id ")
		    			  .append("where capcan.parent_txn_id is null ")
		    	           .toString();
		    	  
		    	  log.info("Value: {}", sqlString);
					 

					ResultSet rs = stmt.executeQuery(sqlString);
					ResultSetMetaData rsmd = rs.getMetaData();

					int columnsNumber = rsmd.getColumnCount();

					String columns = "";
					String records = "";

					while (rs.next()) {
						if (columns == "") {
							for (int i = 1; i < columnsNumber + 1; i++) {
								columns += rsmd.getColumnName(i) + "\t";
							}
							;
							myWriter.write(columns + "\r\n");
						}

						for (int i = 1; i < columnsNumber + 1; i++) {

							records += rs.getString(i) + "\t";
						}
						;
						myWriter.write(records + "\r\n");
						records = "";
					}
					;

				}

			myWriter.close();
		    con.close();

		} catch (Exception e) {
			log.error("Error occurred", e);
		}

	}

}
