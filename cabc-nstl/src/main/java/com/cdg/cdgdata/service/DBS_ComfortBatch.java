package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Iterator;

import org.apache.commons.codec.binary.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


@Slf4j
public class DBS_ComfortBatch {

	public static ArrayList<ArrayList<String>> map = new ArrayList<ArrayList<String>>();
	public static ArrayList<Object[]> dbsList = new ArrayList<Object[]>();
	
	public static void GetData() throws IOException {
		try
        {
			map = Helper.ReadNPXDBSTIDMap();

			
            FileInputStream file = new FileInputStream(new File("C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\CDGData-eclipse\\data\\ComfortBatch\\Comfort batch details.xlsx"));
            
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
 
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
 
            //Iterate through each rows one by one
            Iterator<Row> rowIterator = sheet.iterator();
            
            int rowctr = 0;
            
            while (rowIterator.hasNext()) 
            {
                Row row = rowIterator.next();
                //For each row, iterate through all the columns
                Iterator<Cell> cellIterator = row.cellIterator();
                
                Object[] recList = new Object[5];
                int colctr = 0;
                
                if(rowctr>0) {
                	 while (cellIterator.hasNext()) 
                     {
                         Cell cell = cellIterator.next();
                         //Check the cell type and format accordingly
                         if (colctr == 3 || colctr == 4 || colctr == 5) {
                         	
                        	 switch (colctr)
                        	 {
                        	 	case 3:
                        	 		recList[0] = cell.getNumericCellValue();
                        	 		break;
                        		
                        	 	case 4:
                        	 		recList[1] = cell.getNumericCellValue();                        	 		
                        	 		break;
                        	 		
                        	 	case 5:
                        	 		//String value = cell.getStringCellValue().substring(52,60);
                        	 		recList[2] = GetTxnDate(cell.getStringCellValue()); //.substring(35,43));
                        	 		recList[3] = GetTID(cell.getStringCellValue()); //.substring(52,60));
                        	 		recList[4] = GetNETSTID(recList[3].toString());
                        	 		break;
                        	 		
                        	 }

                         }
                         	
                         colctr++;
                     }
                	 dbsList.add(recList);
                }
                rowctr++;
                //log.info("");
            }
            workbook.close();
            file.close();
            WriteExcel(dbsList);
            
        } 
        catch (Exception e) 
        {
            e.printStackTrace();
        }
	}

	public static String GetTID(String input) {
		String result = "";
		result = input.substring(input.indexOf("TID")+3, input.length());
		result = result.replace(":", "");
		result = result.replace(" ", "");
		result = result.replace("\n", "");
		return result;
	}
	
	public static String GetTxnDate(String input) {
		String result = "";
		result = input.substring(input.indexOf("(")+1, input.indexOf(")"));
		return result.replace("TXN DD ", "");
	}
	
	public static void WriteExcel(ArrayList<Object[]> dbsList) throws FileNotFoundException, IOException {
		Workbook wb = new XSSFWorkbook();
		Sheet sheet1 = wb.createSheet("Sheet1");

		//WRITE THE COLUMNS
		Row rowField = sheet1.createRow(0);
		Cell colA = rowField.createCell(0);
		colA.setCellValue("");
		Cell colB = rowField.createCell(1);
		colB.setCellValue("AMT");
		Cell colC = rowField.createCell(2);
		colC.setCellValue("COUNT");
		Cell colD = rowField.createCell(3);
		colD.setCellValue("TXN_DATE");
		Cell colE = rowField.createCell(4);
		colE.setCellValue("DBS TID");
		Cell colF = rowField.createCell(5);
		colF.setCellValue("NETS TID");
		
		
		//WRITE THE RECORDS
		for (int i = 0; i < dbsList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = dbsList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			for (int j = 0; j < 5; j++) { 
		        Cell recordCell = recordRow.createCell(j+1);
		        if(recordObj[j]!=null) {
		        	recordCell.setCellValue(recordObj[j].toString());
		        }
		        else {
		        	recordCell.setCellValue("");
		        }

			};

		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<5;i++) {
			sheet1.autoSizeColumn(i);
		}
		

		String filename = Helper.GenerateFileName();
		
		try (OutputStream fileOut = new FileOutputStream(Constants.FolderPath +  filename + ".xlsx")) {
		    wb.write(fileOut);
		    wb.close();
		}
 	}
	
	public static String GetNETSTID(String input) {
		String result = "";
		
		int dbsIdx = 0;
		dbsIdx = map.get(1).indexOf(input);
		
		if(dbsIdx==-1) {
			dbsIdx = map.get(2).indexOf(input);
		}
		
		if(dbsIdx==-1) {
			dbsIdx = map.get(3).indexOf(input);
		}
		
		if(dbsIdx==-1) {
			dbsIdx = map.get(4).indexOf(input);
		}
		
		result = map.get(0).get(dbsIdx);
		
		return result;
	}
	
}
