package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;

import javax.print.attribute.standard.DateTimeAtCompleted;
import java.time.LocalDate;
import java.text.ParseException;
import java.text.SimpleDateFormat;  
import java.util.Date;  
@Slf4j
public class NPX_Archive {


	public static void GetData() throws IOException {
		
		
			try{		  
				
				String source = "O:\\NPX\\archive";
				String dest = "C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\NPX report for MinShi";

			    File directory = new File(dest);
			    File[] files = directory.listFiles(File::isFile);
			    long lastModifiedTime = Long.MIN_VALUE;
			    File chosenFile = null;

			    if (files != null)
			    {
			        for (File file : files)
			        {
			            if (file.lastModified() > lastModifiedTime)
			            {
			                chosenFile = file;
			                lastModifiedTime = file.lastModified();
			            }
			        }
			    }
			    
			    String filename = chosenFile.getName();
			    log.info("Value: {}", filename);
			    GetDateOfFile(filename);
			    
				 }catch(Exception e){ log.error("Error occurred", e);}
			 
			
	}

	public static LocalDate GetDateOfFile(String filename) throws ParseException {
		LocalDate myObj = LocalDate.now();
		
		String dateStr = filename.substring(31,39);
		log.info("Value: {}", dateStr);
		
		Date date1=new SimpleDateFormat("yyyyMMdd").parse(dateStr);  
		System.out.println(date1.toString());
		
		return myObj;
	}
	
}
