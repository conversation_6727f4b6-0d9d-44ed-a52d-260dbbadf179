package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class  TMTB_NON_BILL_TXNNO {


	public static void GetData() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		
			try{		  	  
				  Connection con= Helper.GetConnection("ptfspro_ibs");    
				  Statement stmt=con.createStatement();  
				  
				 //Load data
				 ArrayList<String[]> list = Helper.ReadDataSource("TMTB_NON_BILL_TXN_NO.txt");
				 int total = list.size();
				 
				 int chunk = 1000;
				 int cnt = 0;
				 String job_no = "";
				 for (int counter = 0; counter < list.size(); counter++) { 
					 
					 cnt++;
					 job_no = "" + list.get(counter)[0] + "," + job_no;
					 if(chunk==cnt || counter+1 == total)
					 {
						 
						 job_no = job_no.substring(0,job_no.length()-1);
						 
				    	  String sqlString = new StringBuilder()
				    	          .append("select job_NO, txn_no, batch_id from tmtb_non_billable_txn ")  
				    			  .append("WHERE txn_no in (" + job_no + ") ")
				    	            .toString();
						 
						 log.info("Value: {}", sqlString);
						 
				    	  ResultSet rs=stmt.executeQuery(sqlString);
						  ResultSetMetaData rsmd = rs.getMetaData();

						  int columnsNumber = rsmd.getColumnCount();
					
						  String columns = "";
						  String records = "";

					
						  while(rs.next())
						  {     
							  if(columns=="")
							  {
								  for (int i = 1; i < columnsNumber+1; i++) {
									  columns+=rsmd.getColumnName(i) + "\t";    
									};
									myWriter.write(columns + "\r\n");
							  }
							  

							  for (int i = 1; i < columnsNumber+ 1; i++) {

								   records+=rs.getString(i) + "\t";
							  };
							  myWriter.write(records + "\r\n");
							  records="";
						  };
						 
						 
						  job_no = "";
						 cnt=0;
					 }
					 

					 
				 }
				  

				  myWriter.close();
				  con.close();
				  
				  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	
	
}
