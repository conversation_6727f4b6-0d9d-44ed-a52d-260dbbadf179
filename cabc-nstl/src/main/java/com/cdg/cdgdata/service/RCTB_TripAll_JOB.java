package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.IOException;
import java.sql.*;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RCTB_TripAll_JOB {

    public static void GetData() throws IOException {


        ArrayList<Object[]> resultList = new ArrayList<Object[]>();
        ArrayList<String> columnList = new ArrayList<String>();
        ArrayList<Integer> columnTypeList = new ArrayList<Integer>();

        try {
            Connection con = Helper.GetConnection("ptfspro");
            Statement stmt = con.createStatement();

            //Load data
            ArrayList<String[]> list = Helper.ReadDataSource("RCTB_TripAll_JOB.txt");
            int total = list.size();

            int chunk = 1000;
            int cnt = 0;
            String jobs = "";
            String columns = "";
            for (int counter = 0; counter < list.size(); counter++) {

                cnt++;
                jobs = "'" + list.get(counter)[0] + "'," + jobs;
                if(chunk==cnt || counter+1 == total)
                {

                    jobs = jobs.substring(0,jobs.length()-1);

                    /*
                     * String sqlString = new StringBuilder()
                     * .append("SELECT * FROM rctb_trip_intf ") .append(" WHERE job_no in (" + jobs
                     * + ") ") .toString();
                     */

                    String sqlString = new StringBuilder()
                            .append("SELECT job_no, CARD_NO, fms_status, fms_date, error_code, recon_date FROM rctb_trip_intf ")
                            .append(" WHERE error_code is null and job_no in (" + jobs + ") ")
                            
                    		//.append("SELECT JOB_NO,	PAYMENT_MODE, CARD_NO ")
                            //.append(" from rctb_trip_intf ")
                            //.append(" WHERE job_no in (" + jobs + ") ")
                            //.append(" AND PAYMENT_MODE IN ('CABC') ")
                            //.append(" AND IBS_STATUS is null ")
                            //.append(" AND FMS_STATUS ='C'")
                            .toString();

                    log.info(sqlString);

                    ResultSet rs=stmt.executeQuery(sqlString);
                    ResultSetMetaData rsmd = rs.getMetaData();

                    int columnsNumber = rsmd.getColumnCount();




                    while(rs.next())
                    {
                        if(columns=="")
                        {

                            for (int i = 0; i < columnsNumber; i++) {
                                columnList.add(rsmd.getColumnName(i+1).toString());
                                columnTypeList.add(rsmd.getColumnType(i+1));
                            };

                            columns = "DONE";
                        }


                        Object[] recList = new Object[columnsNumber];
                        for (int i = 0; i < columnsNumber ; i++) {
                            recList[i] = rs.getString(i+1);

                        };
                        resultList.add(recList);

                    };


                    jobs = "";
                    cnt=0;
                }

            }

            con.close();

        } catch (SQLException e) {
            e.printStackTrace();
        }


        Helper.WriteTextFile("RCTBTRIP", columnList, resultList);

        log.info("RCTB_Trip_JOB done.");
    }
}
