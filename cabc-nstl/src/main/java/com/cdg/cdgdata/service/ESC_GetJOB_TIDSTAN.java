package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ESC_GetJOB_TIDSTAN {


	public static void GetData() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		
			try{		  	  
				Connection con= Helper.GetConnection("ptccpro");    
				  Statement stmt=con.createStatement();  
				  
				 //Load data
				 ArrayList<String[]> list = Helper.ReadDataSource("ESC_GetJOB_TIDSTAN.txt");

				  
			      for (int counter = 0; counter < list.size(); counter++) { 
			    	  
			    	  String sqlString = new StringBuilder()
			    	            .append("SELECT * FROM esc_payment_transaction P ")
			    	            .append("WHERE TID = '" + list.get(counter)[0] + "' ")
			    	            .append("AND SYSTEM_TRACE_AUDIT_NO  = '" + list.get(counter)[1] + "' ")
			    	            //.append("AND TRANS_TYPE  IN ('0200','0220') " )
			    	            //.append("AND PAYMENT_METHOD = 'NPX' " )
			    	            .append("order by p.log_dt asc ")
			    	            //.append("FETCH FIRST 1 ROWS ONLY")
			    	            .toString();
	 				  			    	  
			          log.info("Value: {}", sqlString);
			          		    	  
			    	  ResultSet rs=stmt.executeQuery(sqlString);
			    	  ResultSetMetaData rsmd = rs.getMetaData();
			    	  
					  while(rs.next())
					  {			  

						  int columnsNumber = rsmd.getColumnCount();
					
						  String columns = "";
						  String records = "";

					   
						  if(columns=="")
						  {
							  for (int i = 1; i < columnsNumber+1; i++) {
								  columns+=rsmd.getColumnName(i) + "\t";    
								};
								//columns = columns + "FINANCIAL_INSTITUTION_ID \t";
								myWriter.write(columns + "\r\n");
						  }
						  

						  for (int i = 1; i < columnsNumber+1; i++) {

							   records+=rs.getString(i) + "\t";
						  };
						  
						  myWriter.write(records + "\r\n");
						  records="";
					  }
				
				    	  
			    	  
			      }   	
				

				  myWriter.close();
				  con.close();
				  
				  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	
	
}
