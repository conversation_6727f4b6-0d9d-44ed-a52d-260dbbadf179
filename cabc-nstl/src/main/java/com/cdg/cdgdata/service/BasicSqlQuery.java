package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BasicSqlQuery {
	
	public static void GetData() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		    
		try{
			  
			  //String dbURL =
			 // "****************************** =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = **********)(PORT = 1521)))(CONNECT_DATA=(SERVICE_NAME = cn2uat.aix61.db.cdgtaxi.com.sg)))"
			 // ; String strUserID = "cn2user"; String strPassword = "pass"; 
			  
			 // Connection con=DriverManager.getConnection(dbURL,strUserID,strPassword);
			  
			  Connection con= Helper.GetConnection("ptfspro");    
			  
			  Statement stmt=con.createStatement();  
			  //step3 create the statement object Statement 
			  
			  stmt=con.createStatement();
			  
	    	  String sqlString = new StringBuilder()
	    	            .append("select job_no, payment_mode, completed_dt, recon_date ")
	    	            .append("from rctb_trip_intf where   error_code='NSTL' ")   
	    	            .append("and recon_date >= TO_DATE ('1/12/2022 00:00:00','DD/MM/YYYY HH24:MI:SS') ")
	    	            .toString();
	    	  
	          log.info("Value: {}", sqlString);
			  
			  //step4 execute query 
			  ResultSet rs=stmt.executeQuery(sqlString); 
			  
			  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();
		
			  String columns = "";
			  String records = "";

			  while(rs.next())
			  {     
				  if(columns=="")
				  {
					  for (int i = 1; i < columnsNumber; i++) {
						  columns+=rsmd.getColumnName(i) + "\t";   //+rs.getString(2)+"  "+rs.getString(3))
						};
						myWriter.write(columns + "\r\n");
				  }
				  
				  for (int i = 1; i < columnsNumber; i++) {
					  //System.out.print("'" + rs.getString(i) + "\t" );   //+rs.getString(2)+"  "+rs.getString(3))
					   records+=rs.getString(i) + "\t";
				  };
				  myWriter.write(records + "\r\n");
				  records="";
			  };
			  myWriter.close();
			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	
 
}
