package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RCTB_CommissionPaySumm {

	public static void GetData() throws IOException {
		
		String sqlStringCommision = new StringBuilder()
				
				  .append("select entity, service_type, count(*), sum(driver_levy) ")
				  .append("from rctb_trip_intf where payment_dt=trunc(sysdate) and payment_status='P' and fms_error_code is null and payment_mode!='INVO' "
				  ) .append("and nvl(fms_date, sysdate) > trunc(sysdate) and driver_levy > 0 ")
				  .append("group by entity, service_type ") .toString();
				 
		
/*				.append("select entity, service_type, count(*), sum(driver_levy) ")
				.append("from recsys.rctb_trip_intf where payment_dt=trunc(sysdate) and payment_status='P' and fms_error_code is null and payment_mode!='INVO' ")
				.append("and nvl(fms_date, sysdate) > trunc(sysdate) and driver_levy > 0 ")
				.append("group by entity, service_type ")
				.toString();*/
	
		String filenameCommision = GenerateCommissionFileName() ;
		
		ReadSqlData(sqlStringCommision, filenameCommision);
		
		
		String sqlStringPaySumm = new StringBuilder()
				
				  .append("select entity, payment_mode, count(*), sum(refund) from rctb_trip_intf "
				  )
				  .append("where payment_dt=trunc(sysdate) and payment_status='P' and fms_error_code is null and payment_mode!='CASH' "
				  ) .append("and nvl(fms_date, sysdate) > trunc(sysdate) ")
				  .append("group by entity, payment_mode ") .append("union ")
				  .append("select entity, promo_txn_code, count(*), sum(promo_amount) from rctb_trip_intf "
				  )
				  .append("where payment_dt=trunc(sysdate) and payment_status='P' and fms_error_code is null and promo_txn_code is not null "
				  ) .append("and nvl(fms_date, sysdate) > trunc(sysdate) ")
				  .append("group by entity, promo_txn_code ") .append("union ")
				  .append("select entity, 'CABREWARDS', count(*), sum(cabrewards_amount) from rctb_trip_intf "
				  )
				  .append("where payment_dt=trunc(sysdate) and payment_status='P' and fms_error_code is null and cabrewards_amount!=0.0 "
				  ) .append("and nvl(fms_date, sysdate) > trunc(sysdate) ")
				  .append("group by entity ") .toString();
				 
		

/*				.append("select entity, payment_mode, count(*), sum(refund) from recsys.rctb_trip_intf ")
				.append("where payment_dt=trunc(sysdate) and payment_status='P' and fms_error_code is null and payment_mode!='CASH' ")
				.append("and nvl(fms_date, sysdate) > trunc(sysdate) ")
				.append("group by entity, payment_mode ")
				.append("union ")
				.append("select entity, promo_txn_code, count(*), sum(promo_amount) from recsys.rctb_trip_intf ")
				.append("where payment_dt=trunc(sysdate) and payment_status='P' and fms_error_code is null and promo_txn_code is not null ")
				.append("and nvl(fms_date, sysdate) > trunc(sysdate) ")
				.append("group by entity, promo_txn_code ")
				.append("union ")
				.append("select entity, 'CABREWARDS', count(*), sum(cabrewards_amount) from recsys.rctb_trip_intf ")
				.append("where payment_dt=trunc(sysdate) and payment_status='P' and fms_error_code is null and cabrewards_amount!=0.0 ")
				.append("and nvl(fms_date, sysdate) > trunc(sysdate) ")
				.append("group by entity ")
				.toString();*/
		
		
		String filenamePaySumm = GeneratePaySummFileName() ;
		
		ReadSqlData(sqlStringPaySumm, filenamePaySumm);
		
	}

	public static void ReadSqlData(String sqlString, String filename) throws IOException {

		ArrayList<Object[]> resultList = new ArrayList<Object[]>();
		ArrayList<String> columnList = new ArrayList<String>();
		ArrayList<Integer> columnTypeList = new ArrayList<Integer>();

		try {
			Connection con = Helper.GetConnection("ptfspro"); // fmsprod ptfspro
			Statement stmt = con.createStatement();

			log.info(sqlString);

			ResultSet rs = stmt.executeQuery(sqlString);
			ResultSetMetaData rsmd = rs.getMetaData();

			int columnsNumber = rsmd.getColumnCount();

			String columns = "";

			while (rs.next()) {
				if (columns == "") {

					for (int i = 0; i < columnsNumber; i++) {
						columnList.add(rsmd.getColumnName(i + 1).toString());
						columnTypeList.add(rsmd.getColumnType(i + 1));
					}
					;

					columns = "DONE";
				}

				Object[] recList = new Object[columnsNumber];
				for (int i = 0; i < columnsNumber; i++) {
					recList[i] = rs.getString(i + 1);

				}
				;
				resultList.add(recList);

			}
			;

			con.close();

		} catch (SQLException e) {
			e.printStackTrace();
		}

		WriteExcel(columnList, columnTypeList, resultList, filename);

	}

	public static void WriteExcel(ArrayList<String> columnList, ArrayList<Integer> columnTypeList,
			ArrayList<Object[]> resultList, String filename) throws FileNotFoundException, IOException {
		Workbook wb = new XSSFWorkbook();
		Sheet sheet1 = wb.createSheet("Sheet1");

		// WRITE THE COLUMN NAMES
		CellStyle style = wb.createCellStyle();
		Font font = wb.createFont();
		font.setBold(true);
		style.setFont(font);

		Row headerRow = sheet1.createRow(0);

		for (int i = 0; i < columnList.size(); i++) {
			Cell headerCell = headerRow.createCell(i + 1);
			headerCell.setCellValue(columnList.get(i));
			headerCell.setCellStyle(style);

		}
		;

		// WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) {
			Row recordRow = sheet1.createRow(i + 1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i + 1);
			for (int j = 0; j < columnList.size(); j++) {
				Cell headerCell = recordRow.createCell(j + 1);
				if (recordObj[j] != null) {
					if (columnTypeList.get(j).equals(2)) {
						headerCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
					} else {
						headerCell.setCellValue(recordObj[j].toString());
					}
				} else {
					headerCell.setCellValue("");
				}

			}
			;

		}
		;

		// AUTOSIZE THE COLUMNS
		for (int i = 0; i < columnList.size() + 1; i++) {
			sheet1.autoSizeColumn(i);
		}

		try (OutputStream fileOut = new FileOutputStream(Constants.FolderPath + filename)) {
			wb.write(fileOut);
			wb.close();
		}
	}

	public static String GenerateCommissionFileName() {

		SimpleDateFormat formatter = new SimpleDateFormat("dd MMM");
		Date date = new Date();

		String filename = "PROD " + formatter.format(date) + " commission.xlsx";
		return filename;

	}

	public static String GeneratePaySummFileName() {

		SimpleDateFormat formatter = new SimpleDateFormat("dd MMM");
		Date date = new Date();

		String filename = "PROD " + formatter.format(date) + " payment summary.xlsx";
		return filename;

	}

}
