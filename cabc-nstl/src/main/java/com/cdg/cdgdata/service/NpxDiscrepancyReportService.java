package com.cdg.cdgdata.service;

import com.cdg.cdgdata.dto.NpxDiscrepancyDto;
import com.cdg.cdgdata.dto.NpxDiscrepancyReportDto;
import com.cdg.cdgdata.entity.EscReconSetlNetsnpxStg;
import com.cdg.cdgdata.repository.EscReconSetlNetsnpxRepository;
import com.cdg.cdgdata.repository.EscReconSetlNetsnpxStgRepository;
import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
@Transactional(readOnly = true)
public class NpxDiscrepancyReportService {

    @Autowired
    private EscReconSetlNetsnpxStgRepository stagingRepository;

    @Autowired
    private EscReconSetlNetsnpxRepository setlRepository;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");

    /**
     * Generate NPX discrepancy report for a specific date
     * @param reportDate Date in format dd/MM/yyyy
     * @return NpxDiscrepancyReportDto containing discrepancy data and file information
     */
    public NpxDiscrepancyReportDto generateDiscrepancyReport(String reportDate) {
        try {
            log.info("Starting NPX discrepancy report generation for date: {}", reportDate);

            // Parse the report date
            LocalDateTime startDate = LocalDateTime.parse(reportDate + " 00:00:00", DATETIME_FORMATTER);
            LocalDateTime endDate = LocalDateTime.parse(reportDate + " 23:59:59", DATETIME_FORMATTER);

            // Get staging only discrepancies
            List<NpxDiscrepancyDto> stagingOnlyRecords = getStagingOnlyDiscrepancies(startDate, endDate);
            log.info("Found {} staging only discrepancies", stagingOnlyRecords.size());

            // Get setl only discrepancies
            List<NpxDiscrepancyDto> setlOnlyRecords = getSetlOnlyDiscrepancies(startDate, endDate);
            log.info("Found {} setl only discrepancies", setlOnlyRecords.size());

            // Generate Excel file
            String fileName = generateExcelReport(stagingOnlyRecords, setlOnlyRecords, reportDate);

            // Create response
            NpxDiscrepancyReportDto response = new NpxDiscrepancyReportDto();
            response.setStagingOnlyRecords(stagingOnlyRecords);
            response.setSetlOnlyRecords(setlOnlyRecords);
            response.setFileName(fileName);
            response.setReportDate(reportDate);
            response.setStagingOnlyCount(stagingOnlyRecords.size());
            response.setSetlOnlyCount(setlOnlyRecords.size());
            response.setTotalDiscrepancies(stagingOnlyRecords.size() + setlOnlyRecords.size());
            response.setStatus("SUCCESS");
            response.setMessage("NPX discrepancy report generated successfully");

            log.info("NPX discrepancy report generation completed successfully. Total discrepancies: {}", 
                response.getTotalDiscrepancies());
            return response;

        } catch (Exception e) {
            log.error("Error generating NPX discrepancy report", e);
            NpxDiscrepancyReportDto errorResponse = new NpxDiscrepancyReportDto();
            errorResponse.setStatus("ERROR");
            errorResponse.setMessage("Error generating report: " + e.getMessage());
            errorResponse.setReportDate(reportDate);
            return errorResponse;
        }
    }

    /**
     * Get staging only discrepancies (records in staging but not in setl)
     */
    private List<NpxDiscrepancyDto> getStagingOnlyDiscrepancies(LocalDateTime startDate, LocalDateTime endDate) {
        List<EscReconSetlNetsnpxStg> stagingEntities = stagingRepository.findStagingOnlyDiscrepancies(startDate, endDate);

        // Convert entities to DTOs
        return stagingEntities.stream()
            .map(entity -> new NpxDiscrepancyDto(
                entity.getFileId(),
                entity.getFinancialInstitutionId(),
                entity.getTxnAmount(),
                entity.getCreatedDt(),
                entity.getRecordType(),
                entity.getProduct(),
                entity.getTransactionCd(),
                entity.getTransactionDt(),
                entity.getCorporationId(),
                entity.getEntity02Id(),
                entity.getEntity03Id(),
                entity.getEntity04Id(),
                entity.getEntity05Id(),
                entity.getAmount01No(),
                entity.getAmount02No(),
                entity.getAmount03No(),
                entity.getAmount04No(),
                entity.getAmount05No(),
                entity.getFee01No(),
                entity.getFee02No(),
                entity.getFee03No(),
                entity.getFee04No(),
                entity.getFee05No(),
                entity.getCardIssuerId(),
                entity.getReversalCd(),
                entity.getResponseCd(),
                entity.getEtc01Tx(),
                entity.getOrigTid(),
                entity.getOrigTimestamp(),
                entity.getOrigSeqNo(),
                entity.getEtc05Tx(),
                entity.getCanNo(),
                entity.getSequenceNo(),
                entity.getReferenceTx(),
                entity.getVoidFl(),
                null, // reconDate
                null, // reconStatus
                "STAGING_ONLY" // discrepancyType
            ))
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Get setl only discrepancies (records in setl but not in staging)
     */
    private List<NpxDiscrepancyDto> getSetlOnlyDiscrepancies(LocalDateTime startDate, LocalDateTime endDate) {
        return setlRepository.findSetlOnlyDiscrepancies(startDate, endDate);
    }

    /**
     * Generate Excel report with discrepancy data
     */
    private String generateExcelReport(List<NpxDiscrepancyDto> stagingOnlyRecords, 
                                     List<NpxDiscrepancyDto> setlOnlyRecords, 
                                     String reportDate) throws IOException {
        
        Workbook workbook = new XSSFWorkbook();
        
        // Create sheets
        createStagingOnlySheet(workbook, stagingOnlyRecords);
        createSetlOnlySheet(workbook, setlOnlyRecords);
        createSummarySheet(workbook, stagingOnlyRecords.size(), setlOnlyRecords.size(), reportDate);
        
        // Generate filename
        String fileName = "NPX_Discrepancy_Report_" + reportDate.replace("/", "") + "_" + Helper.GenerateFileName();
        String fullPath = Constants.getFolderPath() + fileName + ".xlsx";
        
        // Write to file
        try (FileOutputStream fileOut = new FileOutputStream(fullPath)) {
            workbook.write(fileOut);
        }
        
        workbook.close();
        return fileName + ".xlsx";
    }

    /**
     * Create staging only discrepancies sheet
     */
    private void createStagingOnlySheet(Workbook workbook, List<NpxDiscrepancyDto> data) {
        Sheet sheet = workbook.createSheet("Staging Only");
        
        // Create header style
        CellStyle headerStyle = createHeaderStyle(workbook);
        
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "Row", "FILE_ID", "FINANCIAL_INSTITUTION_ID", "TXN_AMOUNT", "CREATED_DT", 
            "RECORD_TYPE", "PRODUCT", "TRANSACTION_CD", "TRANSACTION_DT", "CORPORATION_ID",
            "ENTITY_02_ID", "ENTITY_03_ID", "ENTITY_04_ID", "ENTITY_05_ID", "AMOUNT_01_NO",
            "AMOUNT_02_NO", "AMOUNT_03_NO", "AMOUNT_04_NO", "AMOUNT_05_NO", "FEE_01_NO",
            "FEE_02_NO", "FEE_03_NO", "FEE_04_NO", "FEE_05_NO", "CARD_ISSUER_ID",
            "REVERSAL_CD", "RESPONSE_CD", "ETC_01_TX", "ORIG_TID", "ORIG_TIMESTAMP",
            "ORIG_SEQ_NO", "ETC_05_TX", "CAN_NO", "SEQUENCE_NO", "REFERENCE_TX", "VOID_FL"
        };
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // Create data rows
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            NpxDiscrepancyDto item = data.get(i);
            
            row.createCell(0).setCellValue(i + 1);
            row.createCell(1).setCellValue(item.getFileId());
            row.createCell(2).setCellValue(item.getFinancialInstitutionId());
            row.createCell(3).setCellValue(item.getTxnAmount() != null ? item.getTxnAmount().doubleValue() : 0);
            row.createCell(4).setCellValue(item.getCreatedDt() != null ? item.getCreatedDt().toString() : "");
            row.createCell(5).setCellValue(item.getRecordType());
            row.createCell(6).setCellValue(item.getProduct());
            row.createCell(7).setCellValue(item.getTransactionCd());
            row.createCell(8).setCellValue(item.getTransactionDt() != null ? item.getTransactionDt().toString() : "");
            row.createCell(9).setCellValue(item.getCorporationId());
            row.createCell(10).setCellValue(item.getEntity02Id());
            row.createCell(11).setCellValue(item.getEntity03Id());
            row.createCell(12).setCellValue(item.getEntity04Id());
            row.createCell(13).setCellValue(item.getEntity05Id());
            row.createCell(14).setCellValue(item.getAmount01No() != null ? item.getAmount01No().doubleValue() : 0);
            row.createCell(15).setCellValue(item.getAmount02No() != null ? item.getAmount02No().doubleValue() : 0);
            row.createCell(16).setCellValue(item.getAmount03No() != null ? item.getAmount03No().doubleValue() : 0);
            row.createCell(17).setCellValue(item.getAmount04No() != null ? item.getAmount04No().doubleValue() : 0);
            row.createCell(18).setCellValue(item.getAmount05No() != null ? item.getAmount05No().doubleValue() : 0);
            row.createCell(19).setCellValue(item.getFee01No() != null ? item.getFee01No().doubleValue() : 0);
            row.createCell(20).setCellValue(item.getFee02No() != null ? item.getFee02No().doubleValue() : 0);
            row.createCell(21).setCellValue(item.getFee03No() != null ? item.getFee03No().doubleValue() : 0);
            row.createCell(22).setCellValue(item.getFee04No() != null ? item.getFee04No().doubleValue() : 0);
            row.createCell(23).setCellValue(item.getFee05No() != null ? item.getFee05No().doubleValue() : 0);
            row.createCell(24).setCellValue(item.getCardIssuerId());
            row.createCell(25).setCellValue(item.getReversalCd());
            row.createCell(26).setCellValue(item.getResponseCd());
            row.createCell(27).setCellValue(item.getEtc01Tx());
            row.createCell(28).setCellValue(item.getOrigTid());
            row.createCell(29).setCellValue(item.getOrigTimestamp());
            row.createCell(30).setCellValue(item.getOrigSeqNo());
            row.createCell(31).setCellValue(item.getEtc05Tx());
            row.createCell(32).setCellValue(item.getCanNo());
            row.createCell(33).setCellValue(item.getSequenceNo());
            row.createCell(34).setCellValue(item.getReferenceTx());
            row.createCell(35).setCellValue(item.getVoidFl());
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * Create setl only discrepancies sheet
     */
    private void createSetlOnlySheet(Workbook workbook, List<NpxDiscrepancyDto> data) {
        Sheet sheet = workbook.createSheet("Setl Only");
        
        // Create header style
        CellStyle headerStyle = createHeaderStyle(workbook);
        
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "Row", "FILE_ID", "FINANCIAL_INSTITUTION_ID", "TXN_AMOUNT", "CREATED_DT", 
            "RECORD_TYPE", "PRODUCT", "TRANSACTION_CD", "TRANSACTION_DT", "CORPORATION_ID",
            "ENTITY_02_ID", "ENTITY_03_ID", "ENTITY_04_ID", "ENTITY_05_ID", "AMOUNT_01_NO",
            "AMOUNT_02_NO", "AMOUNT_03_NO", "AMOUNT_04_NO", "AMOUNT_05_NO", "FEE_01_NO",
            "FEE_02_NO", "FEE_03_NO", "FEE_04_NO", "FEE_05_NO", "CARD_ISSUER_ID",
            "REVERSAL_CD", "RESPONSE_CD", "ETC_01_TX", "ORIG_TID", "ORIG_TIMESTAMP",
            "ORIG_SEQ_NO", "ETC_05_TX", "CAN_NO", "SEQUENCE_NO", "REFERENCE_TX", "VOID_FL",
            "RECON_DATE", "RECON_STATUS"
        };
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // Create data rows
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            NpxDiscrepancyDto item = data.get(i);
            
            row.createCell(0).setCellValue(i + 1);
            row.createCell(1).setCellValue(item.getFileId());
            row.createCell(2).setCellValue(item.getFinancialInstitutionId());
            row.createCell(3).setCellValue(item.getTxnAmount() != null ? item.getTxnAmount().doubleValue() : 0);
            row.createCell(4).setCellValue(item.getCreatedDt() != null ? item.getCreatedDt().toString() : "");
            row.createCell(5).setCellValue(item.getRecordType());
            row.createCell(6).setCellValue(item.getProduct());
            row.createCell(7).setCellValue(item.getTransactionCd());
            row.createCell(8).setCellValue(item.getTransactionDt() != null ? item.getTransactionDt().toString() : "");
            row.createCell(9).setCellValue(item.getCorporationId());
            row.createCell(10).setCellValue(item.getEntity02Id());
            row.createCell(11).setCellValue(item.getEntity03Id());
            row.createCell(12).setCellValue(item.getEntity04Id());
            row.createCell(13).setCellValue(item.getEntity05Id());
            row.createCell(14).setCellValue(item.getAmount01No() != null ? item.getAmount01No().doubleValue() : 0);
            row.createCell(15).setCellValue(item.getAmount02No() != null ? item.getAmount02No().doubleValue() : 0);
            row.createCell(16).setCellValue(item.getAmount03No() != null ? item.getAmount03No().doubleValue() : 0);
            row.createCell(17).setCellValue(item.getAmount04No() != null ? item.getAmount04No().doubleValue() : 0);
            row.createCell(18).setCellValue(item.getAmount05No() != null ? item.getAmount05No().doubleValue() : 0);
            row.createCell(19).setCellValue(item.getFee01No() != null ? item.getFee01No().doubleValue() : 0);
            row.createCell(20).setCellValue(item.getFee02No() != null ? item.getFee02No().doubleValue() : 0);
            row.createCell(21).setCellValue(item.getFee03No() != null ? item.getFee03No().doubleValue() : 0);
            row.createCell(22).setCellValue(item.getFee04No() != null ? item.getFee04No().doubleValue() : 0);
            row.createCell(23).setCellValue(item.getFee05No() != null ? item.getFee05No().doubleValue() : 0);
            row.createCell(24).setCellValue(item.getCardIssuerId());
            row.createCell(25).setCellValue(item.getReversalCd());
            row.createCell(26).setCellValue(item.getResponseCd());
            row.createCell(27).setCellValue(item.getEtc01Tx());
            row.createCell(28).setCellValue(item.getOrigTid());
            row.createCell(29).setCellValue(item.getOrigTimestamp());
            row.createCell(30).setCellValue(item.getOrigSeqNo());
            row.createCell(31).setCellValue(item.getEtc05Tx());
            row.createCell(32).setCellValue(item.getCanNo());
            row.createCell(33).setCellValue(item.getSequenceNo());
            row.createCell(34).setCellValue(item.getReferenceTx());
            row.createCell(35).setCellValue(item.getVoidFl());
            row.createCell(36).setCellValue(item.getReconDate() != null ? item.getReconDate().toString() : "");
            row.createCell(37).setCellValue(item.getReconStatus());
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * Create summary sheet
     */
    private void createSummarySheet(Workbook workbook, int stagingOnlyCount, int setlOnlyCount, String reportDate) {
        Sheet sheet = workbook.createSheet("Summary");
        
        // Create header style
        CellStyle headerStyle = createHeaderStyle(workbook);
        
        // Create summary data
        Row row0 = sheet.createRow(0);
        Cell cell0 = row0.createCell(0);
        cell0.setCellValue("NPX Discrepancy Report Summary");
        cell0.setCellStyle(headerStyle);
        
        Row row1 = sheet.createRow(2);
        row1.createCell(0).setCellValue("Report Date:");
        row1.createCell(1).setCellValue(reportDate);
        
        Row row2 = sheet.createRow(3);
        row2.createCell(0).setCellValue("Staging Only Discrepancies:");
        row2.createCell(1).setCellValue(stagingOnlyCount);
        
        Row row3 = sheet.createRow(4);
        row3.createCell(0).setCellValue("Setl Only Discrepancies:");
        row3.createCell(1).setCellValue(setlOnlyCount);
        
        Row row4 = sheet.createRow(5);
        row4.createCell(0).setCellValue("Total Discrepancies:");
        row4.createCell(1).setCellValue(stagingOnlyCount + setlOnlyCount);
        
        Row row5 = sheet.createRow(7);
        row5.createCell(0).setCellValue("Generated At:");
        row5.createCell(1).setCellValue(LocalDateTime.now().toString());
        
        // Auto-size columns
        sheet.autoSizeColumn(0);
        sheet.autoSizeColumn(1);
    }

    /**
     * Create header cell style
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        XSSFCellStyle headerStyle = (XSSFCellStyle) workbook.createCellStyle();
        XSSFFont font = (XSSFFont) workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        headerStyle.setFont(font);
        headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        return headerStyle;
    }
}
