package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;


@Slf4j
public class SandBox {
	
	public static void GetData() throws IOException {
		
		String record = "44201111790001588704014f4b01fffb5a34b6d3a50000000a00161e00117801000200000a05000000002340000000000000000000000000000000000000000000002521ff030007d034b949e70000000e01fff5e234d0b8ad434f4d464f520000008648c3e73eca6eda0001540000150001cfbcb7a45e1b9ea20001550000150001cc7e0801534843303833334320202020433030313833363420353136313136363533343030303030303030303030304343504c20202020202020202020202020202020202020\r\n"
				+ "";
		
		String record2 = "44201111700261324020014f4b01fff64134db127d0000000a006a660060a701000200000a05000000002340000000000000000000000000000000000000000000000121ff7500271034d784c70012010030ffff9d34db00e04354482d42475320001484249129455cd7000d9b0000c900019b0eb56b1570318d000d9c0000c90001cdc92601534844333236374c20202020433030373739343520373239323633363033203030303030323030303032384354504c20202020202020202020202020202020202020";
		
		String _date = record.substring(34, 42);
		String _date2 = record2.substring(34, 42);
		
		Date result = getFromJulianDate( record.substring(34, 42));
		Date result2 = getFromJulianDate( record2.substring(34, 42));
			 
	}

	private static Date getFromJulianDate(String julianDateStr) {
		
        int seconds = Integer.parseInt(julianDateStr, 16);
  
        
        Calendar cl = Calendar.getInstance();
        cl.clear();
        cl.set(Calendar.YEAR, 1995);
        cl.set(Calendar.MONTH, 0);
        cl.set(Calendar.DAY_OF_MONTH, 1);
        cl.set(Calendar.HOUR, 0);
        cl.set(Calendar.MINUTE, 0);
        
        cl.add(Calendar.SECOND, seconds);
        
        return cl.getTime();      
    }
	
	private static void ArrayCheck() {
		ArrayList<Date> cryptError = new ArrayList<Date>();
		Date d1 = new Date(2023, 05, 30, 1,3,5);
		cryptError.add(d1);
	}
	
	
	public static void Dictionari() {
		Map<String, String> list = new HashMap<>();
		String value = "";
		list.put("MEMEL", "TINED");
		list.put("MEMEL", "TINED_X");
		list.put("SPARKLE", "CARD");
		
		if(list.containsKey("MEMEL")) {
			value = list.get("MEMEL");
		};
		
	}
 
	
	public static Map<String, String> listContainsString(List<String> list, String checkStr)
	{
        Map<String, String> result = new HashMap<>();
	    Iterator<String> iter = list.iterator();
	    while(iter.hasNext())
	    {
	        String s = iter.next();


	        if (s.contains(checkStr))
	        {
	        	result.put("TRUE", s);
	        }
	        else {
	        	result.put("FALSE", s);
	        }
	    }
	 
	    return result;
	}
	
	
	public static void OracleDB() {
		
		String records = "";
		
		try{
			
			  Connection con= Helper.GetConnection("ptccpro");    
			  Statement stmt=con.createStatement();  
			  //70481414 000377
					
	    	  String sqlString = new StringBuilder()
	    	            .append("SELECT TERMINAL_ID, STAN, CAN_NO, financial_institution_id,  FILE_ID FROM cn2_recon.esc_recon_setl_netsnpx_stg P ")
	    	            .append(" WHERE TERMINAL_ID = '" + "70481414" + "' " + " AND STAN = '" + "000377" + "' ")
	    	            .toString();
			  			    	  
	          log.info("Value: {}", sqlString);
			          		    	  
	    	  ResultSet rs=stmt.executeQuery(sqlString);
	    	  ResultSetMetaData rsmd = rs.getMetaData();
	    	  int columnsNumber = rsmd.getColumnCount();
	    	  
	    	  int countrow = 0;
	    	  
			  while(rs.next())
			  {			  
				  for (int p = 1; p < columnsNumber+1; p++) {
					   records+=rs.getString(p) + "\t";
				  };
				  countrow++;
			  }
			  
			  

			  con.close();
			  
		 }catch(Exception e){ log.error("Error occurred", e);}
	}
	
	public static void ObjCheck() {
		  Object[] recList = new Object[21];
		  for (int j = 0; j < 18 ; j++) {
			  recList[j] = j;
			   
		  };
	}
	
	
	public static void ExistInList() {
		ArrayList<String> listOne = new ArrayList<String>();
		listOne.add("Tom");
		listOne.add("Jack");
		ArrayList<String> listTwo = new ArrayList<String>();
		listTwo.add("Tom");
		listTwo.add("John");
	    List<String> differences = new ArrayList<>(listOne);
	    
	    differences.removeAll(listTwo);
		   
	}
	
	public static void HexMe() {
		
		String data = "1208002020000000c000139999992076585452473030373241545247303037324120202020202020017663eccbeb5498110e22ed457368757bb6e0cc5a33108e66050e555f4bb52e20a34eaa1cf7231cbf7b3209dbef039eb302cc5fc27b82b502b3fb3d01ca9e722e59e30d290d6b3e2d309bbd89a9353fa42e0b73ad0981e42bf6d8c04c168c8eb0f8b99f29afa00bc32d089202b8b85616120e6ad72a84f65666cde2eb93f2bb1da6394349a09b6e0c428d60902d854b8b733b2808102734b0cabb0693935ec6e397ec840ec41dda02abb56aca20c8eeb66b003230303030313530323230383732393230202020203030303130343030303030350989e2f7ebd8cf5c";
		byte[] result;
		String back;
		
		try {
			result = Hex.decodeHex(data);
			back = Hex.encodeHexString(result);
			
		} catch (DecoderException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
	}
}


