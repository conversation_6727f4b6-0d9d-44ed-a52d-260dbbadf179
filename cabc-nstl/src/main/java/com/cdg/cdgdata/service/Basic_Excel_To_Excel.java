package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Iterator;

import org.apache.poi.sl.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


@Slf4j
public class Basic_Excel_To_Excel {
	
	public static ArrayList<Object[]> recordList = new ArrayList<Object[]>();
	public static ArrayList<String> columnList = new ArrayList<String>();
	
	
	public static void GetData() throws IOException {
		ReadExcel();
		WriteExcel(columnList, recordList, "myfinal.xlsx");
	}
	

	public static void ReadExcel() throws IOException {
		try
        {
            FileInputStream file = new FileInputStream(new File("C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\CDGData-eclipse\\data\\dbsdec12.xlsx"));
 
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
 
            //if with password
            //XSSFWorkbook workbook=(XSSFWorkbook)WorkbookFactory.create(file,"npxnpx");
            
            
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
 
            //Iterate through each rows one by one
            Iterator<Row> rowIterator = sheet.iterator();
            
            int headerIdx = 0;
            int colCounter = 1;
            int colIdx = 0;
            while (rowIterator.hasNext()) 
            {
                Row row = rowIterator.next();
                //For each row, iterate through all the columns
                Iterator<Cell> cellIterator = row.cellIterator();
                
                if(headerIdx==0) {
                    while (cellIterator.hasNext()) 
                    {
                        Cell cell = cellIterator.next();
						columnList.add(cell.getStringCellValue());
						colCounter++;
                    }
                	headerIdx=1;
                }
                
                else {
                	Object[] recList = new Object[colCounter];
                	
                    while (cellIterator.hasNext()) 
                    {
                        Cell cell = cellIterator.next();
                    	
                        //Check the cell type and format accordingly
                        switch (cell.getCellType()) 
                        {
                            case NUMERIC:
                            	recList[colIdx] = cell.getNumericCellValue();
                                break;
                            case STRING:
                            	recList[colIdx] = cell.getStringCellValue();
                                break;
	    					default:
	    						recList[colIdx] = cell.getStringCellValue();
	    						break;
                        }
                        colIdx++;
                    }
                    recordList.add(recList);
                    colIdx = 0;
                }
                

                //log.info("");
            }
            workbook.close();
            file.close();
            
        } 
        catch (Exception e) 
        {
            e.printStackTrace();
        }
	}
	
	public static void WriteExcel(ArrayList<String> columnList,  ArrayList<Object[]> resultList, String filename) throws FileNotFoundException, IOException {
		XSSFWorkbook wb = new XSSFWorkbook();
		XSSFSheet sheet1 = wb.createSheet("Sheet1");

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i);
	        headerCell.setCellValue(columnList.get(i));
	        headerCell.setCellStyle(style);
		
		};
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell recordCell = recordRow.createCell(j);
		        if(recordObj[j]!=null) {
		        	recordCell.setCellValue(recordObj[j].toString());
		        }
		        else {
		        	recordCell.setCellValue("");
		        }

			};
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<columnList.size()+1;i++) {
			sheet1.autoSizeColumn(i);
		}
		

		
		try (OutputStream fileOut = new FileOutputStream(Constants.FolderPath +  filename)) {
		    wb.write(fileOut);
		    wb.close();
		}
 	}
 
}
