package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


@Slf4j
public class COF_JobNoCapture {

	
	public static ArrayList<Object[]> dcpList = new ArrayList<Object[]>();
	public static  ArrayList<String> dcpcolumnList = new ArrayList<String>();
	public static  ArrayList<Integer> dcpcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> pytbList = new ArrayList<Object[]>();
	public static ArrayList<String> pytbcolumnList = new ArrayList<String>();
	public static ArrayList<Integer> pytbcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> nocapList = new ArrayList<Object[]>();
	public static  ArrayList<String> nocapcolumnList = new ArrayList<String>();
	public static  ArrayList<Integer> nocapcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> truncdcpList = new ArrayList<Object[]>();
	public static  ArrayList<String> truncdcpcolumnList = new ArrayList<String>();
	public static  ArrayList<Integer> truncdcpcolumnTypeList = new ArrayList<Integer>();
	
	public static Workbook wbfinal = new XSSFWorkbook();

	public static void GetData() throws IOException {
		
		GetDcpId();
		GetPytb();
		
		int dcpsize = dcpList.size();
		int pytbsize = pytbList.size();
		int nocapsize = nocapList.size();
		
		WriteWBSheets(wbfinal, "TRIP-DCP", dcpcolumnList, dcpcolumnTypeList, dcpList);
		WriteWBSheets(wbfinal, "PYTB", pytbcolumnList, pytbcolumnTypeList, pytbList);
		WriteWBSheets(wbfinal, "NOCAPTURE", nocapcolumnList, nocapcolumnTypeList, nocapList);
		WriteWBSheets(wbfinal, "TRUNC-DCP", truncdcpcolumnList, truncdcpcolumnTypeList, truncdcpList);
				
		String filename = Helper.GenerateFileName();
		try (OutputStream fileOut = new
		FileOutputStream(Constants.FolderPath + filename + ".xlsx")) { wbfinal.write(fileOut);
		wbfinal.close(); }
		   
		log.info("COF_JobNoCapture done.");
	}
	


	public static void GetDcpId() throws IOException {
		

		try {
			Connection con = Helper.GetConnection("ptccpro");
			Statement stmt = con.createStatement();

			 //Load data
			 ArrayList<String[]> list = Helper.ReadDataSource("COF_JobNoCapture.txt");
			 int total = list.size();
			 
			 int chunk = 1000;
			 int cnt = 0;
			 String jobs = "";
			 String columns = "";
			 for (int counter = 0; counter < list.size(); counter++) { 
				 
				 cnt++;
				 jobs = "'" + list.get(counter)[0] + "'," + jobs;
				 if(chunk==cnt || counter+1 == total)
				 {
					 
					 jobs = jobs.substring(0,jobs.length()-1);
					 
			    	  String sqlString = new StringBuilder()
			    			  .append("SELECT JOB_NO, DCP_REQUEST_ID ")
			    			  .append(" from esc_job ")
			    			  .append(" WHERE JOB_NO in (" + jobs + ") ")
			    	          .toString();
					 
					 log.info(sqlString);
					 
			    	  ResultSet rs=stmt.executeQuery(sqlString);
					  ResultSetMetaData rsmd = rs.getMetaData();

					  int columnsNumber = rsmd.getColumnCount();
				
					  while(rs.next())
					  {     
						  if(columns=="")
						  {

							  for (int i = 0; i < columnsNumber; i++) {
								  dcpcolumnList.add(rsmd.getColumnName(i+1).toString());
								  dcpcolumnTypeList.add(rsmd.getColumnType(i+1));
								};
								
							columns = "DONE";
						  }
						  

						  Object[] recList = new Object[columnsNumber];
						  for (int i = 0; i < columnsNumber ; i++) {
							  recList[i] = rs.getString(i+1);  
							   
						  };
						  dcpList.add(recList);
						  
					  };
					 
					 
					 jobs = "";
					 cnt=0;
				 }

			 }
				
			con.close();

		} catch (SQLException e) {
			e.printStackTrace();
		}


	}

	
	public static void GetPytb() throws IOException {
		try {
			Connection con = Helper.GetConnection("ptccpro_dcp");
			Statement stmt = con.createStatement();
			Statement stmt2 = con.createStatement();
			
			 String dcpId = "";
			 String jobNo = "";
			 String truncdcpId = "";
			 String columnspytb = "";
			 String columnsnocap = "";
			 String columnstruncdcp = "";
			 for (int i = 0; i < dcpList.size(); i++) { 
					 
				 	Object[] tripDcp = dcpList.get(i);
 				 	jobNo = tripDcp[0].toString();
				 	dcpId = tripDcp[1].toString();
				 	truncdcpId = dcpId.substring(0, dcpId.length() - 6);
				 	
				 	
			    	  String sqlString = new StringBuilder()
			    			  .append(" SELECT BOOKING_REF,PROVIDER_REF,TXN_TYPE,TXN_STATE,STATUS_DESCRIPTION,CREATED_DT,UPDATED_DT, ")
			    			  .append(" REQUESTED_AMOUNT,PAYMENT_METHOD,TXN_ID,PARENT_TXN_ID,PAYMENT_MODE,UDID ")
			    			  .append(" from pytb_wd_txn ")
			    			  //.append(" WHERE BOOKING_REF like ('" + truncdcpId + "%') ")
			    			  .append(" WHERE BOOKING_REF = ('" + dcpId + "') ")
			    			  .append(" and payment_mode ='COF' ")
			    			  .append(" and txn_type ='capture-authorization' ")
			    	          .toString();
					 
					  log.info(sqlString);
					 
			    	  ResultSet rs=stmt.executeQuery(sqlString);
					  ResultSetMetaData rsmd = rs.getMetaData();

					  int columnsNumber = rsmd.getColumnCount();
					  
					  int size = 0;
				      
				      
					  while(rs.next())
					  {     
						  size++;
						  if(columnspytb=="")
						  {
							  pytbcolumnList.add("JOB_NO");
							  pytbcolumnTypeList.add(0);
							  for (int c = 0; c < columnsNumber; c++) {
								  pytbcolumnList.add(rsmd.getColumnName(c+1).toString());
								  pytbcolumnTypeList.add(rsmd.getColumnType(c+1));
								};
								
							columnspytb = "DONE";
						  }
						  

						  Object[] recList = new Object[columnsNumber+1];
						  recList[0] = jobNo;
						  for (int j = 1; j < columnsNumber+1 ; j++) {
							  recList[j] = rs.getString(j);  
							   
						  };
						  pytbList.add(recList);
						  
					  };
					 
					  System.out.println("pytb size:" + Integer.toBinaryString(size));


					  //if no capture record then pull all using exact booking_ref
					 if (size == 0) {
				    	  String sqlString2 = new StringBuilder()
				    			  .append(" SELECT BOOKING_REF,PROVIDER_REF,TXN_TYPE,TXN_STATE,STATUS_DESCRIPTION,CREATED_DT,UPDATED_DT, ")
				    			  .append(" REQUESTED_AMOUNT,PAYMENT_METHOD,TXN_ID,PARENT_TXN_ID,PAYMENT_MODE,UDID ")
				    			  .append(" from pytb_wd_txn ")
				    			  .append(" WHERE BOOKING_REF = ('" + dcpId + "') ")
				    			  .append(" and payment_mode ='COF' ")
				    	          .toString();
						 
						  log.info(sqlString2);
						 
				    	  ResultSet rs2=stmt2.executeQuery(sqlString2);
						  ResultSetMetaData rsmd2 = rs2.getMetaData();

						  columnsNumber = rsmd2.getColumnCount();
						  
						  int sizenocap = 0;
						  while(rs2.next())
						  {   
							  sizenocap++;
							  if(columnsnocap=="")
							  {
								  nocapcolumnList.add("JOB_NO");
								  nocapcolumnTypeList.add(0);
								  for (int c = 0; c < columnsNumber; c++) {
									  nocapcolumnList.add(rsmd2.getColumnName(c+1).toString());
									  nocapcolumnTypeList.add(rsmd2.getColumnType(c+1));
									};
									
								columnsnocap = "DONE";
							  }
							  

							  Object[] recList = new Object[columnsNumber+1];
							  recList[0] = jobNo;
							  for (int j = 1; j < columnsNumber+1 ; j++) {
								  recList[j] = rs2.getString(j);  
								   
							  };
							  nocapList.add(recList);
							  
						  };
						  
						  System.out.println("nocap size:" + Integer.toBinaryString(sizenocap));
						  
						  //if no records using exact booking ref, then use trunc DCP like approach
						  if (sizenocap == 0) {
					    	  String sqlString3 = new StringBuilder()
					    			  .append(" SELECT BOOKING_REF,PROVIDER_REF,TXN_TYPE,TXN_STATE,STATUS_DESCRIPTION,CREATED_DT,UPDATED_DT, ")
					    			  .append(" REQUESTED_AMOUNT,PAYMENT_METHOD,TXN_ID,PARENT_TXN_ID,PAYMENT_MODE,UDID ")
					    			  .append(" from pytb_wd_txn ")
					    			  .append(" WHERE BOOKING_REF like ('" + truncdcpId + "%') ")
					    			  .append(" and payment_mode ='COF' ")
					    	          .toString();
							 
							  log.info(sqlString3);
							 
					    	  ResultSet rs3=stmt2.executeQuery(sqlString3);
							  ResultSetMetaData rsmd3 = rs3.getMetaData();

							  columnsNumber = rsmd3.getColumnCount();
							  
							  int sizetruncdcp = 0;
							  while(rs3.next())
							  {   
								  if(columnstruncdcp=="")
								  {
									  truncdcpcolumnList.add("JOB_NO");
									  truncdcpcolumnTypeList.add(0);
									  for (int c = 0; c < columnsNumber; c++) {
										  truncdcpcolumnList.add(rsmd3.getColumnName(c+1).toString());
										  truncdcpcolumnTypeList.add(rsmd3.getColumnType(c+1));
										};
										
									 columnstruncdcp = "DONE";
								  }
								  

								  Object[] recList = new Object[columnsNumber+1];
								  recList[0] = jobNo;
								  for (int j = 1; j < columnsNumber+1 ; j++) {
									  recList[j] = rs3.getString(j);  
									   
								  };
								  truncdcpList.add(recList);
								  
							  };
							  
							  System.out.println("trunc dcp size:" + Integer.toBinaryString(sizetruncdcp));
						  }
						 
					 }
					 
					 dcpId = "";
					 jobNo = "";
					  
 
				 }
				
			con.close();

		} catch (SQLException e) {
			e.printStackTrace();
		}
	}
	

	
	//ToListBookingRef
	public static Workbook WriteWBSheets(Workbook wb, String sheetname, ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList) throws IOException {
		Sheet sheet1 = wb.createSheet(sheetname);

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i+1);
	        headerCell.setCellValue(columnList.get(i));
	        headerCell.setCellStyle(style);
		
		};
		
		int size = resultList.size();
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			int colsize = columnList.size();
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell recordCell = recordRow.createCell(j+1);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(8)) {
		        		recordCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		recordCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	recordCell.setCellValue("");
		        }

			};
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<columnList.size()+1;i++) {
			sheet1.autoSizeColumn(i);
		}
		
		return wb;
	}

	
}
