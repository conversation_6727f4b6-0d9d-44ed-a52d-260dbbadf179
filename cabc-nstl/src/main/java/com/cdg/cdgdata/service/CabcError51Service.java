package com.cdg.cdgdata.service;

import com.cdg.cdgdata.dto.*;
import com.cdg.cdgdata.entity.Iso8583Incoming;
import com.cdg.cdgdata.repository.*;
import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@Transactional(readOnly = true)
public class CabcError51Service {

    @Autowired
    private RctbTripIntfRepository rctbTripIntfRepository;

    @Autowired
    private EscJobRepository escJobRepository;

    @Autowired
    private EscTripDetailsRepository escTripDetailsRepository;

    @Autowired
    private Iso8583IncomingRepository iso8583IncomingRepository;

    @Autowired
    private PytbCabcBookingRefService pytbCabcBookingRefService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final String CONTROL_FILE = "CABC_ISO8583_CONTROL.txt";

    /**
     * Main method to process CABC Error 51 data
     */
    public CabcError51ResponseDto processData(String targetDay) {
        try {
            log.info("Starting CABC Error 51 processing for date: {}", targetDay);

            // Parse the target day - handle flexible date formats
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("d/M/yyyy HH:mm:ss");
            LocalDateTime startDate = LocalDateTime.parse(targetDay + " 00:00:00", formatter);
            LocalDateTime endDate = LocalDateTime.parse(targetDay + " 23:59:59", formatter);

            // Get NSTL data
            List<NstlDataDto> nstlData = getNstlData(startDate, endDate);
            log.info("Found {} NSTL records", nstlData.size());

            // Extract job numbers for further processing
            List<String> jobNumbers = nstlData.stream()
                .map(NstlDataDto::getJobNo)
                .toList();

            // Get Job CC data
            List<JobCcDataDto> jobCcData = getJobCcData(jobNumbers);
            log.info("Found {} Job CC records", jobCcData.size());

            // Get Trip data
            List<TripDataDto> tripData = getTripData(jobNumbers);
            log.info("Found {} Trip records", tripData.size());

            // Process PYTB data
            processPytbData(jobNumbers);

            // Get ISO data
            List<IsoDataDto> isoData = getIsoData();
            log.info("Found {} ISO records", isoData.size());

            // Generate Excel file
            String fileName = generateExcelFile(nstlData, jobCcData, tripData, isoData);

            // Create response
            CabcError51ResponseDto response = new CabcError51ResponseDto();
            response.setNstlData(nstlData);
            response.setJobCcData(jobCcData);
            response.setTripData(tripData);
            response.setIsoData(isoData);
            response.setFileName(fileName);
            response.setStatus("SUCCESS");
            response.setMessage("CABC Error 51 processing completed successfully");

            log.info("CABC Error 51 processing completed successfully");
            return response;

        } catch (Exception e) {
            log.error("Error processing CABC Error 51 data", e);
            CabcError51ResponseDto errorResponse = new CabcError51ResponseDto();
            errorResponse.setStatus("ERROR");
            errorResponse.setMessage("Error processing data: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * Get NSTL data from rctb_trip_intf table
     */
    private List<NstlDataDto> getNstlData(LocalDateTime startDate, LocalDateTime endDate) {
        return rctbTripIntfRepository.findNstlData(startDate, endDate);
    }

    /**
     * Get Job CC data from esc_job table
     */
    private List<JobCcDataDto> getJobCcData(List<String> jobNumbers) {
        if (jobNumbers.isEmpty()) {
            return new ArrayList<>();
        }
        return escJobRepository.findJobCcDataByJobNumbers(jobNumbers);
    }

    /**
     * Get Trip data from esc_trip_details table
     */
    private List<TripDataDto> getTripData(List<String> jobNumbers) {
        if (jobNumbers.isEmpty()) {
            return new ArrayList<>();
        }
        return escTripDetailsRepository.findTripDataByJobNumbers(jobNumbers);
    }

    /**
     * Process PYTB data by writing job numbers to file and calling external service
     */
    private void processPytbData(List<String> jobNumbers) {
        try {
            // Write job numbers to file for PYTB processing
            String fileName = Constants.getFolderPath() + "PYTB_CABC_BOOKINGREF.txt";
            try (FileWriter writer = new FileWriter(fileName)) {
                for (String jobNo : jobNumbers) {
                    writer.write(jobNo + "\r\n");
                }
            }

            // Call PYTB service
            pytbCabcBookingRefService.processData();

        } catch (Exception e) {
            log.error("Error processing PYTB data", e);
        }
    }

    /**
     * Get ISO data from iso8583_incoming table
     */
    private List<IsoDataDto> getIsoData() {
        try {
            // Read last message ID from control file
            Long lastRecord = readLastMessageId();
            
            // Get ISO data
            List<Iso8583Incoming> isoEntities = iso8583IncomingRepository.findIsoDataFromLastRecord(lastRecord);

            // Convert to DTOs
            List<IsoDataDto> isoData = isoEntities.stream()
                .map(entity -> new IsoDataDto(
                    entity.getNewValue(),  // Use the computed newValue from entity
                    entity.getPan(),
                    entity.getMsgId(),
                    entity.getTxnDate()
                ))
                .collect(java.util.stream.Collectors.toList());

            // Update control file with the latest message ID
            if (!isoData.isEmpty()) {
                Long latestMsgId = isoData.get(isoData.size() - 1).getMsgId();
                writeLastMessageId(latestMsgId);
            }

            return isoData;
            
        } catch (Exception e) {
            log.error("Error getting ISO data", e);
            return new ArrayList<>();
        }
    }

    /**
     * Read last message ID from control file
     */
    private Long readLastMessageId() {
        try {
            File file = new File(Constants.getFolderPath() + CONTROL_FILE);
            if (!file.exists()) {
                return 0L; // Default to 0 if file doesn't exist
            }
            
            try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                String lastLine = null;
                String line;
                while ((line = reader.readLine()) != null) {
                    lastLine = line;
                }
                return lastLine != null ? Long.parseLong(lastLine.trim()) : 0L;
            }
        } catch (Exception e) {
            log.error("Error reading last message ID", e);
            return 0L;
        }
    }

    /**
     * Write last message ID to control file
     */
    private void writeLastMessageId(Long msgId) {
        try {
            try (FileWriter writer = new FileWriter(Constants.getFolderPath() + CONTROL_FILE)) {
                writer.write(msgId.toString());
            }
        } catch (Exception e) {
            log.error("Error writing last message ID", e);
        }
    }

    /**
     * Generate Excel file with all the data
     */
    private String generateExcelFile(List<NstlDataDto> nstlData, List<JobCcDataDto> jobCcData, 
                                   List<TripDataDto> tripData, List<IsoDataDto> isoData) throws IOException {
        
        Workbook workbook = new XSSFWorkbook();
        
        // Create sheets
        createNstlSheet(workbook, nstlData);
        createJobCcSheet(workbook, jobCcData);
        createTripSheet(workbook, tripData);
        createIsoSheet(workbook, isoData);
        
        // Generate filename
        String fileName = Helper.GenerateFileName();
        String fullPath = Constants.getFolderPath() + fileName + ".xlsx";
        
        // Write to file
        try (FileOutputStream fileOut = new FileOutputStream(fullPath)) {
            workbook.write(fileOut);
        }
        
        workbook.close();
        return fileName + ".xlsx";
    }

    /**
     * Create NSTL sheet in the workbook
     */
    private void createNstlSheet(Workbook workbook, List<NstlDataDto> data) {
        Sheet sheet = workbook.createSheet("NSTL");
        
        // Create header style
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] headers = {"Row", "JOB_NO", "COMPLETED_DT", "RECON_DATE"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // Create data rows
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            NstlDataDto item = data.get(i);
            
            row.createCell(0).setCellValue(i + 1);
            row.createCell(1).setCellValue(item.getJobNo());
            row.createCell(2).setCellValue(item.getCompletedDt() != null ? item.getCompletedDt().toString() : "");
            row.createCell(3).setCellValue(item.getReconDate() != null ? item.getReconDate().toString() : "");
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * Create Job CC sheet in the workbook
     */
    private void createJobCcSheet(Workbook workbook, List<JobCcDataDto> data) {
        Sheet sheet = workbook.createSheet("JOBCC");
        
        // Create header style
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] headers = {"Row", "JOB_NO", "CC_NUMBER", "PAX_CONTACT"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // Create data rows
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            JobCcDataDto item = data.get(i);
            
            row.createCell(0).setCellValue(i + 1);
            row.createCell(1).setCellValue(item.getJobNo());
            row.createCell(2).setCellValue(item.getCcNumber());
            row.createCell(3).setCellValue(item.getPaxContact());
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * Create Trip sheet in the workbook
     */
    private void createTripSheet(Workbook workbook, List<TripDataDto> data) {
        Sheet sheet = workbook.createSheet("TRIP");
        
        // Create header style
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] headers = {"Row", "NEW", "JOB_NO"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // Create data rows
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            TripDataDto item = data.get(i);
            
            row.createCell(0).setCellValue(i + 1);
            row.createCell(1).setCellValue(item.getNewValue());
            row.createCell(2).setCellValue(item.getJobNo());
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * Create ISO sheet in the workbook
     */
    private void createIsoSheet(Workbook workbook, List<IsoDataDto> data) {
        Sheet sheet = workbook.createSheet("ISO");
        
        // Create header style
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] headers = {"Row", "NEW", "PAN", "MSG_ID", "TXN_DATE"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // Create data rows
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            IsoDataDto item = data.get(i);
            
            row.createCell(0).setCellValue(i + 1);
            row.createCell(1).setCellValue(item.getNewValue());
            row.createCell(2).setCellValue(item.getPan());
            row.createCell(3).setCellValue(item.getMsgId());
            row.createCell(4).setCellValue(item.getTxnDate() != null ? item.getTxnDate().toString() : "");
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }
}
