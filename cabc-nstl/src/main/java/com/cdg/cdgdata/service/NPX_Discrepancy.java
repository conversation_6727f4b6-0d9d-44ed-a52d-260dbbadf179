package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NPX_Discrepancy {

	//public static ArrayList<String> npxReports = new ArrayList<String>();
	public static ArrayList<String> setlTIDSTAN = new ArrayList<String>();
	public static ArrayList<String> stgTIDSTAN = new ArrayList<String>();
	public static ArrayList<String> discrepancy = new ArrayList<String>();

	public static void GetData(String[] npxReports) throws IOException {
				

		//dont use
		//String[] x__npxReports = { 
								//"NETSNPX_20240701.CSV"
								//};
		
		
		for (String i : npxReports) {
				GetNPXStage(i);
				GetSetle(i);
				CheckDiscrepancy(i);
				GetDetails(i);
		}
		
		//GetDetails();
		
			 
	}
	
	public static void GetDetails(String npxFilename) throws IOException {
		
		//String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + npxFilename.substring(0,16) + ".txt");
		FileWriter myWriter2 = new FileWriter(Constants.FolderPath + npxFilename.substring(0,16) + "_TRIP_DETAILS.txt");
		
		String TID = "";
		String STAN = "";
		String FILE_ID = "";
		String JOB_NO = "";
		
		try{
			
			  Connection con= Helper.GetConnection("ptccpro");  
			  Connection con2= Helper.GetConnection("ptfspro"); 
			  Statement stmt=con.createStatement();  
			  Statement stmt2=con.createStatement(); 
			  Statement stmt3=con.createStatement(); 
			  Statement stmt4=con2.createStatement(); 
			  
				for (int i = 0; i < discrepancy.size(); i++) {
					System.out.println(discrepancy.get(i));
					String value = discrepancy.get(i);
					String[] details = value.split(",");
					TID = details[0];
					STAN = details[1];
					FILE_ID = details[2];
					String records = "";
					String records2 = "";
					String card_fileid = "";
					
			    	  String sqlString = new StringBuilder()
			    	            .append("SELECT TERMINAL_ID, STAN, CAN_NO, financial_institution_id,  FILE_ID FROM cn2_recon.esc_recon_setl_netsnpx_stg P ")
			    	            .append("WHERE FILE_ID = '" + FILE_ID + "'")
			    	            .append(" AND TERMINAL_ID = '" + TID + "' " + " AND STAN = '" + STAN + "' ")
			    	            .toString();
					  			    	  
			          log.info("Value: {}", sqlString);
			          		    	  
			    	  ResultSet rs=stmt.executeQuery(sqlString);
			    	  ResultSetMetaData rsmd = rs.getMetaData();
			    	  int columnsNumber = rsmd.getColumnCount();
			    	  
					  while(rs.next())
					  {			  
						  //CARD_TYPE = rs.getString(1);
						  for (int p = 1; p < columnsNumber+1; p++) {
							   records+=rs.getString(p) + "\t";
							   if(p>3) {
								   card_fileid=rs.getString(p) + "\t" + card_fileid;
							   }
						  };
						  
						  //myWriter.write(records + "\r\n");
						  //records="";
					  }
					  
			    	  String sqlString2 = new StringBuilder()
			    	            .append("SELECT JOB_NO,	TRANS_AMOUNT,	TRANS_DT,	FARE_GST,	FARE_ADMIN,	FARE_AMOUNT,	VEHICLE_ID,	REMARkS, TID, system_trace_audit_no, APPROVAL_CODE, DRIVER_ID FROM esc_payment_transaction ")
			    	            .append(" WHERE tid = '" + TID + "' " + " AND system_trace_audit_no = '" + STAN + "' ")
			    	            .append(" AND TRANS_TYPE in ('0210','0230' ) " )
			    	            .append(" AND PAYMENT_METHOD = 'NPX'" )
			    	            .toString();
					  			    	  
			          log.info("Value: {}", sqlString2);
			          		    	  
			    	  ResultSet rs2=stmt2.executeQuery(sqlString2);
			    	  ResultSetMetaData rsmd2 = rs2.getMetaData();
			    	  int columnsNumber2 = rsmd2.getColumnCount();
			    	  
			    	  int countrow = 0;
			    	  
					  while(rs2.next())
					  {			  
						  //JOB_NO = rs2.getString(1);
						  for (int q = 1; q < columnsNumber2+1; q++) {
							   records+=rs2.getString(q) + "\t";
							   if (q==1) {
								   JOB_NO = rs2.getString(1);
							   }
						  };
						  
						  myWriter.write(records + "\r\n");
						  records="";
						  countrow++;
					  }
					  
					  if(countrow==0) {
						  //records="";
				    	  String sqlString3 = new StringBuilder()
				    	            .append("SELECT JOB_NO,	TRANS_AMOUNT,	TRANS_DT,	FARE_GST,	FARE_ADMIN,	FARE_AMOUNT,	VEHICLE_ID,	REMARkS, TID, system_trace_audit_no, APPROVAL_CODE, DRIVER_ID FROM esc_payment_transaction ")
				    	            .append(" WHERE tid = '" + TID + "' " + " AND system_trace_audit_no = '" + STAN + "' ")
				    	            .append(" AND TRANS_TYPE in ('0200' ) " )
				    	            .append(" AND PAYMENT_METHOD = 'NPX'" )
				    	            .toString();
						  			    	  
				          log.info("Value: {}", sqlString3);
				          		    	  
				    	  ResultSet rs3=stmt3.executeQuery(sqlString3);
				    	  ResultSetMetaData rsmd3 = rs3.getMetaData();
				    	  int columnsNumber3 = rsmd3.getColumnCount();
				    	  
						  while(rs3.next())
						  {			  
							  //JOB_NO = rs2.getString(1);
							  for (int q = 1; q < columnsNumber3+1; q++) {
								   records+=rs3.getString(q) + "\t";
								   if (q==1) {
									   JOB_NO = rs3.getString(1);
								   }
							  };
							  
							  myWriter.write(records + "\r\n");
							  records="";
						  }
					  }
					  
					  //Get trip details
			    	  String sqlString4 = new StringBuilder()
			    	            .append("SELECT * from rctb_trip_intf ")
			    	            .append(" WHERE job_no = '" + JOB_NO + "'")
			    	            .toString();
					  			    	  
			          log.info("Value: {}", sqlString4);
			          		    	  
			    	  ResultSet rs4=stmt4.executeQuery(sqlString4);
			    	  ResultSetMetaData rsmd4 = rs4.getMetaData();
			    	  int columnsNumber4 = rsmd4.getColumnCount();
			    	  
					  while(rs4.next())
					  {			  
						  //JOB_NO = rs2.getString(1);
						  for (int q = 1; q < columnsNumber4+1; q++) {
							   records2+=rs4.getString(q)  + "\t";
						  };
						  records2 = records2.replace("null", "");
						  myWriter2.write(card_fileid + records2 + "\r\n");
						  records2="";
					  }

					 
				}
				

		    	  
			  myWriter.close();
			  myWriter2.close();
			  
			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
		

		
	}

	public static void CheckDiscrepancy(String fileid) {
		
		discrepancy.clear();
		
		for (int i = 0; i < stgTIDSTAN.size(); i++) {
			boolean exist = false;
			String TIDSTAN = stgTIDSTAN.get(i);
			exist = setlTIDSTAN.contains(TIDSTAN);

			if (exist == false) {
				discrepancy.add(TIDSTAN.substring(0,8) + "," + TIDSTAN.substring(8,14) + "," + fileid + ",");
			}
		
		}
		
	}
	
	public static void GetNPXStage(String fileid) {
		
		stgTIDSTAN.clear();
		
		try{
			
			  Connection con= Helper.GetConnection("ptccpro");    
			  Statement stmt=con.createStatement();  
		    	  
		    	  String sqlString = new StringBuilder()
		    	            .append("SELECT TERMINAL_ID||STAN FROM cn2_recon.esc_recon_setl_netsnpx_stg P ")
		    	            .append("WHERE FILE_ID = '" + fileid + "'")
		    	            .append(" AND financial_institution_id in ('MasterCard', 'VISA', 'AMEX', 'JCB') ")
		    	            //.append(" AND financial_institution_id in ('AMEX') ")
		    	            //.append(" AND financial_institution_id in ('VISA') ")
		    	            .toString();
 				  			    	  
		          log.info("Value: {}", sqlString);
		          		    	  
		    	  ResultSet rs=stmt.executeQuery(sqlString);

				  while(rs.next())
				  {			  
					  stgTIDSTAN.add(rs.getString(1));
				  }
			

			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
	}
	
	
	public static void GetSetle(String fileid) {
		
		setlTIDSTAN.clear();
		
		try{
			
			  Connection con= Helper.GetConnection("ptfspro");    
			  Statement stmt=con.createStatement();  
		    	  
		    	  String sqlString = new StringBuilder()
		    	            .append("SELECT TID||STAN FROM rctb_setl ")
		    	            .append("WHERE FILE_ID = '" + fileid + "' " )
		    	            .append("  and card_type in ('M','V','A','J')" )
		    	            //.append("  and card_type in ('A')" )
		    	            .toString();
 				  			    	  
		          log.info("Value: {}", sqlString);
		          		    	  
		    	  ResultSet rs=stmt.executeQuery(sqlString);

				  while(rs.next())
				  {			  
					  setlTIDSTAN.add(rs.getString(1));
				  }
			

			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
	}
}
