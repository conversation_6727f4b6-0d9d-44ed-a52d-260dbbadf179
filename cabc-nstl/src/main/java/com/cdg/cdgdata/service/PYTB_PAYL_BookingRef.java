package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PYTB_PAYL_BookingRef {
	
	public static ArrayList<Object[]> jobList = new ArrayList<Object[]>();
	
	public static void GetData() throws IOException{
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		
		String jobs = TripsJobList();
		GetEscJobDcp(jobs);
		
		try{		  	  
			Connection con= Helper.GetConnection("ptccpro_dcp");  
			  Statement stmt=con.createStatement();  

			 String columns = "";
			 String job = "";
			 String bookingref = "";
			 
		      for (int counter = 0; counter < jobList.size(); counter++) { 
		    	 
				  job = jobList.get(counter)[0].toString();
				  bookingref = jobList.get(counter)[1].toString();
				  
		    	  String sqlString = new StringBuilder()
		    	            .append("SELECT * FROM pytb_wd_txn P ")
		    	            .append("WHERE Booking_REF = '" + bookingref + "' ")
		    	            .append("AND TXN_TYPE = 'purchase' " )
		    	            .append("order by P.WD_TXN_ID")
		    	            .toString();
		    	  
		    	  log.info("Value: {}", sqlString);
		    	  
		    	  ResultSet rs=stmt.executeQuery(sqlString);
				  ResultSetMetaData rsmd = rs.getMetaData();

				  int columnsNumber = rsmd.getColumnCount();
			

				  String records = "";

			
				  while(rs.next())
				  {     
					  if(columns=="")
					  {
						  myWriter.write("JOB_NO \t");
						  for (int i = 1; i < columnsNumber+1; i++) {
							  columns+=rsmd.getColumnName(i) + "\t";    
							};
							myWriter.write(columns + "\r\n");
					  }
					  
					  myWriter.write(job + "\t");
					  
					  for (int i = 1; i < columnsNumber+1; i++) {

						   records+=rs.getString(i) + "\t";
					  };
					  myWriter.write(records + "\r\n");
					  records="";
				  };
		    	  
		    	  
		      }   	


			  myWriter.close();
			  con.close();
			  
			  }catch(Exception e){ log.error("Error occurred", e);}
	}
	


	
	
	public static void WriteExcel(ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList, String filename) throws FileNotFoundException, IOException {
		Workbook wb = new XSSFWorkbook();
		Sheet sheet1 = wb.createSheet("Sheet1");

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i+1);
	        headerCell.setCellValue(columnList.get(i));
	        headerCell.setCellStyle(style);
		
		};
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell headerCell = recordRow.createCell(j+1);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(2)) {
		        		headerCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		headerCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	headerCell.setCellValue("");
		        }

			};
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<columnList.size()+1;i++) {
			sheet1.autoSizeColumn(i);
		}
		

		
		try (OutputStream fileOut = new FileOutputStream(Constants.FolderPath +  filename)) {
		    wb.write(fileOut);
		}
 	}
 
 
	public static String TripsJobList() throws IOException {
		
		 //Load data
		 ArrayList<String[]> jobList = Helper.ReadDataSource("PYTB_PAYL_BOOKINGREF.txt");
		 
		String jobs = "";
		
		 for (int counter = 0; counter < jobList.size(); counter++) { 	
			 jobs = "'" + jobList.get(counter)[0] + "'," + jobs;
		}
				 
		jobs = jobs.substring(0,jobs.length()-1);
		
		return jobs;
	}
	
	public static void GetEscJobDcp(String jobs) throws IOException {
		
		String sqlString = new StringBuilder()
				.append(" select job_no, dcp_request_id from esc_job  ")
				.append(" where job_no in ( " + jobs + ") ")
				.append(" and  dcp_request_id is not null ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptccpro");
			Statement stmt = con.createStatement();			
			
			log.info("Value: {}", sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();
		
			  while(rs.next())
			  {     	  
				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  jobList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
	
	}
	


}


