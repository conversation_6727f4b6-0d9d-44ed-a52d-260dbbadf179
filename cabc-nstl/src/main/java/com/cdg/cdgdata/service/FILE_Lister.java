package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FILE_Lister {
	

	public static void GetData(String folder) throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		    
		try{
 
		    File path = new File(folder);

		    File [] files = path.listFiles();
		    for (int i = 0; i < files.length; i++){
		        if (files[i].isFile()){ //this line weeds out other directories/folders
		            BasicFileAttributes attr =
		                    Files.readAttributes(files[i].toPath(), BasicFileAttributes.class);
		        	myWriter.write(files[i].getName() + "\t" + attr.size() + "\t" + attr.lastModifiedTime() + "\r\n");
		            log.info("Processing file: {}", files[i]);
		        }
		    }
		
		myWriter.close();
	
		
	}catch(Exception e){ log.error("Error occurred", e);}

}

}
