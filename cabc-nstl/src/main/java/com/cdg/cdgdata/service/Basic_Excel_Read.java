package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Iterator;

import org.apache.poi.sl.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


@Slf4j
public class Basic_Excel_Read {

	public static void GetData() throws IOException {
		try
        {
            //FileInputStream file = new FileInputStream(new File("C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\CDGData-eclipse\\data\\dbsfile.xlsx"));
 
            FileInputStream file = new FileInputStream(new File("C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\CDGData-eclipse\\data\\ComfortBatch\\Comfort batch details.xlsx"));
            
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
 
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
 
            //Iterate through each rows one by one
            Iterator<Row> rowIterator = sheet.iterator();
            while (rowIterator.hasNext()) 
            {
                Row row = rowIterator.next();
                //For each row, iterate through all the columns
                Iterator<Cell> cellIterator = row.cellIterator();
                 
                while (cellIterator.hasNext()) 
                {
                    Cell cell = cellIterator.next();
                    //Check the cell type and format accordingly
                    switch (cell.getCellType()) 
                    {
                        case NUMERIC:
                            System.out.print(cell.getNumericCellValue() + "\t");
                            break;
                        case STRING:
                            System.out.print(cell.getStringCellValue() + "\t");
                            break;
					default:
						break;
 
                        	
                        
                    }
                }
                log.info("");
            }
            workbook.close();
            file.close();
            
        } 
        catch (Exception e) 
        {
            e.printStackTrace();
        }
	}
}
