package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NFP_SettleAckParser {
	
	public static void GetData() throws IOException {
		
		ArrayList<String[]> list = Helper.ReadDataSource("G5Q012543 - settle");
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".csv");

		try{
			
			String line; 
			for (int counter = 0; counter < list.size(); counter++) {

				line = list.get(counter)[0].toString();
				myWriter.write(line + "\r\n");
			}
			
			myWriter.close();
			
		}catch(Exception e){ log.error("Error occurred", e);}
		
	}

}
