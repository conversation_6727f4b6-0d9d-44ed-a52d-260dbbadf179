package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


@Slf4j
public class PAYL_TripCapture {

	
	public static ArrayList<Object[]> nstlList = new ArrayList<Object[]>();
	public static  ArrayList<String> nstlcolumnList = new ArrayList<String>();
	public static  ArrayList<Integer> nstlcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> resultList = new ArrayList<Object[]>();
	public static ArrayList<String> columnList = new ArrayList<String>();
	public static ArrayList<Integer> columnTypeList = new ArrayList<Integer>();
	
	public static Workbook wbfinal = new XSSFWorkbook();

	public static void GetData(String TARGET_DAY) throws IOException {
		
		GetCompletedTrips(TARGET_DAY);
		GetTMTBCapture(TARGET_DAY);
		
		WriteWBSheets(wbfinal, "TRIP", nstlcolumnList, nstlcolumnTypeList, nstlList);
		WriteWBSheets(wbfinal, "CAPTURE", columnList, columnTypeList, resultList);
				
		String filename = Helper.GenerateFileName();
		try (OutputStream fileOut = new
		FileOutputStream(Constants.FolderPath + filename + ".xlsx")) { wbfinal.write(fileOut);
		wbfinal.close(); }
		   
		log.info("PAYL_TripCapture done.");
	}
	


	public static void GetTMTBCapture(String TARGET_DAY) throws IOException {

		try {
			Connection con = Helper.GetConnection("ptccpro_tm");
			Statement stmt = con.createStatement();

			 //Load data
			 ArrayList<String[]> list = ToListNSTL(); //Helper.ReadDataSource("RCTB_Trip_JOB.txt");
			 int total = list.size();
			 
			 int chunk = 1000;
			 int cnt = 0;
			 String jobs = "";
			 String columns = "";
			 for (int counter = 0; counter < list.size(); counter++) { 
				 
				 cnt++;
				 jobs = "'" + list.get(counter)[0] + "'," + jobs;
				 if(chunk==cnt || counter+1 == total)
				 {
					 
					 jobs = jobs.substring(0,jobs.length()-1);
					 
			    	  String sqlString = new StringBuilder()
			    			  .append("SELECT JOB_NUMBER, STATUS, BOOKING_NUM, DATE_TIME ")
			    			  .append(" from tmtb_pl_txn_log ")
			    			  .append(" WHERE JOB_NUMBER in (" + jobs + ") ")
			    			  .append(" AND MSG_TYPE in ('pl_capture')" )
			    			  .append(" and DATE_TIME >= TO_DATE('" + TARGET_DAY + " 00:00:00','DD/MM/YYYY HH24:MI:SS') - 30")
			    	          .toString();
					 
					 log.info(sqlString);
					 
			    	  ResultSet rs=stmt.executeQuery(sqlString);
					  ResultSetMetaData rsmd = rs.getMetaData();

					  int columnsNumber = rsmd.getColumnCount();
				
					  while(rs.next())
					  {     
						  if(columns=="")
						  {

							  for (int i = 0; i < columnsNumber; i++) {
								  columnList.add(rsmd.getColumnName(i+1).toString());
								  columnTypeList.add(rsmd.getColumnType(i+1));
								};
								
							columns = "DONE";
						  }
						  

						  Object[] recList = new Object[columnsNumber];
						  for (int i = 0; i < columnsNumber ; i++) {
							  recList[i] = rs.getString(i+1);  
							   
						  };
						  resultList.add(recList);
						  
					  };
					 
					 
					 jobs = "";
					 cnt=0;
				 }

			 }
				
			con.close();

		} catch (SQLException e) {
			e.printStackTrace();
		}


	}

	
	public static void GetCompletedTrips(String TARGET_DAY) throws IOException {
		
		String sqlString = new StringBuilder()
				.append(" select job_no, completed_dt, recon_date, error_code, job_status,valid_trip   from rctb_trip_intf  ")
				.append(" where payment_mode in ( 'DBSPOF', 'PLAH5') ")
				.append(" and COMPLETED_DT >= TO_DATE('" + TARGET_DAY + " 00:00:00','DD/MM/YYYY HH24:MI:SS') ")
				.append(" and COMPLETED_DT <= TO_DATE('" + TARGET_DAY + " 23:59:59','DD/MM/YYYY HH24:MI:SS') ")
				//.append(" and STATUS = 'E' AND ERROR_CODE = 'NSTL' and FMS_STATUS = 'C' order by completed_dt asc ")
				//.append(" and STATUS = 'E' AND ERROR_CODE = 'NSTL'  order by completed_dt asc ")
				//.append(" and STATUS = 'E' AND ERROR_CODE = 'NSTL' and entity='PRIM'  order by completed_dt asc ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  nstlcolumnList.add(rsmd.getColumnName(i+1).toString());
						  //nstlcolumnTypeList.add(rsmd.getColumnType(i+1));
						  if ((i+1)==1) {
							  nstlcolumnTypeList.add(8);
						  }
						  else {
							  nstlcolumnTypeList.add(rsmd.getColumnType(i+1));
						  }
						  
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  nstlList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		
	}
	
	public static ArrayList<String[]> ToListNSTL() {
		ArrayList<String[]> list = new ArrayList<String[]>();
		String[] data = null;
		
		for (int i = 0; i < nstlList.size(); i++) { 
			Object[] recordObj = nstlList.get(i);
			data = recordObj[0].toString().split("\\s+"); 
			list.add(data);
		};
		
		return list;
	}
	
	public static Workbook WriteWBSheets(Workbook wb, String sheetname, ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList) throws IOException {
		Sheet sheet1 = wb.createSheet(sheetname);

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i+1);
	        headerCell.setCellValue(columnList.get(i));
	        headerCell.setCellStyle(style);
		
		};
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell recordCell = recordRow.createCell(j+1);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(8)) {
		        		recordCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		recordCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	recordCell.setCellValue("");
		        }

			};
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<columnList.size()+1;i++) {
			sheet1.autoSizeColumn(i);
		}
		
		return wb;
	}

	
}
