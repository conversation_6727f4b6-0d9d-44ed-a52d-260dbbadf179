package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Blob;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class  TMTB_TMSTxnLog {


	public static void GetData() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		
			try{		  	  
				  Connection con= Helper.GetConnection("ptccpro_tm");    
				  Statement stmt=con.createStatement();  
				  
				 //Load data
				 ArrayList<String[]> list = Helper.ReadDataSource("TMTB_TMSTXNLOG.txt");
				 int total = list.size();
				 
				 int chunk = 1000;
				 int cnt = 0;
				 String  id = "";
				 for (int counter = 0; counter < list.size(); counter++) { 
					 
					 cnt++;
					 id = "" + list.get(counter)[0] + "," + id;
					 if(chunk==cnt || counter+1 == total)
					 {
						 
						 id = id.substring(0,id.length()-1);
						 
				    	  String sqlString = new StringBuilder()
				    	            //.append("select booking_num, msg_type from tmtb_pl_txn_log  ")
				    	          .append("select * from tmtb_txn_log ")  
				    			  .append("WHERE id in (" + id + ") ")
				    			  //.append("and msg_type in ('pl_capture' )")
				    			  //.append("and msg_type in ('pl_preauth_off', 'pl_preauth_on' )")
				    	            .toString();
						 
						 log.info("Value: {}", sqlString);
						 
				    	  ResultSet rs=stmt.executeQuery(sqlString);
						  ResultSetMetaData rsmd = rs.getMetaData();

						  int columnsNumber = rsmd.getColumnCount();
					
						  String columns = "";
						  String records = "";

					
						  while(rs.next())
						  {     
							  if(columns=="")
							  {
								  for (int i = 1; i < columnsNumber+1; i++) {
									  columns+=rsmd.getColumnName(i) + "\t";    
									};
									myWriter.write(columns + "\r\n");
							  }
							  

							  for (int i = 1; i < columnsNumber+ 1; i++) {
								  if (i==2) {
									  //Blob blob = rs.getBytes(i); // .getBlob(i);
									  //byte[] bdata = blob.getBytes(1, (int) blob.length());
									  byte[] bdata = rs.getBytes(i);
									  String text = new String(bdata);
									  records+= text + "\t";
								  }
								  else {
									  records+=rs.getString(i) + "\t";
								  }
								   
							  };
							  myWriter.write(records + "\r\n");
							  records="";
						  };
						 
						 
						  id = "";
						 cnt=0;
					 }
					 

					 
				 }
				  

				  myWriter.close();
				  con.close();
				  
				  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	
	
}
