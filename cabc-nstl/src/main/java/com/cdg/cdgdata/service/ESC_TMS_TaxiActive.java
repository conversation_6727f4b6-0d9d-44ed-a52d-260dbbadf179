package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ESC_TMS_TaxiActive {


	public static void GetData() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		ArrayList<String> taxipayList = new ArrayList<String>();
		ArrayList<String> taxigroupList = new ArrayList<String>();
	
			try{		  	  
				  Connection con= Helper.GetConnection("ptccpro_rst");    
				  Statement stmt=con.createStatement();  
				  
		    	  String sqlString = new StringBuilder()
		    	            .append("SELECT distinct(vehicle_ID) FROM citynet2.esc_payment_transaction P ")
		    	            .append(" WHERE src = 'ABL' ")
		    	            .append(" AND vehicle_ID like ('S%') ")
		    	            .append(" AND (vehicle_id not in (select vehicle_id from cn2tmsys.tmtb_vehicle_group where group_id = 1954)" )
		    	            .append(" AND vehicle_id not in ('SHC3107X'))" )
		    	            //.append(" and log_dt >= TO_DATE(\'" + dateday + "\' 00:00:00\',\'DD/MM/YYYY HH24:MI:SS\')")
			    	        .toString();
	 				  
		    	  ResultSet rs=stmt.executeQuery(sqlString);
 
		    	  log.info("Value: {}", sqlString);
					  
				  while(rs.next())
					  {     
					  	taxipayList.add(rs.getString(1));
					  };
				    	  
				  con.close();
				  
				  /////
				  Connection con2= Helper.GetConnection("ptccpro_tm");    
				  Statement stmt2=con2.createStatement();  
				  
		    	  String sqlString2 = new StringBuilder()
		    	            .append("select vehicle_id from tmtb_vehicle_group ")
		    	            .append(" where group_id in ( 1934) ")
			    	        .toString();
	 				  
		    	  ResultSet rs2=stmt2.executeQuery(sqlString2);
 
		    	  log.info("Value: {}", sqlString2);
					  
				  while(rs2.next())
					  {     
					  	taxigroupList.add(rs2.getString(1));
					  };
				    	  
				  con2.close();
				  
				  	//taxipayList.add("MEMEL");
				    List<String> differences = new ArrayList<>(taxipayList);
				    
				    differences.removeAll(taxigroupList);
				  
				    if(differences.size()>0) {
				    	for (int i = 0; i < differences.size(); i++) {
				    		myWriter.write(differences.get(i) + "\r\n");
				    	}
				    }
				    else {
				    	myWriter.write("NONE");
				    }
				    
				    myWriter.close();
				  
			}catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	
	
}
