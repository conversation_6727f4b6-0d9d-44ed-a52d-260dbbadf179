package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


@Slf4j
public class Basic_Sql_To_Excel {



	public static void GetData() throws IOException {
		
		ArrayList<Object[]> resultList = new ArrayList<Object[]>();
		ArrayList<String> columnList = new ArrayList<String>();
		ArrayList<Integer> columnTypeList = new ArrayList<Integer>();
		
		try {
			Connection con = Helper.GetConnection("ptccpro");
			Statement stmt = con.createStatement();

			String sqlString = new StringBuilder().append("SELECT * FROM citynet2.esc_payment_transaction ")
					.append("WHERE job_no in ( '5225239452' ) ")
					.toString();

			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";

		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  columnList.add(rsmd.getColumnName(i+1).toString());
						  columnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  resultList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}

 
		WriteExcel(columnList,columnTypeList,resultList);
		   
		
	}

	
 public static void WriteExcel(ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList) throws FileNotFoundException, IOException {
		Workbook wb = new XSSFWorkbook();
		Sheet sheet1 = wb.createSheet("Sheet1");

		//WRITE THE COLUMN NAMES
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i);
	        headerCell.setCellValue(columnList.get(i));
		
		};
		
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell recordCell = recordRow.createCell(j);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(2)) {
		        		recordCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		recordCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	recordCell.setCellValue("");
		        }

			};
		
		};
		
		String filename = Helper.GenerateFileName();
		
		try (OutputStream fileOut = new FileOutputStream(Constants.FolderPath + filename + ".xlsx")) {
		    wb.write(fileOut);
		}
 }
 
 
}
