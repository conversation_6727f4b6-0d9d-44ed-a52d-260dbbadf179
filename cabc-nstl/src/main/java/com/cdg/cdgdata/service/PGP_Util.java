package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;

import org.c02e.jpgpj.Decryptor;
import org.c02e.jpgpj.Encryptor;
import org.c02e.jpgpj.HashingAlgorithm;
import org.c02e.jpgpj.CompressionAlgorithm;
import org.c02e.jpgpj.EncryptionAlgorithm;
import org.c02e.jpgpj.Key;
import org.c02e.jpgpj.Subkey;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class PGP_Util {
    // pubKey is the RECIPIENT public key
    private static Key pubKey;

    // priKey is the SENDER private key
    private static  Key priKey;

	public static void GetData(String plain) throws Exception {
 
		log.debug("pgp its me");
		
        pubKey = getKeyFromFile(new File("C:\\Intellij\\Pgpproject\\appadmin_0x3D2B634A_public.asc"));
        priKey = getKeyFromFile(new File("C:\\Intellij\\Pgpproject\\appadmin_0x3D2B634A_SECRET.asc"), new String("test1"));

        byte[] cipher = signAndEncryptData(priKey, pubKey, plain.getBytes(), true);
        log.info(new String(cipher));


        log.info("****DECRYPTED MESSAGE*****");

        String encMessage = new String(cipher);
        byte[] recover = decryptAndVerifyData(priKey, pubKey, encMessage.getBytes());
        log.info(new String(recover));


	    log.info("GetData done.");

	}
	

	   public static Key getKeyFromFile(File file) throws Exception {
	        Key key = new Key(file);
	        return key;
	    }

	    /**
	     * read key from file
	     *
	     * @param file
	     * @param passphrase
	     * @return
	     * @throws Exception
	     */
	    public static Key getKeyFromFile(File file, String passphrase) throws Exception {
	        Key key = new Key(file, passphrase);
	        return key;
	    }

	    /**
	     * sign and encrypt data, returns encrypted data.
	     *
	     * if signing is note required, signKey may be null
	     *
	     * @param signKey signing key, null if signing is not required
	     * @param encKey encryption key
	     * @param data plaintext data
	     * @param armor true if output is in armor format
	     * @return ciphertext
	     * @throws Exception
	     */
	    public static byte[] signAndEncryptData(Key signKey, Key encKey, byte[] data, boolean armor) throws Exception {
	        Key targetKey = new Key();
	        if (signKey != null) {
	            addSigningKey(targetKey, signKey);
	        }
	        addEncryptionKey(targetKey, encKey);

	        Encryptor encryptor = new Encryptor(targetKey);
	        encryptor.setAsciiArmored(armor);
	        encryptor.setCompressionAlgorithm(CompressionAlgorithm.ZIP);
	        encryptor.setSigningAlgorithm(HashingAlgorithm.SHA256);
	        encryptor.setEncryptionAlgorithm(EncryptionAlgorithm.AES256);
	        //log.info("signKey--->" + signKey);
	        if (signKey == null) {
	            log.debug("signKey is null");
	            // if signKey is not present, must set signing algorithm to HashingAlgorithm.Unsigned
	            // else, encryptor will throw exception
	            encryptor.setSigningAlgorithm(HashingAlgorithm.Unsigned);
	        }

	        ByteArrayInputStream in = new ByteArrayInputStream(data);
	        ByteArrayOutputStream out = new ByteArrayOutputStream(data.length);

	        encryptor.encrypt(in, out);

	        return out.toByteArray();
	    }

	    /**
	     * verify and decrypt data, returns plaintext data
	     *
	     * if verify signature not required, verifyKey may be null.
	     *
	     * if verifyKey is not null, but ciphertext does not contain signature,
	     * an exception will be thrown
	     *
	     * @param verifyKey verify key, null if verify signature is not required
	     * @param decKey decryption key
	     * @param data ciphertext data
	     * @return plaintext data
	     * @throws Exception
	     */
	    public static byte[] decryptAndVerifyData(Key verifyKey, Key decKey, byte[] data) throws Exception {
	        Key targetKey = new Key();
	        if (verifyKey != null) {
	            addVerificationKey(targetKey, verifyKey);
	        }
	        addDecryptionKey(targetKey, decKey);

	        Decryptor decryptor = new Decryptor(targetKey);
	        if (verifyKey == null) {
	            log.debug("verifyKey is null");
	            // if verifyKey not present, must set verificationRequired to false
	            // else, decryptor will throw exception
	            decryptor.setVerificationRequired(false);
	        }

	        ByteArrayInputStream in = new ByteArrayInputStream(data);
	        ByteArrayOutputStream out = new ByteArrayOutputStream(data.length);

	        decryptor.decrypt(in, out);

	        return out.toByteArray();
	    }

	    /**
	     * find encryption keys from sourceKey to add into targetKey
	     *
	     * @param targetKey
	     * @param sourceKey
	     */
	    private static void addEncryptionKey(Key targetKey, Key sourceKey) {
	        log.debug("addEncryptionKey");
	        for (Subkey subkey: sourceKey.getSubkeys()) {
	            // only add subkey if encryption flag is set
	            if (subkey.isForEncryption()) {
	                log.debug("addEncryptionKey: adding " + subkey + ", id=" + subkey.getId());
	                targetKey.getSubkeys().add(subkey);
	            } else {
	                log.debug("addEncryptionKey: NOT adding " + subkey + ", id=" + subkey.getId());
	            }
	        }
	        log.debug("addEncryptionKey done");
	    }

	    /**
	     * find decryption keys from source key to add into targetKey
	     *
	     * @param targetKey
	     * @param sourceKey
	     */
	    private static void addDecryptionKey(Key targetKey, Key sourceKey) {
	        log.debug("addDecryptionKey");
	        for (Subkey subkey: sourceKey.getSubkeys()) {
	            log.debug("addDecryptionKey: adding " + subkey + ", id=" + subkey.getId());
	            // only add subkey if it is an encryption key
	            if (null != subkey.getPublicKey() && subkey.getPublicKey().isEncryptionKey()) {
	                log.debug("addDecryptionKey: adding " + subkey + ", id=" + subkey.getId());
	                subkey.setForDecryption(true);
	                targetKey.getSubkeys().add(subkey);
	            } else {
	                log.debug("addDecryptionKey: NOT adding " + subkey + ", id=" + subkey.getId());
	            }
	        }
	        log.debug("addDecryptionKey done");
	    }

	    /**
	     * find signing keys from source key to add into targetKey
	     *
	     * @param targetKey
	     * @param sourceKey
	     */
	    private static void addSigningKey(Key targetKey, Key sourceKey) {
	        log.debug("addSigningKey");
	        for (Subkey subkey: sourceKey.getSubkeys()) {
	            // only add subkey if signing flag is set
	            if (subkey.isForSigning()) {
	                log.debug("addSigningKey: adding " + subkey + ", id=" + subkey.getId());
	                targetKey.getSubkeys().add(subkey);
	            } else {
	                log.debug("addSigningKey: NOT adding " + subkey + ", id=" + subkey.getId());
	            }
	        }
	        log.debug("addSigningKey done");
	    }

	    /**
	     * find verification keys from source key to add into targetKey
	     *
	     * @param targetKey
	     * @param sourceKey
	     */
	    private static void addVerificationKey(Key targetKey, Key sourceKey) {
	        log.debug("addVerificationKey");
	        for (Subkey subkey: sourceKey.getSubkeys()) {
	            // only add subkey if verification flag is set
	            if (subkey.isForVerification()) {
	                log.debug("addVerificationKey: adding " + subkey + ", id=" + subkey.getId());
	                targetKey.getSubkeys().add(subkey);
	            } else {
	                log.debug("addVerificationKey: NOT adding " + subkey + ", id=" + subkey.getId());
	            }
	        }
	        log.debug("addVerificationKey done");
	    }
	
}
 

