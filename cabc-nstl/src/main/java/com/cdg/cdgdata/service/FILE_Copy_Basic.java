package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;


@Slf4j
public class FILE_Copy_Basic {
	

	public static void GetData() throws IOException {
		
		String filename = "";
		String filerename = "";
		
		try{
			 
		    File path = new File("C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\Backup\\mydocuments\\Investigations\\Alipay issues\\reconaliac\\");

		    File [] files = path.listFiles();
		    for (int i = 0; i < files.length; i++){
		        if (files[i].isFile()){ //this line weeds out other directories/folders
		            BasicFileAttributes attr =
		                    Files.readAttributes(files[i].toPath(), BasicFileAttributes.class);
		        	//myWriter.write(files[i].getName() + "\t" + attr.size() + "\t" + attr.lastModifiedTime() + "\r\n");
		            log.info("Processing file: {}", files[i]);
		            
		            filename = files[i].getName();
		            filerename = filename.substring(0, 24);
		            
			        Path source = Paths.get(path + "\\" + filename);
				    Path destination = Paths.get(path + "\\aliac_rename\\" + filerename);

				    if(Files.exists(source) && !Files.exists(destination))
				    {
				        Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING);					    
					    log.info("Value: {}", filename);
				    }
		        }
		    }
		
	 
		
		}catch(Exception e){ log.error("Error occurred", e);}

	}
}
