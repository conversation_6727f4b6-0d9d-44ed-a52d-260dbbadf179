package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RCTB_DASH_Discrepancy {

	public static ArrayList<String> GetFiles() {
		ArrayList<String> list = new ArrayList<String>();

		File folder = new File(Constants.FolderPath + "/DASH/");
		File[] listOfFiles = folder.listFiles();

		for (File file : listOfFiles) {
			if (file.isFile()) {
				list.add(file.getName());
			}
		}

		return list;
	}

	public static ArrayList<String> ReadDashRecon(String filename) {

		ArrayList<String> result = new ArrayList<String>();

		try {
			File file = new File(Constants.FolderPath + "/DASH/" + filename); // creates a new file instance
			FileReader fr = new FileReader(file); // reads the file
			BufferedReader br = new BufferedReader(fr); // creates a buffering character input stream

			String line;
			while ((line = br.readLine()) != null) {
				result.add(line);
			}
			fr.close(); // closes the stream and release the resources

		} catch (IOException e) {
			e.printStackTrace();
		}

		return result;
	}

	public static ArrayList<String> ReadRCTBSetl(String file_id) {

		ArrayList<String> result = new ArrayList<String>();

		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();

			String sqlString = new StringBuilder().append("SELECT job_no FROM rctb_setl ")
					.append("WHERE file_id = '" + file_id.toUpperCase() + "' ")
					// .append("WHERE job_no = '" + "727777081" + "' ")
					.toString();

			log.info("Value: {}", sqlString);

			ResultSet rs = stmt.executeQuery(sqlString);

			while (rs.next()) {
				result.add(rs.getString(1));
			}
			;

			con.close();

		} catch (SQLException e) {
			e.printStackTrace();
		}

		return result;
	}

	public static void GetData() throws IOException {

		String filename = "";
		String dash_file = "";

		try {

			// Load files
			ArrayList<String> filelist = GetFiles();

			for (int i = 0; i < filelist.size(); i++) {
				filename = filelist.get(i);
				dash_file = filename;

				ArrayList<String> rctbContent = new ArrayList<String>();
				rctbContent = ReadRCTBSetl(dash_file);

				// PrintToFile(rctbContent);

				ArrayList<String> dashreconContent = new ArrayList<String>();
				dashreconContent = ReadDashRecon(filename);

				// PrintToFile(cdgoutContent);

				Compare(rctbContent, dashreconContent, dash_file);

			}

		} catch (Exception e) {
			log.error("Error occurred", e);
		}

	}

	public static void PrintToFile(ArrayList<String> input) throws IOException {

		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");

		for (int i = 0; i < input.size(); i++) {

			myWriter.write(input.get(i) + "\r\n");

		}

		myWriter.close();

	}

	public static void Compare(ArrayList<String> rctb, ArrayList<String> dashrecon, String dash_file) throws IOException {


		FileWriter myWriter = new FileWriter(Constants.FolderPath + "/DASH/RESULT/" + dash_file);

		String job = "";
		String dash_rec = "";
 

		for (int i = 0; i < dashrecon.size(); i++) {
			boolean exist = false;
			dash_rec = dashrecon.get(i).toString();
			job = dash_rec.substring(95, 105).replaceFirst(" ", "");
			

			exist = rctb.contains(job);

			if (exist == false) {

				
				  if (dash_rec.substring(0,3).equals("DTL") || dash_rec.substring(0,3).equals("HDR") || dash_rec.substring(0,3).equals("TRL")) 
				  {
					  myWriter.write(dash_rec + "\r\n"); 
				  }
				 

			}

		}

		myWriter.close();

	}

}
