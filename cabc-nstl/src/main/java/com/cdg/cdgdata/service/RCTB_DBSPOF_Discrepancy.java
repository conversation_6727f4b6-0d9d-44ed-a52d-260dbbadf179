package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RCTB_DBSPOF_Discrepancy {

	public static ArrayList<String> GetFiles() {
		ArrayList<String> list = new ArrayList<String>();

		File folder = new File(Constants.FolderPath + "/DBSPOF/");
		File[] listOfFiles = folder.listFiles();

		for (File file : listOfFiles) {
			if (file.isFile()) {
				list.add(file.getName());
			}
		}

		return list;
	}

	public static ArrayList<String> ReadDBSPOFRecon(String filename) {

		ArrayList<String> result = new ArrayList<String>();

		try {
			File file = new File(Constants.FolderPath + "/DBSPOF/" + filename); // creates a new file instance
			FileReader fr = new FileReader(file); // reads the file
			BufferedReader br = new BufferedReader(fr); // creates a buffering character input stream

			String line;
			while ((line = br.readLine()) != null) {
				result.add(line);
			}
			fr.close(); // closes the stream and release the resources

		} catch (IOException e) {
			e.printStackTrace();
		}

		return result;
	}

	public static ArrayList<String> ReadRCTBSetl(String file_id) {

		ArrayList<String> result = new ArrayList<String>();

		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();

			String sqlString = new StringBuilder().append("SELECT job_no FROM rctb_setl ")
					.append("WHERE file_id = '" + file_id + "' ")
					.toString();

			log.info("Value: {}", sqlString);

			ResultSet rs = stmt.executeQuery(sqlString);

			while (rs.next()) {
				result.add(rs.getString(1));
			}
			;

			con.close();

		} catch (SQLException e) {
			e.printStackTrace();
		}

		return result;
	}

	public static void GetData() throws IOException {

		String filename = "";
		String dbspof_file = "";

		try {

			// Load files
			ArrayList<String> filelist = GetFiles();

			for (int i = 0; i < filelist.size(); i++) {
				filename = filelist.get(i);
				dbspof_file = filename;

				ArrayList<String> rctbContent = new ArrayList<String>();
				rctbContent = ReadRCTBSetl(dbspof_file);

				// PrintToFile(rctbContent);

				ArrayList<String> dbspofreconContent = new ArrayList<String>();
				dbspofreconContent = ReadDBSPOFRecon(filename);

				// PrintToFile(cdgoutContent);

				Compare(rctbContent, dbspofreconContent, dbspof_file);

			}

		} catch (Exception e) {
			log.error("Error occurred", e);
		}

	}

	public static void PrintToFile(ArrayList<String> input) throws IOException {

		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");

		for (int i = 0; i < input.size(); i++) {

			myWriter.write(input.get(i) + "\r\n");

		}

		myWriter.close();

	}

	public static void Compare(ArrayList<String> rctb, ArrayList<String> dbspofrecon, String dbspof_file) throws IOException {


		FileWriter myWriter = new FileWriter(Constants.FolderPath + "/DBSPOF/RESULT/" + dbspof_file);

		String job = "";
		String dbspof_rec = "";
 

		for (int i = 0; i < dbspofrecon.size(); i++) {
			boolean exist = false;
			dbspof_rec = dbspofrecon.get(i).toString();
			job = ParseJob(dbspof_rec);
			

			exist = rctb.contains(job);

			if (exist == false) {

				
				  if (ParseP2b(dbspof_rec).equals("P2B") || ParseP2b(dbspof_rec).equals("Tranx Type")) 
				  {
					  myWriter.write(dbspof_rec + "\r\n"); 
				  }
				 

			}

		}

		myWriter.close();

	}
	
	
	public static String ParseJob(String record) {
		String result = "";
		String[] arrSplit = record.split(",");
		
		result = arrSplit[14].substring(1,11);
		return result;
		
	}
	
	public static String ParseP2b(String record) {
		String result = "";
		String[] arrSplit = record.split(",");
		
		result = arrSplit[13].replace("'", "");
		return result;
		
	}
	

}
