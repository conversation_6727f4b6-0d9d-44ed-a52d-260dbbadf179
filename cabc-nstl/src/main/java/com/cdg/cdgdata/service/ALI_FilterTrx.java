package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ALI_FilterTrx {
	
	public static void GetData() throws IOException {
		
		ArrayList<String[]> list = Helper.ReadDataSource("ALIPAY_JOBS.txt");
			
		File file=new File(Constants.FolderPath + "ALI_SOURCE22.txt");    //creates a new file instance  


		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath  + filename + ".csv");
		
		try{
			  

				
			for (int counter = 0; counter < list.size(); counter++) {
 	 
				FileReader fr=new FileReader(file);   //reads the file  
				BufferedReader br=new BufferedReader(fr);  //creates a buffering character input stream  
				
				String line;  
				while((line=br.readLine())!=null)  
				{  
					if(line.contains(list.get(counter)[0].toString())) {
					myWriter.write(line + "\r\n");
					break;
					}
				}  
				
				br.close();
				fr.close();
				
				
			 }
			
			myWriter.close();
 
			
		}catch(Exception e){ log.error("Error occurred", e);}
			 
 
	}
 
}
