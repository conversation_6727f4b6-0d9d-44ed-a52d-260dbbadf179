package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class  TMTB_GetPaylah {


	public static void GetData() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		
			try{		  	  
				  Connection con= Helper.GetConnection("ptccpro_tm");    
				  Statement stmt=con.createStatement();  
				  
				 //Load data
				 ArrayList<String[]> list = Helper.ReadDataSource("TMTB_DCP_PAYLAH.txt");
				 int total = list.size();
				 
				 int chunk = 1000;
				 int cnt = 0;
				 String dcp_request_id = "";
				 for (int counter = 0; counter < list.size(); counter++) { 
					 
					 cnt++;
					 dcp_request_id = "'" + list.get(counter)[0] + "'," + dcp_request_id;
					 if(chunk==cnt || counter+1 == total)
					 {
						 
						 dcp_request_id = dcp_request_id.substring(0,dcp_request_id.length()-1);
						 
				    	  String sqlString = new StringBuilder()
				    	            //.append("select booking_num, msg_type from tmtb_pl_txn_log  ")
				    	          .append("select * from tmtb_pl_txn_log ")  
				    			  .append("WHERE booking_num in (" + dcp_request_id + ") ")
				    			  //.append("and msg_type in ('pl_capture' )")
				    			  //.append("and msg_type in ('pl_preauth_off', 'pl_preauth_on' )")
				    	            .toString();
						 
						 log.info("Value: {}", sqlString);
						 
				    	  ResultSet rs=stmt.executeQuery(sqlString);
						  ResultSetMetaData rsmd = rs.getMetaData();

						  int columnsNumber = rsmd.getColumnCount();
					
						  String columns = "";
						  String records = "";

					
						  while(rs.next())
						  {     
							  if(columns=="")
							  {
								  for (int i = 1; i < columnsNumber+1; i++) {
									  columns+=rsmd.getColumnName(i) + "\t";    
									};
									myWriter.write(columns + "\r\n");
							  }
							  

							  for (int i = 1; i < columnsNumber+ 1; i++) {

								   records+=rs.getString(i) + "\t";
							  };
							  myWriter.write(records + "\r\n");
							  records="";
						  };
						 
						 
						  dcp_request_id = "";
						 cnt=0;
					 }
					 

					 
				 }
				  

				  myWriter.close();
				  con.close();
				  
				  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	
	
}
