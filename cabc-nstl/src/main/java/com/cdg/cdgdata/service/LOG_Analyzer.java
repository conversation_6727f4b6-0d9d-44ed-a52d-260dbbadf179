package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LOG_Analyzer {
	
	static ArrayList<String> filters = new ArrayList<String>();
	static ArrayList<String> cryptLog = new ArrayList<String>();
	static ArrayList<String> restartLog = new ArrayList<String>();
	static ArrayList<String> shutdownLog = new ArrayList<String>();
	
	public static void GetData() throws IOException {
		
		GetFilters();
		
		String filename = "";

		try {

			// Load files
			ArrayList<String> filelist = GetFiles();
			ArrayList<String> fileContent = new ArrayList<String>();


			for (int i = 0; i < filelist.size(); i++) {
				filename = filelist.get(i);

				File file = new File(Constants.FolderPath + "/LOG_ANALYZER/" + filename); // creates a new file instance
				FileReader fr = new FileReader(file); // reads the file
				BufferedReader br = new BufferedReader(fr); // creates a buffering character input stream

				String line;
				while ((line = br.readLine()) != null) {
					fileContent.add(line);
				}
				fr.close(); // closes the stream and release the resource

			}
			
			String resultFile = Helper.GenerateFileName();
			FileWriter myWriter = new FileWriter(Constants.FolderPath + "/LOG_ANALYZER/RESULT/" + resultFile + ".csv");

			String file_rec = "";
			String filter = "";
			String datetime = "";
			String errorlog = "";

			myWriter.write("DATETIME,ERROR LOG" + "\r\n"); 
			
			for (int j=0; j < filters.size(); j++) {
				
				filter = filters.get(j).toString();
				
				for (int i = 0; i < fileContent.size(); i++) {
					
					file_rec = fileContent.get(i).toString();
					
					if(j==0) {
						if(fileContent.get(i).toString().contains(filter)) {
							datetime = file_rec.substring(0,20);
							errorlog = file_rec.substring(20,file_rec.length());
							myWriter.write(datetime + "," + errorlog + "\r\n"); 
							cryptLog.add(datetime);
						}
					}
					
					if(j==1) {
						if(fileContent.get(i).toString().contains(filter)) {
							datetime = file_rec.substring(0,20);
							errorlog = file_rec.substring(20,file_rec.length());
							myWriter.write(datetime + "," + errorlog + "\r\n"); 
							restartLog.add(datetime);
						}
					}
					
					if(j==2) {
						if(fileContent.get(i).toString().contains(filter)) {
							datetime = file_rec.substring(0,20);
							errorlog = file_rec.substring(20,file_rec.length());
							myWriter.write(datetime + "," + errorlog + "\r\n"); 
							shutdownLog.add(datetime);
						}
					}

				}
			}
			

			myWriter.close();

		} catch (Exception e) {
			log.error("Error occurred", e);
		}
		
		SortFilters();
	}

	
	
	public static ArrayList<String> GetFiles() {
		ArrayList<String> list = new ArrayList<String>();

		File folder = new File(Constants.FolderPath + "/LOG_ANALYZER/");
		File[] listOfFiles = folder.listFiles();

		for (File file : listOfFiles) {
			if (file.isFile()) {
				list.add(file.getName());
			}
		}

		return list;
	}

	
	public static void GetFilters() throws IOException {
 
		File file = new File(Constants.FolderPath + "LOG_ANALYZER/Control/ControlFilters.txt"); // creates a new file instance
		FileReader fr = new FileReader(file); // reads the file
		BufferedReader br = new BufferedReader(fr); // creates a buffering character input stream

		String line;
		while ((line = br.readLine()) != null) {
			filters.add(line);
		}
		fr.close(); // closes the stream and release the resource
		
	}
	
	public static void SortFilters() throws IOException {
		
		Collections.sort(cryptLog, Collections.reverseOrder());
		//log.info("After Sorting: "+ cryptLog);
		System.out.println("Last log crypt error: " + cryptLog.get(0));
		String cryptLogLast = cryptLog.get(0);
		
		Collections.sort(restartLog, Collections.reverseOrder());
		//log.info("After Sorting: "+ restartLog);
		System.out.println("Last log restart: " + restartLog.get(0));
		String restartLogLast = restartLog.get(0);
		
		if(restartLogLast.compareTo(cryptLogLast) > 0) {
			log.info("Restarted already");
		}
		else {
			log.info("Must restart");
		}
		
	}

}
