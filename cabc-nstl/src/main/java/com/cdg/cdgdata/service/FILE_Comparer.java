package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Scanner;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FILE_Comparer {
	

	public static void GetData() throws IOException {
		
		//String data = null;
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		
        String first = "", second = "";
        
        
		try{
			 
		    File path = new File(Constants.FolderPath + "\\comparer");
		    BufferedReader [] input = new BufferedReader [2];
		    
		    File [] files = path.listFiles();
		    for (int i = 0; i < files.length; i++){
		        if (files[i].isFile()){ //this line weeds out other directories/folders
 
		        	input[i] = Files.newBufferedReader(files[i].toPath());
		            log.info("Value: {}", i);
		        }
		        	        

		    }
		
		
		    long lineNumber = 1;
	        String line1 = "", line2 = "";
	        while ((line1 = input[0].readLine()) != null) {
	            line2 = input[1].readLine();
	            if (line2 == null || !line1.equals(line2)) {
	            	log.info("Line number: {}", lineNumber);
	            }
	            lineNumber++;
	        }
	        if (input[1].readLine() == null) {
	        	log.info("Line number: {}", lineNumber);
	        }
	        else {
	        	log.info("Line number: {}", lineNumber);
	        }
		    
	        myWriter.close();
	
		
	}catch(Exception e){ log.error("Error occurred", e);}

}

}
