package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;


@Slf4j
public class FILE_Alipay_Copy {
	

	public static void GetData() throws IOException {
		
		String lastfile = "";
		String filename = "";
		String filecontroller = "FILE_ALIPAY_COPY.txt";
		
	    LocalDate curDate = LocalDate.now();
	    
		int year;
		int month;
		int day;
		
		
		
		try{
			
			File file=new File(Constants.FolderPath + filecontroller);      
			FileReader fr=new FileReader(file);   //reads the file  
			BufferedReader br=new BufferedReader(fr);  //creates a buffering character input stream  
	 
			String line;  
			while((line=br.readLine())!=null)  
			{  
				lastfile = line;
			}  
			fr.close();    //closes the stream and release the resources  

			year = Integer.parseInt(lastfile.substring(12,16));
		    month = Integer.parseInt(lastfile.substring(16,18));
		    day = Integer.parseInt(lastfile.substring(18,20));
		    
		    LocalDate lastfileday = LocalDate.of(year, month, day);
		    Period period = Period.between(curDate, lastfileday);
		    int diff = Math.abs(period.getDays());
		    
		    
		    for (int counter = 1; counter <= diff-1; counter++) { 
			    LocalDate newDate = LocalDate.of(year, month, day).plusDays(counter);  
			    LocalDate newDateplus1 = LocalDate.of(year, month, day).plusDays(counter+1);  
			    
			    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
			    String fileday = newDate.format(formatter);
			    String pickupday = newDateplus1.format(formatter);
	            

			    int timeCounter = 20000;
			    filename = "ALIAC_RECON_" + fileday + ".TXT__" + pickupday + "_ " + Integer.toString(timeCounter) + ".bak";
			    Path source = Paths.get("V:\\loader\\production\\alipay\\data_bak\\" + filename);
			    
			    while(!Files.exists(source) && !(timeCounter > 20100)) {
			    	timeCounter++;
			    	filename = "ALIAC_RECON_" + fileday + ".TXT__" + pickupday + "_ " + Integer.toString(timeCounter) + ".bak";
			    	source = Paths.get("V:\\loader\\production\\alipay\\data_bak\\" + filename);
			    }
			    
		        //*****Path destination = Paths.get("C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\CDGData-eclipse\\data\\ALIPAY\\" + filename);
			    Path destination = Paths.get("C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\NPX report for MinShi\\ALIPAY\\" + filename);
			    
			    if(Files.exists(source) && !Files.exists(destination))
			    {
			        Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING);
				     
				    FileWriter myWriter = new FileWriter(Constants.FolderPath + filecontroller);
				    myWriter.write(filename);
				    myWriter.close();
				    
				    log.info("Value: {}", filename);
			    }

		    }
		    

		
		}catch(Exception e){ log.error("Error occurred", e);}

}

}
