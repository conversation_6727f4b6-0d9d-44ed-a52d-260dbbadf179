package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.IOException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class KRIS_Signature {
	

	public static void GetData() throws IOException {
		
 
		try{
 
			String apikey = "zr7sgd964j279q83f6tda5hy"; 
			String secret = "bkFzzsYDBj"; 
			Long timestamp = System.currentTimeMillis()/1000;///1000; // Eg: 1200603038 
			String text = apikey + secret + timestamp;
			String encoded = org.apache.commons.codec.digest.DigestUtils.sha256Hex(text);
			//log.info("Value: {}", timestamp);
			//log.info(timestamp/1000);
			log.info("Value: {}", encoded);
			
	}catch(Exception e){ log.error("Error occurred", e);}

}

}
