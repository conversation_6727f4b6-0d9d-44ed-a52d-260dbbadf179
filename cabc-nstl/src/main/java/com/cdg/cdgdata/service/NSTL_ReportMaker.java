package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


@Slf4j
public class NSTL_ReportMaker {

	public static ArrayList<Object[]> nstlList = new ArrayList<Object[]>();
	public static  ArrayList<String> nstlcolumnList = new ArrayList<String>();
	public static  ArrayList<Integer> nstlcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> invList = new ArrayList<Object[]>();
	public static ArrayList<String> invcolumnList = new ArrayList<String>();
	public static ArrayList<Integer> invcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> esctripList = new ArrayList<Object[]>();
	public static ArrayList<String> esctripcolumnList = new ArrayList<String>();
	public static ArrayList<Integer> esctripcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> rctbtripList = new ArrayList<Object[]>();
	public static ArrayList<String> rctbtripcolumnList = new ArrayList<String>();
	public static ArrayList<Integer> rctbtripcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> missingList = new ArrayList<Object[]>();
	public static ArrayList<String> missingcolumnList = new ArrayList<String>();
	public static ArrayList<Integer> missingcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> invlessmissList = new ArrayList<Object[]>();
	public static ArrayList<String> invlessmisscolumnList = new ArrayList<String>();
	public static ArrayList<Integer> invlessmisscolumnTypeList = new ArrayList<Integer>();
	
	
	public static ArrayList<Object[]> missEntityList = new ArrayList<Object[]>();
	public static ArrayList<String> missEntitycolumnList = new ArrayList<String>();
	public static ArrayList<Integer> missEntitycolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> invTripList = new ArrayList<Object[]>();
	public static ArrayList<String> invTripcolumnList = new ArrayList<String>();
	public static ArrayList<Integer> invTripcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<Object[]> paidList = new ArrayList<Object[]>();
	public static ArrayList<String> paidcolumnList = new ArrayList<String>();
	public static ArrayList<Integer> paidcolumnTypeList = new ArrayList<Integer>();
	
	public static ArrayList<String> escjobs = new ArrayList<String>();
	
	public static Workbook wbfinal = new XSSFWorkbook();
	
	public static void GetData(String TARGET_DAY) throws IOException {
		GetNstl(TARGET_DAY);
		String nstljobs = NstlJobList();
		GetInvalidTrips(nstljobs);
		GetEscTrips(nstljobs);
		GetMissingTrips();
		GetInvLessMiss();
		GetMissEntity();
		GetInvTripIntf();
		GetPaidJobs();
		
		WriteWBSheets(wbfinal, "NSTL", nstlcolumnList, nstlcolumnTypeList, nstlList);
		WriteWBSheets(wbfinal, "Invalid", invcolumnList, invcolumnTypeList, invList);
		WriteWBSheets(wbfinal, "Esc trip", esctripcolumnList, esctripcolumnTypeList, esctripList);
		WriteWBSheets(wbfinal, "Missing trips", missingcolumnList, missingcolumnTypeList, missingList);
		WriteWBSheets(wbfinal, "InvLessMiss", invlessmisscolumnList, invlessmisscolumnTypeList, invlessmissList);
		WriteWBSheets(wbfinal, "MissEntity", missEntitycolumnList,missEntitycolumnTypeList,missEntityList);
		WriteWBSheets(wbfinal, "InvTripDet", invTripcolumnList,invTripcolumnTypeList,invTripList);
		WriteWBSheets(wbfinal, "PaidJobs", paidcolumnList,paidcolumnTypeList,paidList);
		
		String filename = GenerateNstlFileName() ; 
		try (OutputStream fileOut = new
		FileOutputStream(Constants.FolderPath + filename)) { wbfinal.write(fileOut);
		wbfinal.close(); }

	}
	
	public static void GetNstl(String TARGET_DAY) throws IOException {
		
		String sqlString = new StringBuilder()
				.append(" select ''''||job_no||''',', job_no, recon_date, payment_status, payment_mode from recsys.rctb_trip_intf  ")
				.append(" where recon_date > TO_DATE('" + TARGET_DAY + " 00:00:00','DD/MM/YYYY HH24:MI:SS') ")
				.append(" and recon_date < TO_DATE('" + TARGET_DAY + " 23:59:59','DD/MM/YYYY HH24:MI:SS') ")
				.append(" and error_code='NSTL' and payment_mode not in ('CASH') ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  nstlcolumnList.add(rsmd.getColumnName(i+1).toString());
						  nstlcolumnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  nstlList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		//String filename = GenerateNstlFileName() ;
		
		//WriteExcel(nstlcolumnList, nstlcolumnTypeList, nstlList, filename);
		
	}
	
	public static void GetPaidJobs() throws IOException {
		
		 log.info("Get paid jobs...");
		
		ArrayList<String> nstls = new ArrayList<String>();
		ArrayList<String> missings = new ArrayList<String>();
		ArrayList<String> invalids = new ArrayList<String>();
		
		 for (int counter = 0; counter < nstlList.size(); counter++) { 	
			 nstls.add(nstlList.get(counter)[1].toString());
		}
		 
		 for (int counter = 0; counter < missEntityList.size(); counter++) { 	
			 missings.add(missEntityList.get(counter)[1].toString());
		}
		
		 for (int counter = 0; counter < invTripList.size(); counter++) { 	
			 invalids.add(invTripList.get(counter)[17].toString());
		}
		
		 for (int counter = 0; counter < nstls.size(); counter++) { 	
			 if(missings.contains(nstls.get(counter)) || invalids.contains(nstls.get(counter))) {
				 //do nothing
			 }
			 else {
				 Object[] recList = new Object[1];
				 recList[0] = nstls.get(counter);
				 paidList.add(recList);
			 }
		}
		  paidcolumnList.add("Paid");
		  paidcolumnTypeList.add(94);
		 
		 

	}
	
	public static void GetInvTripIntf() throws IOException {
		
		String jobs = "";
		String invjobs = "";
		
		 for (int counter = 0; counter < invList.size(); counter++) { 	
			 jobs = invList.get(counter)[0] + jobs;
		}
				 
		jobs = jobs.substring(0,jobs.length()-1);
			
		String sqlString = new StringBuilder()
				.append(" Select job_no from esc_trip_details where valid_trip = 'N'   ")
				.append(" and job_no in ( " + jobs + ") ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptccpro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);
	    	ResultSet rs=stmt.executeQuery(sqlString);   	  

			  while(rs.next())
			  {     
				  invjobs = "'" + rs.getString(1) + "'," + invjobs;
				  
			  };

			con.close();
			invjobs = invjobs.substring(0,invjobs.length()-1);
			
			

		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		
		//GET TRIP DETAILS OF INVALID TriPS
		sqlString = new StringBuilder()
				.append(" Select DRIVER_IC,  PAX_NAME, VEHICLE_NO, ENTITY, FLEET_PARTNER,  TOTAL_AMOUNT, DRIVER_LEVY,  LEVY_WAIVE_AMOUNT,  LEVY_WAIVE_FLAG REFUND, DISCOUNT_AMOUNT,  VOUCHER_AMOUNT, MERIT_POINT_FEE,  PROMO_AMOUNT, PROMO_CODE, PROMO_TXN_CODE, PARTNER_DISCOUNT_AMT, CABREWARDS_AMOUNT,  JOB_NO, JOB_TYPE, JOB_STATUS, SERVICE_TYPE, PAYMENT_MODE, PRODUCT COMPLETED_DT, TRIP_END, OFFLINE_FLAG, STATUS, ERROR_CODE   ")
				.append(" FROM  recsys.rctb_trip_intf  ")
				.append(" WHERE job_no in ( " + invjobs + ") ")
				.append(" AND PAYMENT_MODE NOT in ('CASH')  ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();
			  int columnsNumber = rsmd.getColumnCount();
			  String columns = "";
				
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  invTripcolumnList.add(rsmd.getColumnName(i+1).toString());
						  invTripcolumnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  invTripList.add(recList);
				  
			  };

			con.close();

		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		
	}
	
	public static void GetMissEntity() throws IOException {
		
		String jobs = "";
		
		 for (int counter = 0; counter < missingList.size(); counter++) { 	
			 jobs = missingList.get(counter)[0] + jobs;
		}
				 
		jobs = jobs.substring(0,jobs.length()-1);
		
		String sqlString = new StringBuilder()
				.append(" Select entity, job_no from recsys.rctb_trip_intf where payment_mode not in ('CASH')   ")
				.append(" and job_no in ( " + jobs + ") ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  missEntitycolumnList.add(rsmd.getColumnName(i+1).toString());
						  missEntitycolumnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  missEntityList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
 
		
	}
	
	public static void GetInvLessMiss() throws IOException {
		
		ArrayList<String> tempinvList = new ArrayList<String>();
		ArrayList<String> tempmissList = new ArrayList<String>();
		
		for (int i = 0; i < invList.size(); i++) { 
			 tempinvList.add(invList.get(i)[1].toString());
		}
		
		for (int i = 0; i < missingList.size(); i++) { 
			tempmissList.add(missingList.get(i)[1].toString());
		}
		
		for (int i = 0; i < tempinvList.size(); i++) { 
			if(!tempmissList.contains(tempinvList.get(i))) {
				Object[] recList = new Object[1];
				recList[0] = tempinvList.get(i);
				invlessmissList.add(recList);
			};
		}
		
		invlessmisscolumnList.add("JOB");
		invlessmisscolumnTypeList.add(94);
		
	}
	
	public static void GetMissingTrips() throws IOException {
		
		for (int i = 0; i < esctripList.size(); i++) { 
			escjobs.add(esctripList.get(i)[0].toString());
		}
		
		for (int i = 0; i < nstlList.size(); i++) { 
			if(!escjobs.contains(nstlList.get(i)[1])) {
				Object[] recList = new Object[1];
				recList = nstlList.get(i);
				missingList.add(recList);
			};
		}
		
		missingcolumnList.add("JOB");
		missingcolumnList.add("JOB_NO");
		missingcolumnList.add("RECON_DATE");
		missingcolumnList.add("PAYMENT_STATUS");
		missingcolumnTypeList.add(94);
		missingcolumnTypeList.add(94);
		missingcolumnTypeList.add(94);
		missingcolumnTypeList.add(94);
	}
	
	public static void GetRctbTrips(String jobs) throws IOException {
		
		String sqlString = new StringBuilder()
				.append(" Select * from recsys.rctb_trip_intf where fms_status = 'C'   ")
				.append(" and job_no in ( " + jobs + ") ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  rctbtripcolumnList.add(rsmd.getColumnName(i+1).toString());
						  rctbtripcolumnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  rctbtripList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		String filename = GenerateRctbFileName() ;
		
		WriteExcel(rctbtripcolumnList, rctbtripcolumnTypeList, rctbtripList, filename);
		
	}
	
	public static void GetEscTrips(String jobs) throws IOException {
		
		String sqlString = new StringBuilder()
				.append(" Select job_no from esc_trip_details   ")
				.append(" where job_no in ( " + jobs + ") ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptccpro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  esctripcolumnList.add(rsmd.getColumnName(i+1).toString());
						  esctripcolumnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  esctripList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		//String filename = GenerateNstlFileName() ;
		
		//WriteExcel(nstlcolumnList, nstlcolumnTypeList, nstlList, filename);
		
	}
	
	
	
	public static void GetInvalidTrips(String jobs) throws IOException {
		
		String sqlString = new StringBuilder()
				.append(" select ''''||job_no||''',', job_no  from esc_job j where  ")
				.append(" (not exists (select * from esc_trip_details t where t.job_no=j.job_no and t.valid_trip='Y') ")
				.append(" or exists (select * from esc_trip_details t where t.job_no=j.job_no and t.valid_trip='N'))  " )
				.append(" and job_no in ( " + jobs + ") ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptccpro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  invcolumnList.add(rsmd.getColumnName(i+1).toString());
						  invcolumnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  invList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		//String filename = GenerateNstlFileName() ;
		
		//WriteExcel(nstlcolumnList, nstlcolumnTypeList, nstlList, filename);
		
	}
	
	
	public static String NstlJobList() throws IOException {
		String jobs = "";
		
		 for (int counter = 0; counter < nstlList.size(); counter++) { 	
			 jobs = nstlList.get(counter)[0] + jobs;
		}
				 
		jobs = jobs.substring(0,jobs.length()-1);
		
		return jobs;
	}
	
	
	public static String InvalidTripsJobList() throws IOException {
		String jobs = "";
		
		 for (int counter = 0; counter < invList.size(); counter++) { 	
			 jobs = invList.get(counter)[0] + jobs;
		}
				 
		jobs = jobs.substring(0,jobs.length()-1);
		
		return jobs;
	}
	
	


	
	public static Workbook WriteWBSheets(Workbook wb, String sheetname, ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList) throws IOException {
		Sheet sheet1 = wb.createSheet(sheetname);

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i+1);
	        headerCell.setCellValue(columnList.get(i));
	        headerCell.setCellStyle(style);
		
		};
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell headerCell = recordRow.createCell(j+1);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(2)) {
		        		headerCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		headerCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	headerCell.setCellValue("");
		        }

			};
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<columnList.size()+1;i++) {
			sheet1.autoSizeColumn(i);
		}
		
		return wb;
	}

	public static void WriteExcel(ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList, String filename) throws FileNotFoundException, IOException {
		Workbook wb = new XSSFWorkbook();
		Sheet sheet1 = wb.createSheet("Sheet1");

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i+1);
	        headerCell.setCellValue(columnList.get(i));
	        headerCell.setCellStyle(style);
		
		};
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell headerCell = recordRow.createCell(j+1);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(2)) {
		        		headerCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		headerCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	headerCell.setCellValue("");
		        }

			};
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<columnList.size()+1;i++) {
			sheet1.autoSizeColumn(i);
		}
		

		
		try (OutputStream fileOut = new FileOutputStream(Constants.FolderPath +  filename)) {
		    wb.write(fileOut);
		    wb.close();
		}
 	}
 
 
 public static String GenerateNstlFileName() {
	 
	    SimpleDateFormat formatter = new SimpleDateFormat("dd MMM");  
	    Date date = new Date();  

	    String filename =  "NSTL " + formatter.format(date) + ".xlsx";
	    return filename;
	
}

 public static String GenerateRctbFileName() {
	 
	    SimpleDateFormat formatter = new SimpleDateFormat("dd MMM");  
	    Date date = new Date();  

	    String filename =  "PROD " + formatter.format(date) + " NSTL for verification.xlsx";
	    return filename;
	
}
 
}


