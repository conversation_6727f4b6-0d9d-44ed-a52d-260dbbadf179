package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PYTB_GetCAPTURE_JOB {

	public static void GetData() throws IOException {

		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");

		try {
			Connection con = Helper.GetConnection("ptccpro_dcp");
			Statement stmt = con.createStatement();

			// Load data
			ArrayList<String[]> list = Helper.ReadDataSource("PYTB_GETCAPTURE_JOB.txt");
			int total = list.size();

			int chunk = 1;
			int cnt = 0;
			String dcp = "";
			for (int counter = 0; counter < list.size(); counter++) {

				cnt++;
				dcp = "'%" + list.get(counter)[0] + "%'," + dcp;
				if (chunk == cnt || counter + 1 == total) {

					dcp = dcp.substring(0, dcp.length() - 1);

					 
					  String sqlString = new StringBuilder()
					  .append("select * from dcp_sys.pytb_wd_txn td  ")
					  .append(" where payment_mode in ('COF') ")
					  //.append(" and txn_type in ('capture-authorization')  ")
					  .append(" and created_dt >= TO_DATE('29/5/2025 00:00:00','DD/MM/YYYY HH24:MI:SS')")
					  .append(" and td.provider_ref like (" + dcp + ") ").toString();
					

					System.out.println(String.valueOf(counter) + " " +  sqlString);

					ResultSet rs = stmt.executeQuery(sqlString);
					ResultSetMetaData rsmd = rs.getMetaData();

					int columnsNumber = rsmd.getColumnCount();

					String columns = "";
					String records = "";

					while (rs.next()) {
						if (columns == "") {
							for (int i = 1; i < columnsNumber + 1; i++) {
								columns += rsmd.getColumnName(i) + "\t";
							}
							;
							//myWriter.write(columns + "\r\n");
						}

						for (int i = 1; i < columnsNumber + 1; i++) {

							records += rs.getString(i) + "\t";
						}
						;
						myWriter.write(records + "\r\n");
						records = "";
					}
					;

					dcp = "";
					cnt = 0;
				}

			}

			myWriter.close();
		    con.close();

		} catch (Exception e) {
			log.error("Error occurred", e);
		}

	}

}
