package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.IOException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Program{  
	
public static void main(String args[]) throws Exception{  

	//ALI_FilterTrx.GetData(); ONEDRIVE
	//BasicSqlQuery.GetData();
	//Basic_Excel_Read.GetData();
	//Basic_Sql_To_Excel.GetData();
	//Dash_FilterTrx.GetData();
	//Dash_GetJOB_MIDAMT.GetData();
	
	//ESC_GetESC_TIDSTAN.GetData();
	//ESC_GetEscJob_DCP.GetData();
	//ESC_GetEscJob_JOB.GetData();
	//ESC_GetEscPay_JOB.GetData();
	//ESC_GetEscTrip_JOB.GetData();
	
	//ESC_GetFirst3Taxi.GetData();   //Use in TMS vehicle if still running
	//ESC_GetJOB_STANPP.GetData();
	//ESC_GetJOB_TIDSTAN.GetData();
	//ESC_GetNPX.GetData();
	//ESC_GetPaxCategory.GetData();
	//ESC_GetPay_TIDSTAN.GetData();
	//ESC_GetQRJob.GetData(); //For WECHAT getting esc_pay details
	
	//FILE_Combiner.GetData();  //For NPX report MC/Visa pivot
	//FILE_Comparer.GetData();
	//FILE_Lister.GetData("C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\CDGData-eclipse\\data\\FileLister\\");
	//FILE_Npx_Copy.GetData(); //ALWAYS ENABLE
	//FILE_ReconFMS_Copy.GetData(); //ALWAYS ENABLE
	//FILE_Copy_Basic.GetData();
	
	//KRIS_Signature.GetData();
	//NFP_SettleAckParser.GetData();
	//NPX_Archive.GetData();
	//NPX_FilterTrx.GetData();
	
	//PTDAPRO_Sql_To_Excel.GetData();
	
	//PYTB_BookingRef.GetData();
	//PYTB_CABC_BookingRef.GetData2();
	//PYTB_CABC_BookingRef.GetData();
	
	//PYTB_ESCJob_WDTxn.GetData();
	//PYTB_GetDCP_PSP.GetData();
	//PYTB_GetParentTxnBook=ngRef.GetData();
	//PYTB_GetPYTB_DCP.GetData();
	//PYTB_ParentTxnBookingRef.GetData();
	//RCTB_CABC_Discrepancy.GetData();
	//RCTB_CommissionPaySumm.GetData();
	
	//RCTB_DASH_Discrepancy.GetData();
	//RCTB_DBSPOF_Discrepancy.GetData();
	//RCTB_SETL_FILEID.GetData();
	//RCTB_SETL_JOB.GetData();
	//RCTB_Trip_JOB.GetData();
	//RECON_NPX_CreditFilter.GetData();
	
	//ReconFileGen.Alipay();
	//ReconFileGen.Dash();
	//TMTB_GetPaylah.GetData();
	//TMTB_IBS_Acquirer.GetData();
	//TMTB_TMSTxnLog.GetData();
	
	
	//SandBox.GetData();
	//SandBox.HexMe();
	
	//FAVORITES
	
	//ESC_GetJOB_TIDSTAN.GetData();
	
	//ESC_GetPay_TIDSTAN.GetData();
	
	//RCTB_TripExcel_JOB.GetData();
	//RCTB_SetlExcel_JOB.GetData();

	//ESC_GetEscTrip_JOB.GetData();
	
	//RCTB_CommissionPaySumm.GetData();

	//CPTB_POLICY.GetData();
	
	//TMTB_NON_BILL.GetData();
	
	//PYTB_CABC_BookingRef.GetData();
	
	FILE_ReconFMS_Copy.GetData(); //ALWAYS ENABLE
	//FILE_Npx_Copy.GetData(); //ALWAYS ENABLE
	//FILE_Alipay_Copy.GetData(); //ALWAYS ENABLE

	
	//ESC_TMS_TaxiActive.GetData();   //CHECK NEW TAXI ON THE ROAD NOT IN GROUP 6
	//SHD3314J-ezlink issue, <SHC3107X;SHD4695C(starhub) recall>
	
	//NSTL_ReportMaker.GetData("19/3/2023");

	NPX_Discrepancy_Report.GetData("7/6/2025");    
	//ESC_TMS_TaxiActive.GetData();   //CHECK NEW TAXI ON THE ROAD NOT IN GROUP 6
	//SHD3314J-ezlink issue, <SHC3107X;SHD4695C recall>
	
	
	//PAYL_TripCapture.GetData("16/4/2024"); //completed_dt
	//COF_TripCapture.GetData("16/4/2024");
	
	CABC_Error51.GetData("9/6/2025");
	//CABC_Error51.GetNoCard();
	//CABC_Error51.GetPANIso();
	//RCTB_TripAll_JOB.GetData();   //get CABC card no
	//PYTB_GetMaskedPAN_DCP.GetData();
	
	//PYTB_GetCAPTURE_DCP.GetData();
	//PYTB_GetCAPTURE_JOB.GetData();
	
	//PYTB_GetDCP_PSP.GetData();
	//PYTB_GetNotif_PSP.GetData();
	
	
	//RCTB_TripExcel_JOB.GetData();
	//RCTB_SetlExcel_JOB.GetData();
	//TMTB_PLCap_JOB.GetData();
	//RCTB_Trip_JOB.GetData();

	
	//PYTB_GETNOTIF_JOB.GetData();
	
	
	//ESC_GetEscTrip_JOB.GetData();
	
	//PYTB_COFAuthNoCapCan.GetData();
	//NETSNPX_20250222.CSV
	//NETSNPX_20250223.CSV

	//For NPX discrepancy to check with DBS dump
	String[] npxReports = {  "NETSNPX_20250527.CSV"};
	//NPX_Discrepancy.GetData(npxReports);
	//NPX_DISCREP_DBS_TID.GetData();
	
	//For regular NPX in rctb_setl check with DBS Dump
	//RCTB_NPX_DBS_TID.GetData();
	//FILE_CombinerNPXDBS.GetData();
	
	
	//TMTB_NON_BILL.GetData();
	//TMTB_NON_BILL_TXNNO.GetData();
	
	//NPX_SETL_STG.GetData();
	//NPX_STG_TIDSTAN.GetData();

	//LOG_Analyzer.GetData();
	
	//Basic_Excel_Read.GetData();
	//Basic_Excel_To_Excel.GetData();
	//DBS_ComfortBatch.GetData();
	
	//RCTB_CABC_Discrepancy.GetData();
	

	//SandBox.ExistInList();
	//SandBox.OracleDB();

	//COF_JobNoCapture.GetData();
	
	//RCTB_TripExcel_BookingRef.GetData();
	//TMTB_NON_BILL.GetData();
	
	//TMTB_TripExcel_PLTXN.GetData();
	
 
	String folder = "C:\\bitbucket\\ZPPT-1135 branch";
	//KEYWORD_FileSearch.GetData(folder);
	
	
	//PGP_Util.GetData("{help}");
	
	//Helper.Splitter("job_no", "'");
	
	log.info("Done.! main program");
	
	

	
}  


}  


