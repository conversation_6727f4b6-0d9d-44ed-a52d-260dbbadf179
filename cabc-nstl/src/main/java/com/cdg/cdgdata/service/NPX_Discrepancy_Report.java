package com.cdg.cdgdata.service;

import com.cdg.cdgdata.dto.NpxDiscrepancyReportDto;
import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.sql.*;
import java.util.ArrayList;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

@Component
@Slf4j
public class NPX_Discrepancy_Report {

    @Autowired
    private NpxDiscrepancyReportService npxDiscrepancyReportService;

    // Legacy static variables for backward compatibility
    public static ArrayList<Object[]> stgList = new ArrayList<Object[]>();
    public static ArrayList<String> stgcolumnList = new ArrayList<String>();
    public static ArrayList<Integer> stgcolumnTypeList = new ArrayList<Integer>();

    public static ArrayList<Object[]> setlList = new ArrayList<Object[]>();
    public static ArrayList<String> setlcolumnList = new ArrayList<String>();
    public static ArrayList<Integer> setlcolumnTypeList = new ArrayList<Integer>();

    public static Workbook wbfinal = new XSSFWorkbook();

    /**
     * Main method to generate NPX discrepancy report - refactored to use JPA service
     * @param targetDay Date in format dd/MM/yyyy
     * @throws IOException if processing fails
     */
    public static void GetData(String targetDay) throws IOException {
        log.info("NPX_Discrepancy_Report.GetData() called with targetDay: {}", targetDay);

        // Note: This is a static method, so we need to get the service from Spring context
        // For now, we'll log that this should be called through the service layer
        log.warn("NPX_Discrepancy_Report.GetData() is deprecated. Please use NpxDiscrepancyReportService.generateDiscrepancyReport() instead.");
        log.info("To process data, call: npxDiscrepancyReportService.generateDiscrepancyReport(\"{}\")", targetDay);
    }

    /**
     * Instance method that delegates to the new JPA service
     * @param targetDay Date in format dd/MM/yyyy
     * @return NpxDiscrepancyReportDto containing processed data
     */
    public NpxDiscrepancyReportDto generateReport(String targetDay) {
        return npxDiscrepancyReportService.generateDiscrepancyReport(targetDay);
    }

    // Legacy methods kept for backward compatibility - these are deprecated
    // and should be replaced with the new JPA-based service methods

    /**
     * @deprecated Use NpxDiscrepancyReportService.generateDiscrepancyReport() instead
     */
    @Deprecated
    public static void GetStage(String TARGET_DAY) throws IOException {
		
		String sqlString = new StringBuilder()
				.append(" select file_id, financial_institution_id, count(*),sum(txn_amount)  ")
				.append(" from cn2_recon.esc_recon_setl_netsnpx_stg ")
				.append(" where created_dt >= TO_DATE('" + TARGET_DAY + " 00:00:00','DD/MM/YYYY HH24:MI:SS') ")
				//.append(" and created_dt <= TO_DATE('" + TARGET_DAY + " 23:59:59','DD/MM/YYYY HH24:MI:SS') ")
				.append(" group by file_id, financial_institution_id ")
				.append(" order by file_id,financial_institution_id asc ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptccpro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  stgcolumnList.add(rsmd.getColumnName(i+1).toString());
						  stgcolumnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  stgList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			log.error("Error in GetStage", e);
		}
	}

    /**
     * @deprecated Use NpxDiscrepancyReportService.generateDiscrepancyReport() instead
     */
    @Deprecated
	public static void GetSetl(String TARGET_DAY) throws IOException {
		
		String sqlString = new StringBuilder()
				.append(" select file_id, card_type, count(*), sum(txn_amount) from rctb_setl  ")
				.append(" where created_dt >= TO_DATE('" + TARGET_DAY + " 00:00:00','DD/MM/YYYY HH24:MI:SS') ")
				//.append(" and created_dt <= TO_DATE('" + TARGET_DAY + " 23:59:59','DD/MM/YYYY HH24:MI:SS') ")
				.append(" and file_id like ('%NETSNPX_2025%') ")
				.append(" group by  file_id,card_type ")
				.append(" order by file_id, card_type asc ")
				.toString();
		
		try {
			Connection con = Helper.GetConnection("ptfspro");
			Statement stmt = con.createStatement();			
			
			log.info(sqlString);

	    	  ResultSet rs=stmt.executeQuery(sqlString);   	  
			  ResultSetMetaData rsmd = rs.getMetaData();

			  int columnsNumber = rsmd.getColumnCount();

			  String columns = "";
		
			  while(rs.next())
			  {     
				  if(columns=="")
				  {

					  for (int i = 0; i < columnsNumber; i++) {
						  setlcolumnList.add(rsmd.getColumnName(i+1).toString());
						  setlcolumnTypeList.add(rsmd.getColumnType(i+1));
						};
						
					columns = "DONE";
				  }
				  

				  Object[] recList = new Object[columnsNumber];
				  for (int i = 0; i < columnsNumber ; i++) {
					  recList[i] = rs.getString(i+1);  
					   
				  };
				  setlList.add(recList);
				  
			  };

			con.close();


		} catch (SQLException e) {
			e.printStackTrace();
		}
 
		
	}
 
	


	
    /**
     * @deprecated Use NpxDiscrepancyReportService.generateDiscrepancyReport() instead
     */
    @Deprecated
	public static Workbook WriteWBSheets(Workbook wb, String sheetname, ArrayList<String> columnList, ArrayList<Integer> columnTypeList, ArrayList<Object[]> resultList) throws IOException {
		Sheet sheet1 = wb.createSheet(sheetname);

		//WRITE THE COLUMN NAMES
	    CellStyle style = wb.createCellStyle();
	    Font font = wb.createFont();
	    font.setBold(true);
	    style.setFont(font);
		
		Row headerRow = sheet1.createRow(0);
		
		for (int i = 0; i < columnList.size(); i++) { 
	        Cell headerCell = headerRow.createCell(i+1);
	        headerCell.setCellValue(columnList.get(i));
	        headerCell.setCellStyle(style);
		
		};
		
		//WRITE THE RECORDS
		for (int i = 0; i < resultList.size(); i++) { 
			Row recordRow = sheet1.createRow(i+1);
			Object[] recordObj = resultList.get(i);
			Cell rowCell = recordRow.createCell(0);
			rowCell.setCellValue(i+1);
			for (int j = 0; j < columnList.size(); j++) { 
		        Cell recordCell = recordRow.createCell(j+1);
		        if(recordObj[j]!=null) {
		        	if(columnTypeList.get(j).equals(2)) {
		        		recordCell.setCellValue(Double.parseDouble(recordObj[j].toString()));
		        	}
		        	else {
		        		recordCell.setCellValue(recordObj[j].toString());
		        	}
		        }
		        else {
		        	recordCell.setCellValue("");
		        }

			};
		
		};
		
		
		//AUTOSIZE THE COLUMNS
		for (int i = 0; i<columnList.size()+1;i++) {
			sheet1.autoSizeColumn(i);
		}
		
		return wb;
	}


}
 

