package com.cdg.cdgdata.service;

import com.cdg.cdgdata.util.Constants;
import com.cdg.cdgdata.util.Helper;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ReconFileGen {
	
	 private static final DecimalFormat df = new DecimalFormat("0.00");
	 private static final DateFormat dateFormatLong = new SimpleDateFormat("yyyyMMddHHmmss"); 
	 private static final DateFormat dateFormatLong_v2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
	 private static final DateFormat dateFormatShort = new SimpleDateFormat("yyyyMMdd"); 

	public static void Dash() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		
			try{		  	  
				Connection con= Helper.GetConnection("ptccpro");    
				  Statement stmt=con.createStatement();  
				  
				 //Load data
				 ArrayList<String[]> list = Helper.ReadDataSource("RECON_JOBS.txt");

				  
			      for (int counter = 0; counter < list.size(); counter++) { 
			    	  
			    	  String sqlString = new StringBuilder()
			    	            .append("SELECT * FROM esc_payment_transaction P ")
			    	            .append("WHERE job_no = '" + list.get(counter)[0] + "' ")
			    	            .append("AND APPROVAL_CODE  is not null " )
			    	            .toString();
	 				  			    	  
			          log.info("Value: {}", sqlString);
			          		    	  
			    	  ResultSet rs=stmt.executeQuery(sqlString);
			    	  
					  while(rs.next())
					  {			  
						  String str = new String(new char[137]).replace('\0', ' ');;
						  StringBuilder sb = new StringBuilder(str);
						  
						  sb.insert(0, "DTL");

						  Date dt = dateFormatLong_v2.parse(rs.getString("TRANS_DT"));
						  
						  sb.insert(3, dateFormatLong.format(dt));
				
						  sb.insert(17, "DASH");
						  sb.insert(21, rs.getString("MID"));
						  sb.insert(33, "CDG_");
						  sb.insert(37, rs.getString("VEHICLE_ID") +"_");
						  sb.insert(46, rs.getString("JOB_NO"));
						  sb.insert(58, rs.getString("RET_REF_NO").substring(2));
						  sb.insert(68, String.format("%06d", Integer.parseInt(df.format(rs.getDouble("TRANS_AMOUNT")).replace(".",""))));
						  sb.insert(74, rs.getString("VEHICLE_ID"));
						  sb.insert(86, rs.getString("TID"));
						  sb.insert(95, rs.getString("JOB_NO"));
						  sb.insert(105, String.format("%06d", Integer.parseInt(df.format(rs.getDouble("TRANS_AMOUNT")).replace(".",""))));
						  sb.insert(111, "000000000000");
						  sb.insert(123, rs.getString("REMARKS"));
						  sb.insert(129, dateFormatShort.format(dt));
						  
						  myWriter.write(sb + "\r\n");

					  }
				
				    	  
			    	  
			      }   	
				

				  myWriter.close();
				  con.close();
				  
				  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}

	public static void Alipay() throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + filename + ".txt");
		
			try{		  	  
				Connection con= Helper.GetConnection("cn2prosby");    
				  Statement stmt=con.createStatement();  
				  
				 //Load data
				 ArrayList<String[]> list = Helper.ReadDataSource("RECON_JOBS.txt");

				  
			      for (int counter = 0; counter < list.size(); counter++) { 
			    	  
			    	  String sqlString = new StringBuilder()
			    	            .append("SELECT * FROM esc_payment_transaction P ")
			    	            .append("WHERE job_no = '" + list.get(counter)[0] + "' ")
			    	            .append("AND APPROVAL_CODE  is not null " )
			    	            .append("AND TRANS_TYPE  = '0210' " )
			    	            .toString();
	 				  			    	  
			          log.info("Value: {}", sqlString);
			          		    	  
			    	  ResultSet rs=stmt.executeQuery(sqlString);
			    	  
					  while(rs.next())
					  {			  
						  String str = new String(new char[250]).replace('\0', ' ');;
						  StringBuilder sb = new StringBuilder(str);
						  
						  
						  sb.insert(0, rs.getString("VEHICLE_ID"));
						  sb.insert(12, "XXXXX" + rs.getString("TID").substring(5));
						  sb.insert(21, rs.getString("JOB_NO"));
						  sb.insert(31, String.format("%06d", Integer.parseInt(df.format(rs.getDouble("FARE_AMOUNT")).replace(".",""))));
						  sb.insert(37, String.format("%06d", Integer.parseInt(df.format(rs.getDouble("FARE_GST")).replace(".",""))));
						  sb.insert(43, String.format("%06d", Integer.parseInt(df.format(rs.getDouble("FARE_ADMIN")).replace(".",""))));
						  
							
						  sb.insert(49, rs.getString("REMARKS")); 
						  sb.insert(53, "-");
						  sb.insert(54, rs.getString("SYSTEM_TRACE_AUDIT_NO")); sb.insert(60, "|");
						  
						  Date dt = dateFormatLong_v2.parse(rs.getString("TRANS_DT"));
						  
						  sb.insert(61, dateFormatShort.format(dt)); 
						  sb.insert(69, "194010800100188");
						  sb.insert(84, rs.getString("RET_REF_NO")); 
						  sb.insert(96, "|"); 
						  sb.insert(97,df.format(rs.getDouble("TRANS_AMOUNT"))
								  +  "|0.10|SGD|" 
								  + dateFormatLong_v2.format(dt) + "|PAYMENT||" + rs.getString("TID")
								  + " " 
								  + String.format("%06d", Integer.parseInt(df.format(rs.getDouble("FARE_AMOUNT")).replace(".",""))) 
								  + String.format("%06d",Integer.parseInt(df.format(rs.getDouble("FARE_GST")).replace(".",""))) 
								  + String.format("%06d", Integer.parseInt(df.format(rs.getDouble("FARE_ADMIN")).replace(".",""))) 
								  + rs.getString("REMARKS") 
								  + "|" 
								  + String.format("%-12s",rs.getString("VEHICLE_ID")) 
								  + rs.getString("JOB_NO")
								  + "||transactionQrCode|SGD|" 
								  + df.format(rs.getDouble("TRANS_AMOUNT")) 
								  +  "|1.0" 
								  
								  ); 

							 
						  myWriter.write(sb + "\r\n");

					  }
				
				    	  
			    	  
			      }   	
				

				  myWriter.close();
				  con.close();
				  
				  }catch(Exception e){ log.error("Error occurred", e);}
			 
	}
	




}
