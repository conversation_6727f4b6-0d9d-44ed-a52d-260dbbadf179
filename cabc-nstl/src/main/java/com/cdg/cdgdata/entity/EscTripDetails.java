package com.cdg.cdgdata.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "esc_trip_details")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EscTripDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "job_no")
    private String jobNo;

    @Column(name = "end_dt")
    private LocalDateTime endDt;

    @Column(name = "amount_paid")
    private BigDecimal amountPaid;

    @Column(name = "start_y")
    private BigDecimal startY;

    @Column(name = "start_x")
    private BigDecimal startX;

    @Column(name = "end_y")
    private BigDecimal endY;

    @Column(name = "end_x")
    private BigDecimal endX;

    @Column(name = "platform_fee")
    private BigDecimal platformFee;

    @Column(name = "valid_trip")
    private String validTrip;

    @Column(name = "distance")
    private BigDecimal distance;

    @Column(name = "created_dt")
    private LocalDateTime createdDt;

    @Column(name = "recon_sync_dt")
    private LocalDateTime reconSyncDt;

    @Column(name = "payment_method")
    private String paymentMethod;
}
