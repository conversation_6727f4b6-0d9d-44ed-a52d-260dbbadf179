package com.cdg.cdgdata.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "iso8583_incoming", schema = "pay_as_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Iso8583Incoming {

    @Id
    @Column(name = "msg_id")
    private Long msgId;

    @Column(name = "msg_typ_id")
    private String msgTypId;

    @Column(name = "pan")
    private String pan;

    @Column(name = "txn_amt")
    private String txnAmt;

    @Column(name = "txn_date")
    private LocalDateTime txnDate;

    @Column(name = "tripinformation")
    private String tripInformation;

    @Column(name = "expiry_date")
    private String expiryDate;

    @Column(name = "track_2_data")
    private String track2Data;

    // Computed field for the concatenated NEW value
    @Transient
    private String newValue;

    public String getNewValue() {
        if (txnDate != null && txnAmt != null && tripInformation != null && tripInformation.length() > 87) {
            StringBuilder sb = new StringBuilder();
            
            // Format date as YYYYMMDD
            sb.append(String.format("%04d%02d%02d", 
                txnDate.getYear(), 
                txnDate.getMonthValue(), 
                txnDate.getDayOfMonth()));
            
            // Add transaction amount
            sb.append(txnAmt);
            
            // Extract and format coordinates from tripinformation
            if (tripInformation.length() >= 85) {
                try {
                    String coord1 = tripInformation.substring(24, 39).trim();
                    String coord2 = tripInformation.substring(39, 54).trim();
                    String coord3 = tripInformation.substring(54, 69).trim();
                    String coord4 = tripInformation.substring(69, 84).trim();
                    
                    sb.append(String.format("%.4f", Double.parseDouble(coord1)));
                    sb.append(String.format("%.4f", Double.parseDouble(coord2)));
                    sb.append(String.format("%.4f", Double.parseDouble(coord3)));
                    sb.append(String.format("%.4f", Double.parseDouble(coord4)));
                } catch (Exception e) {
                    // If parsing fails, return empty string
                    return "";
                }
            }
            
            return sb.toString();
        }
        return "";
    }
}
