package com.cdg.cdgdata.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "rctb_trip_intf")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RctbTripIntf {

    @Id
    @Column(name = "trip_intf_pk")
    private Long tripIntfPk;

    @Column(name = "job_no")
    private String jobNo;

    @Column(name = "vehicle_no")
    private String vehicleNo;

    @Column(name = "driver_ic")
    private String driverIc;

    @Column(name = "entity")
    private String entity;

    @Column(name = "booked_channel")
    private String bookedChannel;

    @Column(name = "booked_date_time")
    private LocalDateTime bookedDateTime;

    @Column(name = "booked_vehicle_group")
    private String bookedVehicleGroup;

    @Column(name = "pax_name")
    private String paxName;

    @Column(name = "booking_reference")
    private String bookingReference;

    @Column(name = "trip_start")
    private LocalDateTime tripStart;

    @Column(name = "trip_end")
    private LocalDateTime tripEnd;

    @Column(name = "pickup")
    private String pickup;

    @Column(name = "destination")
    private String destination;

    @Column(name = "job_type")
    private String jobType;

    @Column(name = "job_status")
    private String jobStatus;

    @Column(name = "pax_privilege")
    private String paxPrivilege;

    @Column(name = "product_id")
    private String productId;

    @Column(name = "product")
    private String product;

    @Column(name = "payment_mode")
    private String paymentMode;

    @Column(name = "account_lv1")
    private String accountLv1;

    @Column(name = "account_lv2")
    private String accountLv2;

    @Column(name = "card_no")
    private String cardNo;

    @Column(name = "approval_code")
    private String approvalCode;

    @Column(name = "gst_inclusive")
    private String gstInclusive;

    @Column(name = "txn_amount")
    private BigDecimal txnAmount;

    @Column(name = "booking_fee")
    private BigDecimal bookingFee;

    @Column(name = "admin_amount")
    private BigDecimal adminAmount;

    @Column(name = "gst_amount")
    private BigDecimal gstAmount;

    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    @Column(name = "driver_levy")
    private BigDecimal driverLevy;

    @Column(name = "merit_point_fee")
    private BigDecimal meritPointFee;

    @Column(name = "surcharge_description")
    private String surchargeDescription;

    @Column(name = "charged_to")
    private String chargedTo;

    @Column(name = "complimentary")
    private String complimentary;

    @Column(name = "status")
    private String status;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "fms_status")
    private String fmsStatus;

    @Column(name = "fms_date")
    private LocalDateTime fmsDate;

    @Column(name = "completed_dt")
    private LocalDateTime completedDt;

    @Column(name = "recon_date")
    private LocalDateTime reconDate;

    @Column(name = "ibs_status")
    private String ibsStatus;

    @Column(name = "ibs_date")
    private LocalDateTime ibsDate;

    @Column(name = "flowthru_action")
    private String flowthruAction;

    @Column(name = "offline_flag")
    private String offlineFlag;

    @Column(name = "valid_trip")
    private String validTrip;

    @Column(name = "create_date")
    private LocalDateTime createDate;
}
