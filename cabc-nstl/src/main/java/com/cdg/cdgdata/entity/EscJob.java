package com.cdg.cdgdata.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "esc_job")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EscJob {

    @Id
    @Column(name = "job_no")
    private String jobNo;

    @Column(name = "booking_id")
    private String bookingId;

    @Column(name = "job_type")
    private String jobType;

    @Column(name = "status")
    private String status;

    @Column(name = "dcp_request_id")
    private String dcpRequestId;

    @Column(name = "link_job_no_cn3")
    private String linkJobNoCn3;

    @Column(name = "link_job_no")
    private String linkJobNo;

    @Column(name = "replaced_job_no")
    private String replacedJobNo;

    @Column(name = "pax_contact")
    private String paxContact;

    @Column(name = "pax_name")
    private String paxName;

    @Column(name = "cc_number")
    private String ccNumber;

    @Column(name = "created_dt")
    private LocalDateTime createdDt;

    @Column(name = "updated_dt")
    private LocalDateTime updatedDt;
}
