package com.cdg.cdgdata.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "esc_recon_setl_netsnpx_stg", schema = "cn2_recon")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EscReconSetlNetsnpxStg {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "file_id")
    private String fileId;

    @Column(name = "financial_institution_id")
    private String financialInstitutionId;

    @Column(name = "txn_amount")
    private BigDecimal txnAmount;

    @Column(name = "created_dt")
    private LocalDateTime createdDt;

    @Column(name = "record_type")
    private String recordType;

    @Column(name = "product")
    private String product;

    @Column(name = "transaction_cd")
    private String transactionCd;

    @Column(name = "transaction_dt")
    private LocalDateTime transactionDt;

    @Column(name = "corporation_id")
    private String corporationId;

    @Column(name = "entity_02_id")
    private String entity02Id;

    @Column(name = "entity_03_id")
    private String entity03Id;

    @Column(name = "entity_04_id")
    private String entity04Id;

    @Column(name = "entity_05_id")
    private String entity05Id;

    @Column(name = "amount_01_no")
    private BigDecimal amount01No;

    @Column(name = "amount_02_no")
    private BigDecimal amount02No;

    @Column(name = "amount_03_no")
    private BigDecimal amount03No;

    @Column(name = "amount_04_no")
    private BigDecimal amount04No;

    @Column(name = "amount_05_no")
    private BigDecimal amount05No;

    @Column(name = "fee_01_no")
    private BigDecimal fee01No;

    @Column(name = "fee_02_no")
    private BigDecimal fee02No;

    @Column(name = "fee_03_no")
    private BigDecimal fee03No;

    @Column(name = "fee_04_no")
    private BigDecimal fee04No;

    @Column(name = "fee_05_no")
    private BigDecimal fee05No;

    @Column(name = "card_issuer_id")
    private String cardIssuerId;

    @Column(name = "reversal_cd")
    private String reversalCd;

    @Column(name = "response_cd")
    private String responseCd;

    @Column(name = "etc_01_tx")
    private String etc01Tx;

    @Column(name = "orig_tid")
    private String origTid;

    @Column(name = "orig_timestamp")
    private String origTimestamp;

    @Column(name = "orig_seq_no")
    private String origSeqNo;

    @Column(name = "etc_05_tx")
    private String etc05Tx;

    @Column(name = "can_no")
    private String canNo;

    @Column(name = "sequence_no")
    private String sequenceNo;

    @Column(name = "reference_tx")
    private String referenceTx;

    @Column(name = "void_fl")
    private String voidFl;
}
