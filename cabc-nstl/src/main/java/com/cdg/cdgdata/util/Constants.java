
package com.cdg.cdgdata.util;

import com.cdg.cdgdata.config.AppProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class Constants {

	private static String folderPath;

	@Autowired
	public void setFolderPath(@Value("${app.data.folder-path}") String folderPath) {
		Constants.folderPath = folderPath;
	}

	public static String getFolderPath() {
		return folderPath != null ? folderPath : "C:\\Users\\<USER>\\OneDrive - ComfortDelGro Corporation Limited\\Documents\\CDGData-eclipse\\data\\";
	}

	// For backward compatibility
	public static final String FolderPath = getFolderPath();
}
