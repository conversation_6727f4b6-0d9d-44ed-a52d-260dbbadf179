package com.cdg.cdgdata.util;

import com.cdg.cdgdata.service.SimpleDatabaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;

@Component
@Slf4j
public class Helper {

    private static SimpleDatabaseService databaseService;

    @Autowired
    public void setDatabaseService(SimpleDatabaseService databaseService) {
        Helper.databaseService = databaseService;
    }
	
	public static String GenerateFileName() {
		 
	    String filename = "";
	    long millis = System.currentTimeMillis();
	    String datetime = new Date().toGMTString();
	    datetime = ((String) datetime).replace(" ", "");
	    datetime = ((String) datetime).replace(":", "");

	    filename =  datetime + "_" + millis;
	    return filename;
	
}
	
	public static ArrayList<ArrayList<String>> ReadNPXDBSTIDMap() {
		
		String filename = "NPX_DBS_TID_MAP_MASTER.txt";

		ArrayList<String> nets = new ArrayList<String>();
		ArrayList<String> amex = new ArrayList<String>();
		ArrayList<String> visa = new ArrayList<String>();
		ArrayList<String> mc = new ArrayList<String>();
		ArrayList<String> jcb = new ArrayList<String>();
		ArrayList<ArrayList<String>> result = new ArrayList<ArrayList<String>>();
		String[] data = null;
		
		try  
		{  
			File file=new File(Constants.FolderPath + filename);    //creates a new file instance
			FileReader fr=new FileReader(file);   //reads the file  
			BufferedReader br=new BufferedReader(fr);  //creates a buffering character input stream  
	 
			String line;  
			while((line=br.readLine())!=null)  
			{  
				data = line.split("\\s+"); 
				nets.add(data[0]);
				amex.add(data[1]);
				visa.add(data[2]);
				mc.add(data[3]);
				jcb.add(data[4]);
			}  
			fr.close();    //closes the stream and release the resources  
		
		}  
		catch(IOException e)  
		{  
			e.printStackTrace();  
		} 
		
		result.add(nets);
		result.add(amex);
		result.add(visa);
		result.add(mc);
		result.add(jcb);
		
		return result;
	}
	
	public static ArrayList<String[]> ReadDataSourcePath(String filename) {

		ArrayList<String[]> result = new ArrayList<String[]>();
		String[] data = null;
		
		try  
		{  
			File file=new File(filename);    //creates a new file instance  
			FileReader fr=new FileReader(file);   //reads the file  
			BufferedReader br=new BufferedReader(fr);  //creates a buffering character input stream  
	 
			String line;  
			while((line=br.readLine())!=null)  
			{  
				data = line.split("\\s+"); 
				result.add(data);
			}  
			fr.close();    //closes the stream and release the resources  
		
		}  
		catch(IOException e)  
		{  
			e.printStackTrace();  
		} 
		
		return result;
	}
	
	public static String[] ReadList(String filename) {

		List<String> list = new ArrayList<String>();
		String data = null;
		
		try  
		{  
			File file=new File(Constants.FolderPath + filename);    //creates a new file instance  
			FileReader fr=new FileReader(file);   //reads the file  
			BufferedReader br=new BufferedReader(fr);  //creates a buffering character input stream  
	 
			String line;  
			while((line=br.readLine())!=null)  
			{  
				data = line;
				list.add(data);
			}  
			fr.close();    //closes the stream and release the resources  
		
		}  
		catch(IOException e)  
		{  
			e.printStackTrace();  
		} 
		
		String[] result = list.toArray(new String[0]);
		
		return result;
	}
	
	
	public static ArrayList<String[]> ReadDataSource(String filename) {

		ArrayList<String[]> result = new ArrayList<String[]>();
		String[] data = null;
		
		try  
		{  
			File file=new File(Constants.FolderPath + filename);    //creates a new file instance  
			FileReader fr=new FileReader(file);   //reads the file  
			BufferedReader br=new BufferedReader(fr);  //creates a buffering character input stream  
	 
			String line;  
			while((line=br.readLine())!=null)  
			{  
				data = line.split("\\s+"); 
				result.add(data);
			}  
			fr.close();    //closes the stream and release the resources  
		
		}  
		catch(IOException e)  
		{  
			e.printStackTrace();  
		} 
		
		return result;
	}


	/**
	 * Get database connection using the SimpleDatabaseService
	 * @param database Database name
	 * @return Connection object
	 * @throws SQLException if connection fails
	 */
	public static Connection GetConnection(String database) throws SQLException {
		if (databaseService == null) {
			throw new IllegalStateException("DatabaseService not initialized. Make sure Spring context is loaded.");
		}
		return databaseService.getConnection(database);
	}
	
	public static void WriteTextFile(String sheetname, ArrayList<String> columnList, ArrayList<Object[]> resultList) throws IOException {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + sheetname + "_" + filename + ".txt");
		
		String columns = "";
		for (int i = 0; i < columnList.size(); i++) { 
			columns+=columnList.get(i) + "\t";
		};
		myWriter.write(columns + "\r\n");
		
		//write records
		String records = "";
		for (int i = 0; i < resultList.size(); i++) { 
			Object[] recordObj = resultList.get(i);
			for (int j = 0; j < columnList.size(); j++) {
				if (recordObj[j] != null) {
					records+=recordObj[j].toString() + "\t";
				}
				else {
					records+= "null" + "\t";
				}
				
			}
			myWriter.write(records + "\r\n");
			records = "";
		};
		
		
		
		myWriter.close();
		
	}
	
	
	public static void Splitter(String field, String append) throws IOException  {
		
		String filename = Helper.GenerateFileName();
		FileWriter myWriter = new FileWriter(Constants.FolderPath + "SPLIT_" + filename + ".txt");
		
        //Load data
		ArrayList<String[]> list = Helper.ReadDataSource("SPLITTER.txt");
		int total = list.size();

		int chunk = 1000;
		int cnt = 0;
		String jobs = "";
		for (int counter = 0; counter < list.size(); counter++) {
		    cnt++;
		    if(list.get(counter)[0].length() >0) {
			    jobs = append + list.get(counter)[0] + append + "," + jobs;
			    if(chunk==cnt || counter+1 == total)
			    {
			        jobs = "or " + field + " in (" + jobs.substring(0,jobs.length()-1) + ")";
			        myWriter.write(jobs + "\r\n");
				    
					jobs = "";
					cnt=0;
			    };
		    }
		};
		
		myWriter.close();

		log.info("Splitter done.");

	}

}
