package com.cdg.cdgdata.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

//@Configuration  // Temporarily disabled for debugging
public class DatabaseConfig {

    @Primary
    @Bean(name = "primaryDataSource")
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource primaryDataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    @Bean(name = "ptfsproDataSource")
    @ConfigurationProperties(prefix = "app.datasource.ptfspro")
    public DataSource ptfsproDataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    @Bean(name = "ptfsproIbsDataSource")
    @ConfigurationProperties(prefix = "app.datasource.ptfspro-ibs")
    public DataSource ptfsproIbsDataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    @Bean(name = "ptccproDataSource")
    @ConfigurationProperties(prefix = "app.datasource.ptccpro")
    public DataSource ptccproDataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    @Bean(name = "ptccproDcpDataSource")
    @ConfigurationProperties(prefix = "app.datasource.ptccpro-dcp")
    public DataSource ptccproDcpDataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    @Bean(name = "ptccproTmDataSource")
    @ConfigurationProperties(prefix = "app.datasource.ptccpro-tm")
    public DataSource ptccproTmDataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    @Bean(name = "ptccproPayDataSource")
    @ConfigurationProperties(prefix = "app.datasource.ptccpro-pay")
    public DataSource ptccproPayDataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    @Bean(name = "ptcnuat1DataSource")
    @ConfigurationProperties(prefix = "app.datasource.ptcnuat1")
    public DataSource ptcnuat1DataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    @Bean(name = "ptdaproDataSource")
    @ConfigurationProperties(prefix = "app.datasource.ptdapro")
    public DataSource ptdaproDataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    @Bean(name = "ptcnuat2DataSource")
    @ConfigurationProperties(prefix = "app.datasource.ptcnuat2")
    public DataSource ptcnuat2DataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    @Bean(name = "ptdaproDcpDataSource")
    @ConfigurationProperties(prefix = "app.datasource.ptdapro-dcp")
    public DataSource ptdaproDcpDataSource() {
        return DataSourceBuilder.create()
                .driverClassName("oracle.jdbc.OracleDriver")
                .build();
    }

    // JdbcTemplate beans for each datasource
    @Primary
    @Bean(name = "primaryJdbcTemplate")
    public JdbcTemplate primaryJdbcTemplate() {
        return new JdbcTemplate(primaryDataSource());
    }

    @Bean(name = "ptfsproJdbcTemplate")
    public JdbcTemplate ptfsproJdbcTemplate() {
        return new JdbcTemplate(ptfsproDataSource());
    }

    @Bean(name = "ptfsproIbsJdbcTemplate")
    public JdbcTemplate ptfsproIbsJdbcTemplate() {
        return new JdbcTemplate(ptfsproIbsDataSource());
    }

    @Bean(name = "ptccproJdbcTemplate")
    public JdbcTemplate ptccproJdbcTemplate() {
        return new JdbcTemplate(ptccproDataSource());
    }

    @Bean(name = "ptccproDcpJdbcTemplate")
    public JdbcTemplate ptccproDcpJdbcTemplate() {
        return new JdbcTemplate(ptccproDcpDataSource());
    }

    @Bean(name = "ptccproTmJdbcTemplate")
    public JdbcTemplate ptccproTmJdbcTemplate() {
        return new JdbcTemplate(ptccproTmDataSource());
    }

    @Bean(name = "ptccproPayJdbcTemplate")
    public JdbcTemplate ptccproPayJdbcTemplate() {
        return new JdbcTemplate(ptccproPayDataSource());
    }

    @Bean(name = "ptcnuat1JdbcTemplate")
    public JdbcTemplate ptcnuat1JdbcTemplate() {
        return new JdbcTemplate(ptcnuat1DataSource());
    }

    @Bean(name = "ptdaproJdbcTemplate")
    public JdbcTemplate ptdaproJdbcTemplate() {
        return new JdbcTemplate(ptdaproDataSource());
    }

    @Bean(name = "ptcnuat2JdbcTemplate")
    public JdbcTemplate ptcnuat2JdbcTemplate() {
        return new JdbcTemplate(ptcnuat2DataSource());
    }

    @Bean(name = "ptdaproDcpJdbcTemplate")
    public JdbcTemplate ptdaproDcpJdbcTemplate() {
        return new JdbcTemplate(ptdaproDcpDataSource());
    }
}
