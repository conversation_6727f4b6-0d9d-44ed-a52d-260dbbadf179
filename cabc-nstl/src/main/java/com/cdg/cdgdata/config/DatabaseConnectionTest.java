package com.cdg.cdgdata.config;

import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

@Component
@Slf4j
public class DatabaseConnectionTest {

    @Value("${spring.datasource.url}")
    private String jdbcUrl;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    @PostConstruct
    public void testConnection() {
        log.info("Testing database connection...");
        log.info("JDBC URL: {}", jdbcUrl);
        log.info("Username: {}", username);
        log.info("Password provided: {}", password != null && !password.isEmpty() ? "YES" : "NO");

        try {
            // Test basic connection
            Connection connection = DriverManager.getConnection(jdbcUrl, username, password);
            log.info("✅ Database connection successful!");
            
            // Test simple query
            var statement = connection.createStatement();
            var resultSet = statement.executeQuery("SELECT 1 FROM DUAL");
            if (resultSet.next()) {
                log.info("✅ Database query test successful!");
            }
            
            connection.close();
        } catch (SQLException e) {
            log.error("❌ Database connection failed: {}", e.getMessage());
            log.error("SQL State: {}", e.getSQLState());
            log.error("Error Code: {}", e.getErrorCode());
        }
    }
}
