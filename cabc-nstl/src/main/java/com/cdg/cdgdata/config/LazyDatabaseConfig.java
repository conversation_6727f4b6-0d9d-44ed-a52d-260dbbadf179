package com.cdg.cdgdata.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Lazy Database Configuration - Creates connections only when needed
 * This is more efficient than creating all datasources at startup
 */
@Component
@Slf4j
public class LazyDatabaseConfig {

    // Cache for created datasources
    private final Map<String, DataSource> datasourceCache = new ConcurrentHashMap<>();
    private final Map<String, JdbcTemplate> jdbcTemplateCache = new ConcurrentHashMap<>();

    // Database configuration properties
    private final Map<String, DatabaseProperties> databaseConfigs = new ConcurrentHashMap<>();

    public LazyDatabaseConfig() {
        initializeDatabaseConfigs();
    }

    /**
     * Initialize database configurations
     */
    private void initializeDatabaseConfigs() {
        // Primary database (currently active)
        databaseConfigs.put("PRIMARY", new DatabaseProperties(
            "***********************************",
            "RECUSER",
            "password"
        ));

        // Add other database configurations as needed
        // These will only be used if actually accessed
        databaseConfigs.put("PTFSPRO", new DatabaseProperties(
            "${app.datasource.ptfspro.url:}",
            "${app.datasource.ptfspro.username:}",
            "${app.datasource.ptfspro.password:}"
        ));

        databaseConfigs.put("PTCCPRO", new DatabaseProperties(
            "${app.datasource.ptccpro.url:}",
            "${app.datasource.ptccpro.username:}",
            "${app.datasource.ptccpro.password:}"
        ));

        // Add more as needed...
    }

    /**
     * Get or create a datasource for the specified database
     */
    public DataSource getDataSource(String databaseName) {
        return datasourceCache.computeIfAbsent(databaseName.toUpperCase(), this::createDataSource);
    }

    /**
     * Get or create a JdbcTemplate for the specified database
     */
    public JdbcTemplate getJdbcTemplate(String databaseName) {
        return jdbcTemplateCache.computeIfAbsent(databaseName.toUpperCase(), 
            name -> new JdbcTemplate(getDataSource(name)));
    }

    /**
     * Get a direct connection for legacy code compatibility
     */
    public Connection getConnection(String databaseName) throws SQLException {
        DataSource dataSource = getDataSource(databaseName);
        return dataSource.getConnection();
    }

    /**
     * Create a new datasource for the specified database
     */
    private DataSource createDataSource(String databaseName) {
        DatabaseProperties config = databaseConfigs.get(databaseName);
        if (config == null) {
            log.warn("No configuration found for database: {}, using PRIMARY", databaseName);
            config = databaseConfigs.get("PRIMARY");
        }

        log.info("Creating datasource for database: {}", databaseName);

        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(config.getUrl());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setDriverClassName("oracle.jdbc.OracleDriver");
        
        // Optimized pool settings for lazy loading
        hikariConfig.setMaximumPoolSize(10);  // Smaller pool size
        hikariConfig.setMinimumIdle(1);       // Minimal idle connections
        hikariConfig.setIdleTimeout(300000);  // 5 minutes
        hikariConfig.setConnectionTimeout(10000);
        hikariConfig.setMaxLifetime(1200000); // 20 minutes
        hikariConfig.setLeakDetectionThreshold(60000);
        
        // Pool name for monitoring
        hikariConfig.setPoolName("LazyPool-" + databaseName);

        return new HikariDataSource(hikariConfig);
    }

    /**
     * Test connection to a database
     */
    public boolean testConnection(String databaseName) {
        try {
            JdbcTemplate template = getJdbcTemplate(databaseName);
            template.execute("SELECT 1 FROM DUAL");
            log.info("Database connection test successful for: {}", databaseName);
            return true;
        } catch (Exception e) {
            log.error("Database connection test failed for: {}", databaseName, e);
            return false;
        }
    }

    /**
     * Close and remove a datasource from cache (for cleanup)
     */
    public void closeDataSource(String databaseName) {
        String key = databaseName.toUpperCase();
        
        // Remove from caches
        jdbcTemplateCache.remove(key);
        DataSource dataSource = datasourceCache.remove(key);
        
        // Close the datasource if it's HikariDataSource
        if (dataSource instanceof HikariDataSource) {
            ((HikariDataSource) dataSource).close();
            log.info("Closed datasource for database: {}", databaseName);
        }
    }

    /**
     * Get statistics about active datasources
     */
    public Map<String, String> getDataSourceStats() {
        Map<String, String> stats = new ConcurrentHashMap<>();
        
        datasourceCache.forEach((name, dataSource) -> {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDS = (HikariDataSource) dataSource;
                stats.put(name, String.format("Active: %d, Idle: %d, Total: %d", 
                    hikariDS.getHikariPoolMXBean().getActiveConnections(),
                    hikariDS.getHikariPoolMXBean().getIdleConnections(),
                    hikariDS.getHikariPoolMXBean().getTotalConnections()));
            }
        });
        
        return stats;
    }

    /**
     * Database configuration properties
     */
    private static class DatabaseProperties {
        private final String url;
        private final String username;
        private final String password;

        public DatabaseProperties(String url, String username, String password) {
            this.url = url;
            this.username = username;
            this.password = password;
        }

        public String getUrl() { return url; }
        public String getUsername() { return username; }
        public String getPassword() { return password; }
    }
}
