# Application Configuration (Default/Production Profile)
# For local development, use: --spring.profiles.active=test or --spring.profiles.active=dev
spring.application.name=CDG Data Processing Application
spring.main.banner-mode=console

# Logging Configuration
logging.level.com.cdg.cdgdata=INFO
logging.level.org.springframework=WARN

# JPA/Hibernate Startup and Connection Logging
logging.level.org.springframework.orm.jpa=DEBUG
logging.level.org.springframework.boot.autoconfigure.orm.jpa=DEBUG
logging.level.org.hibernate.engine.jdbc.env.internal=DEBUG
logging.level.org.hibernate.dialect=DEBUG
logging.level.org.hibernate.cfg=DEBUG
logging.level.org.hibernate.boot=DEBUG
logging.level.org.hibernate.engine.jdbc.connections=DEBUG
logging.level.org.springframework.beans.factory=DEBUG

# DataSource and Connection Pool Logging
logging.level.com.zaxxer.hikari=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.com.zaxxer.hikari.HikariDataSource=DEBUG
logging.level.org.springframework.jdbc.datasource=DEBUG
logging.level.org.springframework.boot.autoconfigure.jdbc=DEBUG

# Entity scanning and repository initialization
logging.level.org.springframework.data.jpa=DEBUG
logging.level.org.springframework.context=DEBUG

# SQL Logging (optional - uncomment if you want to see SQL queries)
# logging.level.org.hibernate.SQL=DEBUG
# logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# File paths
app.data.folder-path=C:/Users/<USER>/OneDrive - ComfortDelGro Corporation Limited/Documents/CDGData-eclipse/data/

# Primary Database Configuration (Docker Oracle)
spring.datasource.url=***********************************
spring.datasource.username=RECUSER
spring.datasource.password=ociPro$reC_450
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.Oracle12cDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# JPA startup optimization
spring.jpa.defer-datasource-initialization=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# HikariCP Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=10000
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.leak-detection-threshold=60000
spring.datasource.hikari.initialization-fail-timeout=30000
spring.datasource.hikari.validation-timeout=5000
