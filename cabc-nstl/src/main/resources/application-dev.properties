# Development Profile Configuration for Local Docker Oracle Database
# Usage: Run with --spring.profiles.active=dev or set SPRING_PROFILES_ACTIVE=dev

# Application Configuration
spring.application.name=CDG Data Processing Application (Development Mode)
spring.main.banner-mode=console

# Development Logging (Balanced for development)
logging.level.com.cdg.cdgdata=DEBUG
logging.level.org.springframework=WARN
logging.level.org.springframework.orm.jpa=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=DEBUG
logging.level.com.zaxxer.hikari=INFO
logging.level.org.springframework.jdbc.datasource=INFO

logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [DEV] - %msg%n

# Development Data Folder (Local directory)
app.data.folder-path=./dev-data/

# Docker Oracle Database Configuration (Same as test but with dev settings)
spring.datasource.url=***********************************
spring.datasource.username=RECUSER
spring.datasource.password=ociPro$reC_450
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver

# Development JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.Oracle12cDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# JPA startup optimization
spring.jpa.defer-datasource-initialization=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# HikariCP Configuration (Development optimized)
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.minimum-idle=3
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=10000
spring.datasource.hikari.max-lifetime=900000
spring.datasource.hikari.leak-detection-threshold=45000
spring.datasource.hikari.initialization-fail-timeout=15000
spring.datasource.hikari.validation-timeout=4000
spring.datasource.hikari.pool-name=DevHikariPool

# Development connection validation
spring.datasource.hikari.connection-test-query=SELECT 1 FROM DUAL
spring.datasource.hikari.test-while-idle=true
