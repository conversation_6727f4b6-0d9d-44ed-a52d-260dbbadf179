# Application Configuration
spring.application.name=CDG Data Processing Application
spring.main.banner-mode=console

# Logging Configuration
logging.level.com.cdg.cdgdata=INFO
logging.level.org.springframework=WARN

# JPA/Hibernate Startup and Connection Logging
logging.level.org.springframework.orm.jpa=INFO
logging.level.org.springframework.boot.autoconfigure.orm.jpa=INFO
logging.level.org.hibernate.engine.jdbc.env.internal=INFO
logging.level.org.hibernate.dialect=INFO
logging.level.org.hibernate.cfg=INFO
logging.level.org.hibernate.boot=INFO

# DataSource and Connection Pool Logging
logging.level.com.zaxxer.hikari=INFO
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.com.zaxxer.hikari.HikariDataSource=INFO
logging.level.org.springframework.jdbc.datasource=INFO

# SQL Logging (disabled in production - uncomment if needed for debugging)
# logging.level.org.hibernate.SQL=DEBUG
# logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# File paths
app.data.folder-path=C:/Users/<USER>/OneDrive - ComfortDelGro Corporation Limited/Documents/CDGData-eclipse/data/

# Primary Database Configuration (ptfspro)
spring.datasource.jdbc-url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.username=RECUSER
spring.datasource.password=ociPro$reC_450
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.Oracle12cDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# HikariCP Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.leak-detection-threshold=60000

# Multiple Database Configurations
# PTFSPRO
app.datasource.ptfspro.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
app.datasource.ptfspro.username=RECUSER
app.datasource.ptfspro.password=ociPro$reC_450
app.datasource.ptfspro.driver-class-name=oracle.jdbc.OracleDriver

# PTFSPRO IBS
app.datasource.ptfspro-ibs.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
app.datasource.ptfspro-ibs.username=IBSUSER
app.datasource.ptfspro-ibs.password=ociPro$ibU_416
app.datasource.ptfspro-ibs.driver-class-name=oracle.jdbc.OracleDriver

# PTCCPRO
app.datasource.ptccpro.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=***********))(connect_data=(service_name=gbe786bc9113b95_ptccpro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
app.datasource.ptccpro.username=cn2user
app.datasource.ptccpro.password=ociPro$dCN2_112
app.datasource.ptccpro.driver-class-name=oracle.jdbc.OracleDriver

# PTCCPRO DCP
app.datasource.ptccpro-dcp.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=***********))(connect_data=(service_name=gbe786bc9113b95_ptccpro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
app.datasource.ptccpro-dcp.username=dcp_user
app.datasource.ptccpro-dcp.password=ociPro$gDCP_255
app.datasource.ptccpro-dcp.driver-class-name=oracle.jdbc.OracleDriver

# PTCCPRO TM
app.datasource.ptccpro-tm.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=***********))(connect_data=(service_name=gbe786bc9113b95_ptccpro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
app.datasource.ptccpro-tm.username=cn2tmuser
app.datasource.ptccpro-tm.password=ociPro$iCN2_141
app.datasource.ptccpro-tm.driver-class-name=oracle.jdbc.OracleDriver

# PTCCPRO PAY
app.datasource.ptccpro-pay.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=***********))(connect_data=(service_name=gbe786bc9113b95_ptccpro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
app.datasource.ptccpro-pay.username=pay_as_user
app.datasource.ptccpro-pay.password=ociPro$cPAY_248
app.datasource.ptccpro-pay.driver-class-name=oracle.jdbc.OracleDriver

# PTCNUAT1
app.datasource.ptcnuat1.url=***********************************= (failover=on) (load_balance=off) (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptcnuat1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptcnuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes))))
app.datasource.ptcnuat1.username=CN2USER
app.datasource.ptcnuat1.password=ocicUat$125_ah
app.datasource.ptcnuat1.driver-class-name=oracle.jdbc.OracleDriver

# PTDAPRO
app.datasource.ptdapro.url=***********************************= (failover=on) (load_balance=off) (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=***********))(connect_data=(service_name=gbe786bc9113b95_ptdapro_low.adb.oraclecloud.com))(security=(ssl_server_dn_match=no))))
app.datasource.ptdapro.username=cn2user
app.datasource.ptdapro.password=ociPro$cnV_113
app.datasource.ptdapro.driver-class-name=oracle.jdbc.OracleDriver

# PTCNUAT2
app.datasource.ptcnuat2.url=***********************************= (failover=on) (load_balance=off)(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=**********))(connect_data=(service_name=gbe786bc9113b95_ptcnuat2_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no))))
app.datasource.ptcnuat2.username=cn2tmapps
app.datasource.ptcnuat2.password=ociUat$L_168
app.datasource.ptcnuat2.driver-class-name=oracle.jdbc.OracleDriver

# PTDAPRO DCP
app.datasource.ptdapro-dcp.url=***********************************= (failover=on) (load_balance=off) (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=***********))(connect_data=(service_name=gbe786bc9113b95_ptdapro_low.adb.oraclecloud.com))(security=(ssl_server_dn_match=no))))
app.datasource.ptdapro-dcp.username=DCP_USER
app.datasource.ptdapro-dcp.password=ociPro$dcJ_139
app.datasource.ptdapro-dcp.driver-class-name=oracle.jdbc.OracleDriver

# Web application configuration
spring.main.web-application-type=servlet
server.port=8080
server.servlet.context-path=/cdgdata
