# Test Profile Configuration for Local Docker Oracle Database
# Usage: Run with --spring.profiles.active=test or set SPRING_PROFILES_ACTIVE=test

# Application Configuration
spring.application.name=CDG Data Processing Application (Test Mode)
spring.main.banner-mode=console

# Test Environment Logging (More verbose for debugging)
logging.level.com.cdg.cdgdata=DEBUG
logging.level.org.springframework=INFO
logging.level.org.springframework.orm.jpa=DEBUG
logging.level.org.springframework.boot.autoconfigure.orm.jpa=DEBUG
logging.level.org.hibernate.engine.jdbc.env.internal=DEBUG
logging.level.org.hibernate.dialect=DEBUG
logging.level.com.zaxxer.hikari=DEBUG
logging.level.org.springframework.jdbc.datasource=DEBUG
logging.level.org.springframework.data.jpa=DEBUG

# SQL Logging (Enabled for debugging)
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [TEST] - %msg%n

# Test Data Folder (Local directory)
app.data.folder-path=./test-data/

# Docker Oracle Database Configuration
# Make sure your Docker Oracle container is running on these settings
spring.datasource.url=***********************************
spring.datasource.username=RECUSER
spring.datasource.password=ociPro$reC_450
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver

# Test JPA Configuration (More permissive for testing)
spring.jpa.database-platform=org.hibernate.dialect.Oracle12cDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.jdbc.batch_size=10
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.hbm2ddl.auto=none

# JPA startup optimization for testing
spring.jpa.defer-datasource-initialization=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# HikariCP Configuration (Optimized for local testing)
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=10000
spring.datasource.hikari.max-lifetime=600000
spring.datasource.hikari.leak-detection-threshold=30000
spring.datasource.hikari.initialization-fail-timeout=10000
spring.datasource.hikari.validation-timeout=3000
spring.datasource.hikari.pool-name=TestHikariPool

# Test-specific connection validation
spring.datasource.hikari.connection-test-query=SELECT 1 FROM DUAL
spring.datasource.hikari.test-while-idle=true
spring.datasource.hikari.test-on-borrow=true
