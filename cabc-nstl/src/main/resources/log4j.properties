# Define the root logger with appender file  
log = logs
log4j.rootLogger = DEBUG, FILE  
  
# Define the file appender  
log4j.appender.FILE=org.apache.log4j.FileAppender  
log4j.appender.FILE.File=${log}/log.out  
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.FILE.layout.ConversionPattern=%d %-5p %X{id} = %m - %c (%F:%L)%n
log4j.appender.FILE.encoding = UTF-8
log4j.appender.FILE.append = true

# Define the layout for file appender  
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout  
log4j.appender.FILE.layout.conversionPattern=%m%n  