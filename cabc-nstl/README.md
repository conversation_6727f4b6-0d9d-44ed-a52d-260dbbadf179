# CDG Data Processing Application

A Spring Boot application for processing CDG data with JPA, REST APIs, and Excel generation capabilities.

## Project Structure

This project has been migrated from Eclipse to IntelliJ IDEA with a modern Spring Boot architecture:

```
src/main/java/com/cdg/cdgdata/
├── entity/           # JPA Entities
├── repository/       # JPA Repositories  
├── service/          # Business Logic Services
├── dto/              # Data Transfer Objects
├── controller/       # REST Controllers
├── config/           # Configuration Classes
└── util/             # Utility Classes
```

## Prerequisites

- **Java 8** or higher
- **IntelliJ IDEA** (recommended IDE)
- **Gradle** (included via wrapper)

## IntelliJ IDEA Setup

### 1. Import Project
1. Open IntelliJ IDEA
2. Choose "Open or Import"
3. Select the project root directory
4. Choose "Import project from external model" → "Gradle"
5. Use default settings and click "OK"

### 2. Configure Project SDK
1. Go to `File` → `Project Structure` → `Project`
2. Set Project SDK to Java 8
3. Set Project language level to "8 - Lambdas, type annotations etc."

### 3. Enable Annotation Processing
1. Go to `File` → `Settings` → `Build, Execution, Deployment` → `Compiler` → `Annotation Processors`
2. Check "Enable annotation processing"
3. This is required for Lombok to work properly

### 4. Install Required Plugins
Install these IntelliJ plugins for optimal development experience:
- **Lombok Plugin** (for @Data, @Slf4j, etc.)
- **Spring Boot Plugin** (usually pre-installed)
- **Database Tools and SQL** (for database management)

## Running the Application

### Using IntelliJ Run Configurations
The project includes pre-configured run configurations:

1. **CdgDataApplication** - Run the Spring Boot application
2. **Gradle - Build** - Build the project
3. **Gradle - Clean Build** - Clean and build the project

### Using Gradle Commands
```bash
# Build the project
./gradlew build

# Run the application
./gradlew bootRun

# Clean and build
./gradlew clean build
```

### Using Java directly
```bash
java -jar build/libs/cdgdata-1.0.0.jar
```

## API Endpoints

The application provides REST APIs for data processing:

### CABC Error 51 Processing
```bash
# Process data for a specific date
POST http://localhost:8080/cdgdata/api/cabc-error51/process?targetDay=09/06/2025

# Health check
GET http://localhost:8080/cdgdata/api/cabc-error51/health
```

## Configuration

### Database Configuration
Update `src/main/resources/application.properties` with your database credentials:

```properties
# Primary Database (PTFSPRO)
spring.datasource.url=jdbc:oracle:thin:@(...)
spring.datasource.username=RECUSER
spring.datasource.password=your_password

# Additional databases configured with app.datasource.* prefix
```

### Application Properties
```properties
# File paths
app.data.folder-path=C:/path/to/your/data/folder/

# Server configuration
server.port=8080
server.servlet.context-path=/cdgdata
```

## Development Features

### Code Style
- Configured with Java code style settings
- 120 character line limit
- Proper import organization
- Consistent formatting

### Logging
- Uses SLF4J with Logback
- Lombok @Slf4j annotation for clean logging
- Configurable log levels in application.properties

### Database Access
- JPA entities for type-safe database access
- Multiple database support
- Connection pooling via Spring Boot
- Custom repository methods with @Query

### Excel Generation
- Apache POI integration
- Automated Excel file generation
- Multiple sheets support
- Proper styling and formatting

## Migration from Eclipse

This project has been successfully migrated from Eclipse to IntelliJ with the following improvements:

### ✅ Completed
- Removed all Eclipse files (.project, .classpath, .settings/, bin/)
- Configured IntelliJ project settings
- Set up Gradle build system
- Added Spring Boot framework
- Implemented JPA with proper entity structure
- Created REST API endpoints
- Added Lombok for clean code
- Configured annotation processing

### 🔧 IntelliJ Advantages
- **Better Gradle Integration** - Native Gradle support
- **Superior Code Completion** - Advanced IntelliSense
- **Built-in Spring Boot Support** - Spring-specific features
- **Database Tools** - Integrated database management
- **Version Control** - Better Git integration
- **Debugging** - Advanced debugging capabilities
- **Refactoring** - Powerful refactoring tools

## Troubleshooting

### Common Issues

1. **Lombok not working**
   - Ensure Lombok plugin is installed
   - Enable annotation processing in settings
   - Restart IntelliJ after installation

2. **Database connection issues**
   - Check database credentials in application.properties
   - Ensure Oracle JDBC driver is available
   - Verify network connectivity to database

3. **Build issues**
   - Run `./gradlew clean build` to clean and rebuild
   - Check Java version compatibility
   - Ensure all dependencies are downloaded

### Getting Help

1. Check the logs in `logs/` directory
2. Use IntelliJ's built-in debugger
3. Review the JPA_REFACTORING_SUMMARY.md for detailed architecture information

## Contributing

1. Use the configured code style settings
2. Write unit tests for new features
3. Follow Spring Boot best practices
4. Use proper logging with @Slf4j
5. Document API endpoints

## License

Internal CDG project - All rights reserved.
