plugins {
    id 'org.springframework.boot' version '2.7.18'
    id 'io.spring.dependency-management' version '1.0.15.RELEASE'
    id 'java'
}

group = 'com.cdg'
version = '1.0.0'
sourceCompatibility = '1.8'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot starters
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'

    // Database
    implementation 'com.oracle.database.************************'

    // Apache POI for Excel operations
    implementation 'org.apache.poi:poi:5.2.4'
    implementation 'org.apache.poi:poi-ooxml:5.2.4'
    implementation 'org.apache.poi:poi-scratchpad:5.2.4'

    // PDF processing
    implementation 'com.itextpdf:itextpdf:5.5.13.3'

    // File upload utilities
    implementation 'commons-fileupload:commons-fileupload:1.5'
    implementation 'commons-codec:commons-codec:1.16.0'

    // PGP utilities
    implementation 'org.bouncycastle:bcpg-jdk15on:1.70'
    implementation 'org.bouncycastle:bcprov-jdk15on:1.70'
    implementation 'org.c02e.jpgpj:jpgpj:0.6'

    // Logging (Spring Boot includes logback by default, but keeping log4j if needed)
    implementation 'org.springframework.boot:spring-boot-starter-logging'

    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'com.h2database:h2'

    // Development tools
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    //org.projectlombok:lombok:1.18.34
    compileOnly 'org.projectlombok:lombok:1.18.34'
    annotationProcessor 'org.projectlombok:lombok:1.18.34'
}

tasks.named('test') {
    useJUnitPlatform()
}

// Custom task to run specific data processing jobs
task runDataProcessing(type: JavaExec) {
    classpath = sourceSets.main.runtimeClasspath
    mainClass = 'com.cdg.cdgdata.CdgDataApplication'
    args = ['--spring.profiles.active=default']
}

// Jar configuration
jar {
    enabled = false
    archiveClassifier = ''
}

// Fat jar configuration
bootJar {
    enabled = true
    archiveClassifier = ''
    mainClass = 'com.cdg.cdgdata.CdgDataApplication'
}
