version: '3.8'

services:
  oracle-db:
    image: gvenzl/oracle-xe:21-slim-faststart
    container_name: oracle-test-db
    environment:
      # Database configuration
      ORACLE_PASSWORD: "ociPro$$reC_450"
      ORACLE_DATABASE: XE

      # User configuration
      APP_USER: RECUSER
      APP_USER_PASSWORD: "ociPro$$reC_450"
      
      # Optional: Enable automatic database startup
      ORACLE_CHARACTERSET: AL32UTF8
      
    ports:
      - "1521:1521"
      - "5500:5500"  # Oracle Enterprise Manager Express
      
    volumes:
      # Persist database data
      - oracle_data:/opt/oracle/oradata
      
      # Mount initialization scripts (optional)
      - ./deployment/init-scripts:/docker-entrypoint-initdb.d/startup
      
    healthcheck:
      test: ["CMD", "healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
      
    restart: unless-stopped
    
    # Resource limits for development
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

volumes:
  oracle_data:
    driver: local

networks:
  default:
    name: oracle-network
