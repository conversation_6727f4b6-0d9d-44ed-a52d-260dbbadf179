@echo off
echo Starting CDG Data Application in TEST mode...
echo.

REM Check if Oracle container is running
docker ps | findstr oracle-test-db >nul
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  Oracle database container is not running!
    echo.
    echo Please start it first by running: start-oracle-docker.bat
    echo Or manually: docker-compose -f docker-compose-oracle.yml up -d
    echo.
    pause
    exit /b 1
)

echo ✅ Oracle database container is running
echo.
echo 🚀 Starting application with TEST profile...
echo    - Enhanced logging enabled
echo    - SQL debugging enabled
echo    - Connecting to Docker Oracle database
echo.

REM Run the application with test profile
gradlew.bat bootRun --args="--spring.profiles.active=test"

echo.
echo Application finished.
pause
