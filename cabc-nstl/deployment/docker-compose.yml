version: '3.8'

services:
  oracle-db:
    image: gvenzl/oracle-xe:21-slim
    container_name: cdg-oracle-db
    environment:
      - ORACLE_PASSWORD=OraclePassword123
      - ORACLE_DATABASE=CDGDB
      - APP_USER=CDGUSER
      - APP_USER_PASSWORD=CdgPassword123
    ports:
      - "1521:1521"
    volumes:
      - oracle-data:/opt/oracle/oradata
      - ../init-scripts:/container-entrypoint-initdb.d
    networks:
      - cdg-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "sqlplus", "-L", "system/OraclePassword123@//localhost:1521/XE", "@/container-entrypoint-initdb.d/99_healthcheck.sql"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  adminer:
    image: adminer:4.8.1
    container_name: cdg-adminer
    ports:
      - "8081:8080"
    networks:
      - cdg-network
    depends_on:
      - oracle-db
    environment:
      - ADMINER_DEFAULT_SERVER=oracle-db:1521
      - ADMINER_DESIGN=pepa-linha

volumes:
  oracle-data:
    driver: local

networks:
  cdg-network:
    driver: bridge
