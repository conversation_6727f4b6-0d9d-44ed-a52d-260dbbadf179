-- CDG Database Mock Data Insertion Script

-- Insert mock data into RECSYS.rctb_trip_intf
INSERT INTO RECSYS.rctb_trip_intf (
    trip_intf_pk, job_no, vehicle_no, driver_ic, entity, booked_channel,
    booked_date_time, pax_name, booking_reference, trip_start, trip_end,
    pickup, destination, job_type, job_status, payment_mode, card_no,
    approval_code, txn_amount, total_amount, status, error_code,
    completed_dt, recon_date, valid_trip
) VALUES (
    RECSYS.rctb_trip_intf_seq.NEXTVAL, 'JOB001', 'SH1234A', 'S1234567A', 'CDG', 'APP',
    SYSDATE - 1, '<PERSON>', 'BK001', SYSDATE - 1, SYSDATE - 1 + 0.5/24,
    'Orchard Road', 'Marina Bay', 'NORMAL', 'COMPLETED', 'CABC', '1234****5678',
    'APP001', 25.50, 25.50, 'S', NULL,
    SYSDATE - 1 + 0.5/24, SYSDATE, 'Y'
);

INSERT INTO RECSYS.rctb_trip_intf (
    trip_intf_pk, job_no, vehicle_no, driver_ic, entity, booked_channel,
    booked_date_time, pax_name, booking_reference, trip_start, trip_end,
    pickup, destination, job_type, job_status, payment_mode, card_no,
    approval_code, txn_amount, total_amount, status, error_code,
    completed_dt, recon_date, valid_trip
) VALUES (
    RECSYS.rctb_trip_intf_seq.NEXTVAL, 'JOB002', 'SH5678B', 'S2345678B', 'CDG', 'CALL',
    SYSDATE - 2, 'Jane Smith', 'BK002', SYSDATE - 2, SYSDATE - 2 + 0.75/24,
    'Raffles Place', 'Sentosa', 'NORMAL', 'COMPLETED', 'EVCH', '9876****1234',
    'APP002', 45.80, 45.80, 'E', 'NSTL',
    SYSDATE - 2 + 0.75/24, SYSDATE, 'Y'
);

-- Insert mock data into RECUSER.esc_job
INSERT INTO RECUSER.esc_job (
    job_no, booking_id, job_type, status, pax_contact, pax_name, cc_number
) VALUES (
    'JOB001', 'BK001', 'NORMAL', 'COMPLETED', '+6591234567', 'John Doe', '1234****5678'
);

INSERT INTO RECUSER.esc_job (
    job_no, booking_id, job_type, status, pax_contact, pax_name, cc_number
) VALUES (
    'JOB002', 'BK002', 'NORMAL', 'COMPLETED', '+6597654321', 'Jane Smith', '9876****1234'
);

-- Insert mock data into RECUSER.esc_trip_details
INSERT INTO RECUSER.esc_trip_details (
    id, job_no, end_dt, amount_paid, start_y, start_x, end_y, end_x,
    platform_fee, valid_trip, distance, payment_method
) VALUES (
    RECUSER.esc_trip_details_seq.NEXTVAL, 'JOB001', SYSDATE - 1 + 0.5/24, 25.50,
    1.3048, 103.8318, 1.2966, 103.8520, 2.50, 'Y', 12.5, 'CABC'
);

INSERT INTO RECUSER.esc_trip_details (
    id, job_no, end_dt, amount_paid, start_y, start_x, end_y, end_x,
    platform_fee, valid_trip, distance, payment_method
) VALUES (
    RECUSER.esc_trip_details_seq.NEXTVAL, 'JOB002', SYSDATE - 2 + 0.75/24, 45.80,
    1.2840, 103.8510, 1.2494, 103.8303, 4.50, 'Y', 18.7, 'EVCH'
);

-- Insert mock data into CN2_RECON.esc_recon_setl_netsnpx_stg
INSERT INTO CN2_RECON.esc_recon_setl_netsnpx_stg (
    id, file_id, financial_institution_id, txn_amount, record_type,
    product, transaction_cd, transaction_dt, orig_tid, orig_timestamp, orig_seq_no
) VALUES (
    CN2_RECON.esc_recon_stg_seq.NEXTVAL, 'NPX20250615001', 'DBS', 25.50, 'TXN',
    'CABC', '00', SYSDATE - 1, 'TID001', '20250614120000', '123456'
);

-- Insert mock data into CN2_RECON.esc_recon_setl_netsnpx (missing from staging)
INSERT INTO CN2_RECON.esc_recon_setl_netsnpx (
    id, file_id, financial_institution_id, txn_amount, record_type,
    product, transaction_cd, transaction_dt, orig_tid, orig_timestamp, orig_seq_no,
    recon_date, recon_status
) VALUES (
    CN2_RECON.esc_recon_setl_seq.NEXTVAL, 'NPX20250615002', 'UOB', 45.80, 'TXN',
    'EVCH', '00', SYSDATE - 2, 'TID002', '20250613130000', '789012',
    SYSDATE, 'MATCHED'
);

-- Insert mock data into PAY_AS_DATA.iso8583_incoming
INSERT INTO PAY_AS_DATA.iso8583_incoming (
    msg_id, msg_typ_id, pan, txn_amt, txn_date, tripinformation, expiry_date
) VALUES (
    PAY_AS_DATA.iso8583_seq.NEXTVAL, '0200', '1234****5678', '002550', SYSDATE - 1,
    'TRIP001|JOB001|CDG|1.3048|103.8318|1.2966|103.8520|COMPLETED|CABC|25.50|SGD|APP001|TID001|123456', '2512'
);

-- Insert mock data into DCP_USER.pytb_wd_txn
INSERT INTO DCP_USER.pytb_wd_txn (
    wd_txn_id, booking_ref, txn_type, amount, txn_date, status, merchant_id, terminal_id
) VALUES (
    DCP_USER.pytb_wd_txn_seq.NEXTVAL, 'BK001', 'PAYMENT', 25.50, SYSDATE - 1, 'SUCCESS', 'CDG001', 'TID001'
);

-- Insert mock data into CN2TMSYS.tmtb_pl_txn_log
INSERT INTO CN2TMSYS.tmtb_pl_txn_log (
    id, job_number, booking_num, msg_type, txn_amount, status, response_code
) VALUES (
    CN2TMSYS.tmtb_pl_txn_log_seq.NEXTVAL, 'JOB001', 'BK001', 'PAYMENT_REQUEST', 25.50, 'SUCCESS', '00'
);

-- Insert mock data into CN2TMSYS.tmtb_pl_paylah_txn_log
INSERT INTO CN2TMSYS.tmtb_pl_paylah_txn_log (
    txn_ref_id, pl_txn_log_id, txn_amount, status, merchant_ref
) VALUES (
    'PL001', CN2TMSYS.tmtb_pl_txn_log_seq.CURRVAL, 25.50, 'SUCCESS', 'CDG_BK001'
);

-- Insert mock data into CITYNET2.esc_payment_transaction
INSERT INTO CITYNET2.esc_payment_transaction (
    txn_id, job_no, payment_method, amount, status, reference_no, citynet_ref
) VALUES (
    CITYNET2.citynet2_payment_seq.NEXTVAL, 'JOB001', 'CABC', 25.50, 'SUCCESS', 'TXN001', 'CN2_TXN001'
);

COMMIT;
