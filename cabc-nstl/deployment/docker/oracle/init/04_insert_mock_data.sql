-- CDG Database Mock Data Insertion Script
-- This script inserts sample data for testing

-- Connect as RECSYS to insert RCTB data
CONNECT RECSYS/recsys123@XE;

-- Insert mock data into RCTB_TRIP_INTF
INSERT INTO rctb_trip_intf (
    trip_intf_pk, job_no, vehicle_no, driver_ic, entity, booked_channel,
    booked_date_time, pax_name, booking_reference, trip_start, trip_end,
    pickup, destination, job_type, job_status, payment_mode, card_no,
    approval_code, txn_amount, total_amount, status, error_code,
    completed_dt, recon_date, valid_trip
) VALUES (
    rctb_trip_intf_seq.NEXTVAL, 'JOB001', 'SH1234A', 'S1234567A', 'CDG', 'APP',
    SYSDATE - 1, '<PERSON>', 'BK001', SYSDATE - 1, SYSDATE - 1 + 0.5/24,
    'Orchard Road', 'Marina Bay', 'NORMAL', 'COMPLETED', 'CABC', '1234****5678',
    'APP001', 25.50, 25.50, 'S', NULL,
    SYSDATE - 1 + 0.5/24, SYSDATE, 'Y'
);

INSERT INTO rctb_trip_intf (
    trip_intf_pk, job_no, vehicle_no, driver_ic, entity, booked_channel,
    booked_date_time, pax_name, booking_reference, trip_start, trip_end,
    pickup, destination, job_type, job_status, payment_mode, card_no,
    approval_code, txn_amount, total_amount, status, error_code,
    completed_dt, recon_date, valid_trip
) VALUES (
    rctb_trip_intf_seq.NEXTVAL, 'JOB002', 'SH5678B', 'S2345678B', 'CDG', 'CALL',
    SYSDATE - 2, 'Jane Smith', 'BK002', SYSDATE - 2, SYSDATE - 2 + 0.75/24,
    'Raffles Place', 'Sentosa', 'NORMAL', 'COMPLETED', 'EVCH', '9876****1234',
    'APP002', 45.80, 45.80, 'E', 'NSTL',
    SYSDATE - 2 + 0.75/24, SYSDATE, 'Y'
);

-- Insert mock data into RCTB_SETL
INSERT INTO rctb_setl (
    setl_pk, job_no, entity, txn_date, card_no, approval_code,
    txn_amount, tid, stan, card_type, file_id
) VALUES (
    rctb_setl_seq.NEXTVAL, 'JOB001', 'CDG', SYSDATE - 1, '1234****5678', 'APP001',
    25.50, 'TID001', '123456', 'VISA', 'SETL20250615001'
);

-- Connect as RECUSER to insert ESC data
CONNECT RECUSER/ociPro$reC_450@XE;

-- Insert mock data into ESC_JOB
INSERT INTO esc_job (
    job_no, booking_id, job_type, status, pax_contact, pax_name, cc_number
) VALUES (
    'JOB001', 'BK001', 'NORMAL', 'COMPLETED', '+6591234567', 'John Doe', '1234****5678'
);

INSERT INTO esc_job (
    job_no, booking_id, job_type, status, pax_contact, pax_name, cc_number
) VALUES (
    'JOB002', 'BK002', 'NORMAL', 'COMPLETED', '+6597654321', 'Jane Smith', '9876****1234'
);

-- Insert mock data into ESC_TRIP_DETAILS
INSERT INTO esc_trip_details (
    id, job_no, end_dt, amount_paid, start_y, start_x, end_y, end_x,
    platform_fee, valid_trip, distance, payment_method
) VALUES (
    esc_trip_details_seq.NEXTVAL, 'JOB001', SYSDATE - 1 + 0.5/24, 25.50,
    1.3048, 103.8318, 1.2966, 103.8520, 2.50, 'Y', 12.5, 'CABC'
);

INSERT INTO esc_trip_details (
    id, job_no, end_dt, amount_paid, start_y, start_x, end_y, end_x,
    platform_fee, valid_trip, distance, payment_method
) VALUES (
    esc_trip_details_seq.NEXTVAL, 'JOB002', SYSDATE - 2 + 0.75/24, 45.80,
    1.2840, 103.8510, 1.2494, 103.8303, 4.50, 'Y', 18.7, 'EVCH'
);

-- Insert mock data into ESC_BOOKING
INSERT INTO esc_booking (
    booking_id, booking_reference, pax_name, pax_contact,
    pickup_address, destination_address, status
) VALUES (
    'BK001', 'REF001', 'John Doe', '+6591234567',
    'Orchard Road, Singapore', 'Marina Bay, Singapore', 'COMPLETED'
);

-- Insert mock data into ESC_PAYMENT_TRANSACTION
INSERT INTO esc_payment_transaction (
    txn_id, job_no, payment_method, amount, txn_date, status, reference_no
) VALUES (
    esc_payment_txn_seq.NEXTVAL, 'JOB001', 'CABC', 25.50, SYSDATE - 1, 'SUCCESS', 'TXN001'
);

-- Connect as CN2_RECON to insert reconciliation data
CONNECT CN2_RECON/cn2recon123@XE;

-- Insert mock data into ESC_RECON_SETL_NETSNPX_STG
INSERT INTO esc_recon_setl_netsnpx_stg (
    id, file_id, financial_institution_id, txn_amount, record_type,
    product, transaction_cd, transaction_dt, orig_tid, orig_timestamp, orig_seq_no
) VALUES (
    esc_recon_stg_seq.NEXTVAL, 'NPX20250615001', 'DBS', 25.50, 'TXN',
    'CABC', '00', SYSDATE - 1, 'TID001', '20250614120000', '123456'
);

-- Insert mock data into ESC_RECON_SETL_NETSNPX (missing from staging)
INSERT INTO esc_recon_setl_netsnpx (
    id, file_id, financial_institution_id, txn_amount, record_type,
    product, transaction_cd, transaction_dt, orig_tid, orig_timestamp, orig_seq_no,
    recon_date, recon_status
) VALUES (
    esc_recon_setl_seq.NEXTVAL, 'NPX20250615002', 'UOB', 45.80, 'TXN',
    'EVCH', '00', SYSDATE - 2, 'TID002', '20250613130000', '789012',
    SYSDATE, 'MATCHED'
);

-- Connect as PAY_AS_DATA to insert ISO data
CONNECT PAY_AS_DATA/payasdata123@XE;

-- Insert mock data into ISO8583_INCOMING
INSERT INTO iso8583_incoming (
    msg_id, msg_typ_id, pan, txn_amt, txn_date, tripinformation, expiry_date
) VALUES (
    iso8583_seq.NEXTVAL, '0200', '1234****5678', '002550', SYSDATE - 1,
    'TRIP001|JOB001|CDG|1.3048|103.8318|1.2966|103.8520|COMPLETED|CABC|25.50|SGD|APP001|TID001|123456', '2512'
);

-- Connect as DCP_USER to insert wallet data
CONNECT DCP_USER/ociPro$gDCP_255@XE;

-- Insert mock data into PYTB_WD_TXN
INSERT INTO pytb_wd_txn (
    wd_txn_id, booking_ref, txn_type, amount, txn_date, status, merchant_id, terminal_id
) VALUES (
    pytb_wd_txn_seq.NEXTVAL, 'BK001', 'PAYMENT', 25.50, SYSDATE - 1, 'SUCCESS', 'CDG001', 'TID001'
);

-- Connect as CN2TMSYS to insert TM data
CONNECT CN2TMSYS/cn2tmsys123@XE;

-- Insert mock data into TMTB_PL_TXN_LOG
INSERT INTO tmtb_pl_txn_log (
    id, job_number, booking_num, msg_type, txn_amount, status, response_code
) VALUES (
    tmtb_pl_txn_log_seq.NEXTVAL, 'JOB001', 'BK001', 'PAYMENT_REQUEST', 25.50, 'SUCCESS', '00'
);

-- Insert mock data into TMTB_PL_PAYLAH_TXN_LOG
INSERT INTO tmtb_pl_paylah_txn_log (
    txn_ref_id, pl_txn_log_id, txn_amount, status, merchant_ref
) VALUES (
    'PL001', tmtb_pl_txn_log_seq.CURRVAL, 25.50, 'SUCCESS', 'CDG_BK001'
);

-- Connect as CITYNET2 to insert CityNet2 data
CONNECT CITYNET2/citynet2123@XE;

-- Insert mock data into CITYNET2.ESC_PAYMENT_TRANSACTION
INSERT INTO esc_payment_transaction (
    txn_id, job_no, payment_method, amount, status, reference_no, citynet_ref
) VALUES (
    citynet2_payment_seq.NEXTVAL, 'JOB001', 'CABC', 25.50, 'SUCCESS', 'TXN001', 'CN2_TXN001'
);

-- Insert mock data into CITYNET2.ESC_TRIP_DETAILS
INSERT INTO esc_trip_details (
    id, job_no, end_dt, amount_paid, start_y, start_x, end_y, end_x,
    platform_fee, valid_trip, distance, payment_method
) VALUES (
    citynet2_trip_seq.NEXTVAL, 'JOB001', SYSDATE - 1 + 0.5/24, 25.50,
    1.3048, 103.8318, 1.2966, 103.8520, 2.50, 'Y', 12.5, 'CABC'
);

COMMIT;
