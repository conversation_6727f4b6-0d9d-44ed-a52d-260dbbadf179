-- CDG Database Users Creation Script for gvenzl/oracle-xe
-- This script creates all users for the CDG application

-- Create application users
CREATE USER RECUSER IDENTIFIED BY "ociPro$reC_450";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO RECUSER;
<PERSON><PERSON><PERSON> UNLIMITED TABLESPACE TO RECUSER;

CREATE USER IBSUSER IDENTIFIED BY "ociPro$ibU_416";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO IBSUSER;
GRANT UNLIMITED TABLESPACE TO IBSUSER;

CREATE USER CN2USER IDENTIFIED BY "ociPro$dCN2_112";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO CN2USER;
GRANT UNLIMITED TABLESPACE TO CN2USER;

CREATE USER DCP_USER IDENTIFIED BY "ociPro$gDCP_255";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO DCP_USER;
<PERSON><PERSON><PERSON> UNLIMITED TABLESPACE TO DCP_USER;

CREATE USER CN2TMUSER IDENTIFIED BY "ociPro$iCN2_141";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO CN2TMUSER;
GRANT UNLIMITED TABLESPACE TO CN2TMUSER;

CREATE USER PAY_AS_USER IDENTIFIED BY "ociPro$cPAY_248";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO PAY_AS_USER;
GRANT UNLIMITED TABLESPACE TO PAY_AS_USER;

-- Create schema users
CREATE USER RECSYS IDENTIFIED BY "recsys123";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO RECSYS;
GRANT UNLIMITED TABLESPACE TO RECSYS;

CREATE USER CN2_RECON IDENTIFIED BY "cn2recon123";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO CN2_RECON;
GRANT UNLIMITED TABLESPACE TO CN2_RECON;

CREATE USER CN2TMSYS IDENTIFIED BY "cn2tmsys123";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO CN2TMSYS;
GRANT UNLIMITED TABLESPACE TO CN2TMSYS;

CREATE USER PAY_AS_DATA IDENTIFIED BY "payasdata123";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO PAY_AS_DATA;
GRANT UNLIMITED TABLESPACE TO PAY_AS_DATA;

CREATE USER CITYNET2 IDENTIFIED BY "citynet2123";
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO CITYNET2;
GRANT UNLIMITED TABLESPACE TO CITYNET2;

COMMIT;
