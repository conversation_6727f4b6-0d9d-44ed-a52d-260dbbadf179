-- CDG Database Schema Creation Script
-- This script creates all schemas and users for the CDG application

-- Connect as SYSTEM
CONNECT system/OraclePassword123@XE;

-- Create tablespaces
CREATE TABLESPACE cdg_data
DATAFILE '/opt/oracle/oradata/XE/cdg_data01.dbf' SIZE 100M AUTOEXTEND ON;

CREATE TABLESPACE cdg_index
DATAFILE '/opt/oracle/oradata/XE/cdg_index01.dbf' SIZE 50M AUTOEXTEND ON;

-- Create schemas/users
-- REC<PERSON><PERSON> (Primary user for PTFSPRO)
CREATE USER RECUSER IDENTIFIED BY "ociPro$reC_450"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO RECUSER;
GRANT UNLIMITED TABLESPACE TO RECUSER;

-- I<PERSON><PERSON><PERSON> (IBS user for PTFSPRO)
CREATE USER IBSUSER IDENTIFIED BY "ociPro$ibU_416"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO IBSUSER;
GRANT UNLIMITED TABLESPACE TO IBSUSER;

-- CN2USER (Main user for PTCCPRO)
CREATE USER CN2USER IDENTIFIED BY "ociPro$dCN2_112"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO CN2USER;
GRANT UNLIMITED TABLESPACE TO CN2USER;

-- DCP_USER (DCP user for PTCCPRO)
CREATE USER DCP_USER IDENTIFIED BY "ociPro$gDCP_255"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO DCP_USER;
GRANT UNLIMITED TABLESPACE TO DCP_USER;

-- CN2TMUSER (TM user for PTCCPRO)
CREATE USER CN2TMUSER IDENTIFIED BY "ociPro$iCN2_141"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO CN2TMUSER;
GRANT UNLIMITED TABLESPACE TO CN2TMUSER;

-- PAY_AS_USER (Payment user for PTCCPRO)
CREATE USER PAY_AS_USER IDENTIFIED BY "ociPro$cPAY_248"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO PAY_AS_USER;
GRANT UNLIMITED TABLESPACE TO PAY_AS_USER;

-- Create schema users for different schemas
-- RECSYS schema
CREATE USER RECSYS IDENTIFIED BY "recsys123"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO RECSYS;
GRANT UNLIMITED TABLESPACE TO RECSYS;

-- CN2_RECON schema
CREATE USER CN2_RECON IDENTIFIED BY "cn2recon123"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO CN2_RECON;
GRANT UNLIMITED TABLESPACE TO CN2_RECON;

-- CN2TMSYS schema
CREATE USER CN2TMSYS IDENTIFIED BY "cn2tmsys123"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO CN2TMSYS;
GRANT UNLIMITED TABLESPACE TO CN2TMSYS;

-- PAY_AS_DATA schema
CREATE USER PAY_AS_DATA IDENTIFIED BY "payasdata123"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO PAY_AS_DATA;
GRANT UNLIMITED TABLESPACE TO PAY_AS_DATA;

-- CITYNET2 schema
CREATE USER CITYNET2 IDENTIFIED BY "citynet2123"
DEFAULT TABLESPACE cdg_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SYNONYM TO CITYNET2;
GRANT UNLIMITED TABLESPACE TO CITYNET2;

-- Grant cross-schema access
-- Allow main users to access schema tables
GRANT SELECT, INSERT, UPDATE, DELETE ON RECSYS.rctb_trip_intf TO RECUSER;
GRANT SELECT, INSERT, UPDATE, DELETE ON CN2_RECON.esc_recon_setl_netsnpx_stg TO CN2USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON CN2_RECON.esc_recon_setl_netsnpx TO CN2USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PAY_AS_DATA.iso8583_incoming TO PAY_AS_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON CN2TMSYS.tmtb_pl_txn_log TO CN2TMUSER;
GRANT SELECT, INSERT, UPDATE, DELETE ON CN2TMSYS.tmtb_pl_paylah_txn_log TO CN2TMUSER;

-- Create synonyms for easier access
CREATE OR REPLACE PUBLIC SYNONYM rctb_trip_intf FOR RECSYS.rctb_trip_intf;
CREATE OR REPLACE PUBLIC SYNONYM esc_recon_setl_netsnpx_stg FOR CN2_RECON.esc_recon_setl_netsnpx_stg;
CREATE OR REPLACE PUBLIC SYNONYM esc_recon_setl_netsnpx FOR CN2_RECON.esc_recon_setl_netsnpx;
CREATE OR REPLACE PUBLIC SYNONYM iso8583_incoming FOR PAY_AS_DATA.iso8583_incoming;

COMMIT;
