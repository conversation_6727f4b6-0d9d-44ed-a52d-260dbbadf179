-- CDG Database Schema-specific Tables Creation Script

-- Create RECSYS tables
CREATE TABLE RECSYS.rctb_trip_intf (
    trip_intf_pk NUMBER(19) PRIMARY KEY,
    job_no VARCHAR2(50),
    vehicle_no VARCHAR2(20),
    driver_ic VARCHAR2(20),
    entity VARCHAR2(10),
    booked_channel VARCHAR2(20),
    booked_date_time DATE,
    booked_vehicle_group VARCHAR2(20),
    pax_name VARCHAR2(100),
    booking_reference VARCHAR2(50),
    trip_start DATE,
    trip_end DATE,
    pickup VARCHAR2(200),
    destination VARCHAR2(200),
    job_type VARCHAR2(20),
    job_status VARCHAR2(20),
    pax_privilege VARCHAR2(20),
    product_id VARCHAR2(20),
    product VARCHAR2(50),
    payment_mode VARCHAR2(20),
    account_lv1 VARCHAR2(50),
    account_lv2 VARCHAR2(50),
    card_no VARCHAR2(50),
    approval_code VARCHAR2(20),
    gst_inclusive VARCHAR2(1),
    txn_amount NUMBER(10,2),
    booking_fee NUMBER(10,2),
    admin_amount NUMBER(10,2),
    gst_amount NUMBER(10,2),
    total_amount NUMBER(10,2),
    driver_levy NUMBER(10,2),
    merit_point_fee NUMBER(10,2),
    surcharge_description VARCHAR2(100),
    charged_to VARCHAR2(50),
    complimentary VARCHAR2(1),
    status VARCHAR2(10),
    error_code VARCHAR2(20),
    fms_status VARCHAR2(10),
    fms_date DATE,
    completed_dt DATE,
    recon_date DATE,
    ibs_status VARCHAR2(10),
    ibs_date DATE,
    flowthru_action VARCHAR2(20),
    offline_flag VARCHAR2(1),
    valid_trip VARCHAR2(1),
    create_date DATE DEFAULT SYSDATE
);

CREATE SEQUENCE RECSYS.rctb_trip_intf_seq START WITH 1 INCREMENT BY 1;

-- Create CN2_RECON tables
CREATE TABLE CN2_RECON.esc_recon_setl_netsnpx_stg (
    id NUMBER(19) PRIMARY KEY,
    file_id VARCHAR2(50),
    financial_institution_id VARCHAR2(20),
    txn_amount NUMBER(15,2),
    created_dt DATE DEFAULT SYSDATE,
    record_type VARCHAR2(10),
    product VARCHAR2(20),
    transaction_cd VARCHAR2(10),
    transaction_dt DATE,
    corporation_id VARCHAR2(20),
    entity_02_id VARCHAR2(20),
    entity_03_id VARCHAR2(20),
    entity_04_id VARCHAR2(20),
    entity_05_id VARCHAR2(20),
    amount_01_no NUMBER(15,2),
    amount_02_no NUMBER(15,2),
    amount_03_no NUMBER(15,2),
    amount_04_no NUMBER(15,2),
    amount_05_no NUMBER(15,2),
    fee_01_no NUMBER(15,2),
    fee_02_no NUMBER(15,2),
    fee_03_no NUMBER(15,2),
    fee_04_no NUMBER(15,2),
    fee_05_no NUMBER(15,2),
    card_issuer_id VARCHAR2(20),
    reversal_cd VARCHAR2(10),
    response_cd VARCHAR2(10),
    etc_01_tx VARCHAR2(100),
    orig_tid VARCHAR2(20),
    orig_timestamp VARCHAR2(20),
    orig_seq_no VARCHAR2(20),
    etc_05_tx VARCHAR2(100),
    can_no VARCHAR2(50),
    sequence_no VARCHAR2(20),
    reference_tx VARCHAR2(100),
    void_fl VARCHAR2(1)
);

CREATE SEQUENCE CN2_RECON.esc_recon_stg_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE CN2_RECON.esc_recon_setl_netsnpx (
    id NUMBER(19) PRIMARY KEY,
    file_id VARCHAR2(50),
    financial_institution_id VARCHAR2(20),
    txn_amount NUMBER(15,2),
    created_dt DATE DEFAULT SYSDATE,
    record_type VARCHAR2(10),
    product VARCHAR2(20),
    transaction_cd VARCHAR2(10),
    transaction_dt DATE,
    corporation_id VARCHAR2(20),
    entity_02_id VARCHAR2(20),
    entity_03_id VARCHAR2(20),
    entity_04_id VARCHAR2(20),
    entity_05_id VARCHAR2(20),
    amount_01_no NUMBER(15,2),
    amount_02_no NUMBER(15,2),
    amount_03_no NUMBER(15,2),
    amount_04_no NUMBER(15,2),
    amount_05_no NUMBER(15,2),
    fee_01_no NUMBER(15,2),
    fee_02_no NUMBER(15,2),
    fee_03_no NUMBER(15,2),
    fee_04_no NUMBER(15,2),
    fee_05_no NUMBER(15,2),
    card_issuer_id VARCHAR2(20),
    reversal_cd VARCHAR2(10),
    response_cd VARCHAR2(10),
    etc_01_tx VARCHAR2(100),
    orig_tid VARCHAR2(20),
    orig_timestamp VARCHAR2(20),
    orig_seq_no VARCHAR2(20),
    etc_05_tx VARCHAR2(100),
    can_no VARCHAR2(50),
    sequence_no VARCHAR2(20),
    reference_tx VARCHAR2(100),
    void_fl VARCHAR2(1),
    recon_date DATE,
    recon_status VARCHAR2(20)
);

CREATE SEQUENCE CN2_RECON.esc_recon_setl_seq START WITH 1 INCREMENT BY 1;

COMMIT;
