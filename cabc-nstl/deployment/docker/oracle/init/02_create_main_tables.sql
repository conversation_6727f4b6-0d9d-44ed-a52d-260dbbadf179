-- CDG Database Main Tables Creation Script
-- This script creates main tables in RECUSER schema

-- Create main tables in RECUSER schema
CREATE TABLE RECUSER.esc_job (
    job_no VARCHAR2(50) PRIMARY KEY,
    booking_id VARCHAR2(50),
    job_type VARCHAR2(20),
    status VARCHAR2(20),
    dcp_request_id VARCHAR2(50),
    link_job_no_cn3 VARCHAR2(50),
    link_job_no VARCHAR2(50),
    replaced_job_no VARCHAR2(50),
    pax_contact VARCHAR2(20),
    pax_name VARCHAR2(100),
    cc_number VARCHAR2(50),
    created_dt DATE DEFAULT SYSDATE,
    updated_dt DATE DEFAULT SYSDATE
);

CREATE TABLE RECUSER.esc_trip_details (
    id NUMBER(19) PRIMARY KEY,
    job_no VARCHAR2(50),
    end_dt DATE,
    amount_paid NUMBER(10,2),
    start_y NUMBER(15,6),
    start_x NUMBER(15,6),
    end_y NUMBER(15,6),
    end_x NUMBER(15,6),
    platform_fee NUMBER(10,2),
    valid_trip VARCHAR2(1),
    distance NUMBER(10,2),
    created_dt DATE DEFAULT SYSDATE,
    recon_sync_dt DATE,
    payment_method VARCHAR2(20)
);

CREATE SEQUENCE RECUSER.esc_trip_details_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE RECUSER.esc_booking (
    booking_id VARCHAR2(50) PRIMARY KEY,
    booking_reference VARCHAR2(50),
    pax_name VARCHAR2(100),
    pax_contact VARCHAR2(20),
    pickup_address VARCHAR2(200),
    destination_address VARCHAR2(200),
    booking_date DATE DEFAULT SYSDATE,
    status VARCHAR2(20)
);

CREATE TABLE RECUSER.esc_payment_transaction (
    txn_id NUMBER(19) PRIMARY KEY,
    job_no VARCHAR2(50),
    payment_method VARCHAR2(20),
    amount NUMBER(10,2),
    currency VARCHAR2(3) DEFAULT 'SGD',
    txn_date DATE DEFAULT SYSDATE,
    status VARCHAR2(20),
    reference_no VARCHAR2(50)
);

CREATE SEQUENCE RECUSER.esc_payment_txn_seq START WITH 1 INCREMENT BY 1;

-- Create RCTB_SETL table in RECUSER schema
CREATE TABLE RECUSER.rctb_setl (
    setl_pk NUMBER(19) PRIMARY KEY,
    job_no VARCHAR2(50),
    entity VARCHAR2(10),
    txn_date DATE,
    card_no VARCHAR2(50),
    approval_code VARCHAR2(20),
    txn_amount NUMBER(10,2),
    tid VARCHAR2(20),
    stan VARCHAR2(10),
    card_type VARCHAR2(5),
    file_id VARCHAR2(50),
    create_date DATE DEFAULT SYSDATE
);

CREATE SEQUENCE RECUSER.rctb_setl_seq START WITH 1 INCREMENT BY 1;

COMMIT;
