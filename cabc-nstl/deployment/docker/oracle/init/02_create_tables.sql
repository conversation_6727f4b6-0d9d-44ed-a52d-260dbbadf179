-- CDG Database Tables Creation Script
-- This script creates all tables for the CDG application

-- Connect as RECSYS to create RCTB tables
CONNECT RECSYS/recsys123@XE;

-- Create RCTB_TRIP_INTF table
CREATE TABLE rctb_trip_intf (
    trip_intf_pk NUMBER(19) PRIMARY KEY,
    job_no VARCHAR2(50),
    vehicle_no VARCHAR2(20),
    driver_ic VARCHAR2(20),
    entity VARCHAR2(10),
    booked_channel VARCHAR2(20),
    booked_date_time DATE,
    booked_vehicle_group VARCHAR2(20),
    pax_name VARCHAR2(100),
    booking_reference VARCHAR2(50),
    trip_start DATE,
    trip_end DATE,
    pickup VARCHAR2(200),
    destination VARCHAR2(200),
    job_type VARCHAR2(20),
    job_status VARCHAR2(20),
    pax_privilege VARCHAR2(20),
    product_id VARCHAR2(20),
    product VARCHAR2(50),
    payment_mode VARCHAR2(20),
    account_lv1 VARCHAR2(50),
    account_lv2 VARCHAR2(50),
    card_no VARCHAR2(50),
    approval_code VARCHAR2(20),
    gst_inclusive VARCHAR2(1),
    txn_amount NUMBER(10,2),
    booking_fee NUMBER(10,2),
    admin_amount NUMBER(10,2),
    gst_amount NUMBER(10,2),
    total_amount NUMBER(10,2),
    driver_levy NUMBER(10,2),
    merit_point_fee NUMBER(10,2),
    surcharge_description VARCHAR2(100),
    charged_to VARCHAR2(50),
    complimentary VARCHAR2(1),
    status VARCHAR2(10),
    error_code VARCHAR2(20),
    fms_status VARCHAR2(10),
    fms_date DATE,
    completed_dt DATE,
    recon_date DATE,
    ibs_status VARCHAR2(10),
    ibs_date DATE,
    flowthru_action VARCHAR2(20),
    offline_flag VARCHAR2(1),
    valid_trip VARCHAR2(1),
    create_date DATE DEFAULT SYSDATE
);

-- Create sequence for trip_intf_pk
CREATE SEQUENCE rctb_trip_intf_seq START WITH 1 INCREMENT BY 1;

-- Create RCTB_SETL table
CREATE TABLE rctb_setl (
    setl_pk NUMBER(19) PRIMARY KEY,
    job_no VARCHAR2(50),
    entity VARCHAR2(10),
    txn_date DATE,
    card_no VARCHAR2(50),
    approval_code VARCHAR2(20),
    txn_amount NUMBER(10,2),
    tid VARCHAR2(20),
    stan VARCHAR2(10),
    card_type VARCHAR2(5),
    file_id VARCHAR2(50),
    create_date DATE DEFAULT SYSDATE
);

-- Create sequence for setl_pk
CREATE SEQUENCE rctb_setl_seq START WITH 1 INCREMENT BY 1;

-- Connect as RECUSER to create ESC tables
CONNECT RECUSER/ociPro$reC_450@XE;

-- Create ESC_JOB table
CREATE TABLE esc_job (
    job_no VARCHAR2(50) PRIMARY KEY,
    booking_id VARCHAR2(50),
    job_type VARCHAR2(20),
    status VARCHAR2(20),
    dcp_request_id VARCHAR2(50),
    link_job_no_cn3 VARCHAR2(50),
    link_job_no VARCHAR2(50),
    replaced_job_no VARCHAR2(50),
    pax_contact VARCHAR2(20),
    pax_name VARCHAR2(100),
    cc_number VARCHAR2(50),
    created_dt DATE DEFAULT SYSDATE,
    updated_dt DATE DEFAULT SYSDATE
);

-- Create ESC_TRIP_DETAILS table
CREATE TABLE esc_trip_details (
    id NUMBER(19) PRIMARY KEY,
    job_no VARCHAR2(50),
    end_dt DATE,
    amount_paid NUMBER(10,2),
    start_y NUMBER(15,6),
    start_x NUMBER(15,6),
    end_y NUMBER(15,6),
    end_x NUMBER(15,6),
    platform_fee NUMBER(10,2),
    valid_trip VARCHAR2(1),
    distance NUMBER(10,2),
    created_dt DATE DEFAULT SYSDATE,
    recon_sync_dt DATE,
    payment_method VARCHAR2(20)
);

-- Create sequence for esc_trip_details
CREATE SEQUENCE esc_trip_details_seq START WITH 1 INCREMENT BY 1;

-- Create ESC_BOOKING table
CREATE TABLE esc_booking (
    booking_id VARCHAR2(50) PRIMARY KEY,
    booking_reference VARCHAR2(50),
    pax_name VARCHAR2(100),
    pax_contact VARCHAR2(20),
    pickup_address VARCHAR2(200),
    destination_address VARCHAR2(200),
    booking_date DATE DEFAULT SYSDATE,
    status VARCHAR2(20)
);

-- Create ESC_PAYMENT_TRANSACTION table
CREATE TABLE esc_payment_transaction (
    txn_id NUMBER(19) PRIMARY KEY,
    job_no VARCHAR2(50),
    payment_method VARCHAR2(20),
    amount NUMBER(10,2),
    currency VARCHAR2(3) DEFAULT 'SGD',
    txn_date DATE DEFAULT SYSDATE,
    status VARCHAR2(20),
    reference_no VARCHAR2(50)
);

-- Create sequence for esc_payment_transaction
CREATE SEQUENCE esc_payment_txn_seq START WITH 1 INCREMENT BY 1;
