-- CDG Database Grants and Synonyms Creation Script

-- <PERSON> cross-schema access
GRANT SELECT, INSERT, UPDATE, DELETE ON RECSYS.rctb_trip_intf TO RECUSER;
GRANT SELECT, INSERT, UPDATE, DELETE ON CN2_RECON.esc_recon_setl_netsnpx_stg TO CN2USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON CN2_RECON.esc_recon_setl_netsnpx TO CN2USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PAY_AS_DATA.iso8583_incoming TO PAY_AS_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON CN2TMSYS.tmtb_pl_txn_log TO CN2TMUSER;
GRANT SELECT, INSERT, UPDATE, DELETE ON CN2TMSYS.tmtb_pl_paylah_txn_log TO CN2TMUSER;

-- Grant access to sequences
GRANT SELECT ON RECSYS.rctb_trip_intf_seq TO RECUSER;
GRANT SELECT ON CN2_RECON.esc_recon_stg_seq TO CN2USER;
GRANT SELECT ON CN2_RECON.esc_recon_setl_seq TO CN2USER;
GRANT SELECT ON PAY_AS_DATA.iso8583_seq TO PAY_AS_USER;
GRANT SELECT ON DCP_USER.pytb_wd_txn_seq TO DCP_USER;
GRANT SELECT ON CN2TMSYS.tmtb_pl_txn_log_seq TO CN2TMUSER;

-- Create public synonyms for easier access
CREATE OR REPLACE PUBLIC SYNONYM rctb_trip_intf FOR RECSYS.rctb_trip_intf;
CREATE OR REPLACE PUBLIC SYNONYM esc_recon_setl_netsnpx_stg FOR CN2_RECON.esc_recon_setl_netsnpx_stg;
CREATE OR REPLACE PUBLIC SYNONYM esc_recon_setl_netsnpx FOR CN2_RECON.esc_recon_setl_netsnpx;
CREATE OR REPLACE PUBLIC SYNONYM iso8583_incoming FOR PAY_AS_DATA.iso8583_incoming;
CREATE OR REPLACE PUBLIC SYNONYM pytb_wd_txn FOR DCP_USER.pytb_wd_txn;
CREATE OR REPLACE PUBLIC SYNONYM tmtb_pl_txn_log FOR CN2TMSYS.tmtb_pl_txn_log;
CREATE OR REPLACE PUBLIC SYNONYM tmtb_pl_paylah_txn_log FOR CN2TMSYS.tmtb_pl_paylah_txn_log;

-- Grant access to all users for main tables
GRANT SELECT, INSERT, UPDATE, DELETE ON RECUSER.esc_job TO CN2USER, DCP_USER, CN2TMUSER, PAY_AS_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON RECUSER.esc_trip_details TO CN2USER, DCP_USER, CN2TMUSER, PAY_AS_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON RECUSER.esc_booking TO CN2USER, DCP_USER, CN2TMUSER, PAY_AS_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON RECUSER.esc_payment_transaction TO CN2USER, DCP_USER, CN2TMUSER, PAY_AS_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON RECUSER.rctb_setl TO CN2USER, DCP_USER, CN2TMUSER, PAY_AS_USER;

COMMIT;
