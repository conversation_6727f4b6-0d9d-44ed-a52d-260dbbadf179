-- CDG Database Additional Schema Tables Creation Script

-- Create PAY_AS_DATA tables
CREATE TABLE PAY_AS_DATA.iso8583_incoming (
    msg_id NUMBER(19) PRIMARY KEY,
    msg_typ_id VARCHAR2(10),
    pan VARCHAR2(50),
    txn_amt VARCHAR2(20),
    txn_date DATE,
    tripinformation VARCHAR2(500),
    expiry_date VARCHAR2(10),
    track_2_data VARCHAR2(200)
);

CREATE SEQUENCE PAY_AS_DATA.iso8583_seq START WITH 1 INCREMENT BY 1;

-- Create DCP_USER tables
CREATE TABLE DCP_USER.pytb_wd_txn (
    wd_txn_id NUMBER(19) PRIMARY KEY,
    booking_ref VARCHAR2(50),
    parent_txn_id VARCHAR2(50),
    txn_type VARCHAR2(20),
    amount NUMBER(15,2),
    currency VARCHAR2(3) DEFAULT 'SGD',
    txn_date DATE DEFAULT SYSDATE,
    status VARCHAR2(20),
    merchant_id VARCHAR2(50),
    terminal_id VARCHAR2(20)
);

CREATE SEQUENCE DCP_USER.pytb_wd_txn_seq START WITH 1 INCREMENT BY 1;

-- Create CN2TMSYS tables
CREATE TABLE CN2TMSYS.tmtb_pl_txn_log (
    id NUMBER(19) PRIMARY KEY,
    job_number VARCHAR2(50),
    booking_num VARCHAR2(50),
    msg_type VARCHAR2(50),
    txn_amount NUMBER(15,2),
    currency VARCHAR2(3) DEFAULT 'SGD',
    txn_date DATE DEFAULT SYSDATE,
    status VARCHAR2(20),
    response_code VARCHAR2(10),
    created_dt DATE DEFAULT SYSDATE
);

CREATE SEQUENCE CN2TMSYS.tmtb_pl_txn_log_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE CN2TMSYS.tmtb_pl_paylah_txn_log (
    txn_ref_id VARCHAR2(50) PRIMARY KEY,
    pl_txn_log_id NUMBER(19),
    txn_amount NUMBER(15,2),
    currency VARCHAR2(3) DEFAULT 'SGD',
    txn_date DATE DEFAULT SYSDATE,
    status VARCHAR2(20),
    merchant_ref VARCHAR2(50),
    created_dt DATE DEFAULT SYSDATE,
    CONSTRAINT fk_pl_txn_log FOREIGN KEY (pl_txn_log_id) REFERENCES CN2TMSYS.tmtb_pl_txn_log(id)
);

-- Create CITYNET2 tables
CREATE TABLE CITYNET2.esc_payment_transaction (
    txn_id NUMBER(19) PRIMARY KEY,
    job_no VARCHAR2(50),
    payment_method VARCHAR2(20),
    amount NUMBER(10,2),
    currency VARCHAR2(3) DEFAULT 'SGD',
    txn_date DATE DEFAULT SYSDATE,
    status VARCHAR2(20),
    reference_no VARCHAR2(50),
    citynet_ref VARCHAR2(50)
);

CREATE SEQUENCE CITYNET2.citynet2_payment_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE CITYNET2.esc_trip_details (
    id NUMBER(19) PRIMARY KEY,
    job_no VARCHAR2(50),
    end_dt DATE,
    amount_paid NUMBER(10,2),
    start_y NUMBER(15,6),
    start_x NUMBER(15,6),
    end_y NUMBER(15,6),
    end_x NUMBER(15,6),
    platform_fee NUMBER(10,2),
    valid_trip VARCHAR2(1),
    distance NUMBER(10,2),
    created_dt DATE DEFAULT SYSDATE,
    recon_sync_dt DATE,
    payment_method VARCHAR2(20),
    citynet_version VARCHAR2(10) DEFAULT 'CN2'
);

CREATE SEQUENCE CITYNET2.citynet2_trip_seq START WITH 1 INCREMENT BY 1;

COMMIT;
