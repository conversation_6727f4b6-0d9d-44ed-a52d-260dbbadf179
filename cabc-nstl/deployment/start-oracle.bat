@echo off
echo Starting CDG Oracle Database with gvenzl/oracle-xe...
echo.

cd /d "%~dp0"

echo Pulling gvenzl Oracle XE image...
docker-compose pull oracle-db

echo.
echo Starting Oracle Database container...
docker-compose up -d oracle-db

echo.
echo Waiting for Oracle Database to be ready...
echo This may take 1-2 minutes for first startup...

:wait_loop
docker-compose exec oracle-db sqlplus -L system/OraclePassword123@//localhost:1521/XE @/container-entrypoint-initdb.d/99_healthcheck.sql >nul 2>&1
if %errorlevel% equ 0 (
    echo.
    echo ✅ Oracle Database is ready!
    goto :database_ready
)
echo Waiting for database... (checking every 10 seconds)
timeout /t 10 /nobreak >nul
goto :wait_loop

:database_ready
echo.
echo Starting Adminer (Database Web UI)...
docker-compose up -d adminer

echo.
echo 🎉 CDG Oracle Database Setup Complete!
echo.
echo 📊 Database Connection Details:
echo    Host: localhost
echo    Port: 1521
echo    SID: XE
echo    System User: system / OraclePassword123
echo.
echo 👥 Application Users Created:
echo    RECUSER / ociPro$reC_450
echo    IBSUSER / ociPro$ibU_416
echo    CN2USER / ociPro$dCN2_112
echo    DCP_USER / ociPro$gDCP_255
echo    CN2TMUSER / ociPro$iCN2_141
echo    PAY_AS_USER / ociPro$cPAY_248
echo.
echo 🌐 Web Interfaces:
echo    Adminer (DB Admin): http://localhost:8081
echo.
echo 📋 Schemas Created:
echo    RECSYS, CN2_RECON, CN2TMSYS, PAY_AS_DATA, CITYNET2
echo.
echo 🗃️ Tables Created with Mock Data:
echo    ✅ rctb_trip_intf (RECSYS schema)
echo    ✅ esc_job, esc_trip_details (RECUSER schema)
echo    ✅ esc_recon_setl_netsnpx_stg, esc_recon_setl_netsnpx (CN2_RECON schema)
echo    ✅ iso8583_incoming (PAY_AS_DATA schema)
echo    ✅ pytb_wd_txn (DCP_USER schema)
echo    ✅ tmtb_pl_txn_log, tmtb_pl_paylah_txn_log (CN2TMSYS schema)
echo    ✅ And more...
echo.
echo 🚀 Ready to start your Spring Boot application!
echo.
pause
