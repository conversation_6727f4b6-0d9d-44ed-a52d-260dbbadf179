# Docker Oracle Database Setup for Local Development

This guide helps you set up a local Oracle database using Docker for testing and development.

## 🐳 Quick Start

### 1. Start Oracle Database
```bash
# Start the Oracle database container
docker-compose -f docker-compose-oracle.yml up -d

# Check if the container is running
docker ps

# View logs to ensure successful startup
docker-compose -f docker-compose-oracle.yml logs -f oracle-db
```

### 2. Wait for Database Initialization
The Oracle database takes 2-3 minutes to fully initialize. Wait for this message:
```
DATABASE IS READY TO USE!
```

### 3. Run Application with Test Profile
```bash
# Using Gradle with test profile
./gradlew bootRun --args='--spring.profiles.active=test'

# Or using IntelliJ IDEA:
# 1. Go to Run Configuration
# 2. Add VM options: -Dspring.profiles.active=test
# 3. Or add Program arguments: --spring.profiles.active=test
```

## 📋 Available Profiles

### Test Profile (`application-test.properties`)
- **Purpose**: Local testing with Docker Oracle
- **Features**: Verbose logging, SQL debugging, smaller connection pool
- **Usage**: `--spring.profiles.active=test`

### Development Profile (`application-dev.properties`)
- **Purpose**: Local development with Docker Oracle
- **Features**: Balanced logging, optimized for development workflow
- **Usage**: `--spring.profiles.active=dev`

### Default Profile (`application.properties`)
- **Purpose**: Production-like configuration
- **Features**: Minimal logging, optimized performance

## 🔧 Database Connection Details

| Setting | Value |
|---------|-------|
| **Host** | localhost |
| **Port** | 1521 |
| **SID** | XE |
| **Username** | RECUSER |
| **Password** | ociPro$reC_450 |
| **JDBC URL** | `***********************************` |

## 🛠️ Useful Commands

### Database Management
```bash
# Stop the database
docker-compose -f docker-compose-oracle.yml down

# Stop and remove all data (fresh start)
docker-compose -f docker-compose-oracle.yml down -v

# Connect to Oracle SQL*Plus
docker exec -it oracle-test-db sqlplus RECUSER/ociPro$reC_450@XE

# View database logs
docker-compose -f docker-compose-oracle.yml logs oracle-db
```

### Application Testing
```bash
# Test with different profiles
./gradlew bootRun --args='--spring.profiles.active=test'
./gradlew bootRun --args='--spring.profiles.active=dev'
./gradlew bootRun  # Uses default profile

# Test database connection only
./gradlew test --tests="*DatabaseConnectionTest*"
```

## 🔍 Troubleshooting

### Database Won't Start
1. Check if port 1521 is already in use:
   ```bash
   netstat -an | findstr 1521
   ```
2. Stop any existing Oracle processes
3. Remove existing containers:
   ```bash
   docker rm -f oracle-test-db
   ```

### Connection Refused
1. Wait for database initialization (2-3 minutes)
2. Check container status:
   ```bash
   docker ps
   docker logs oracle-test-db
   ```
3. Verify port mapping:
   ```bash
   docker port oracle-test-db
   ```

### Application Errors
1. Check if using correct profile:
   ```bash
   # Should show: Active profiles: test
   ./gradlew bootRun --args='--spring.profiles.active=test'
   ```
2. Verify database tables exist
3. Check application logs for specific errors

## 📁 Directory Structure
```
cabc-nstl/
├── src/main/resources/
│   ├── application.properties          # Default (production-like)
│   ├── application-test.properties     # Test profile
│   └── application-dev.properties      # Development profile
├── docker-compose-oracle.yml          # Oracle database setup
└── README-Docker-Setup.md            # This file
```

## 🎯 IntelliJ IDEA Setup

### Method 1: Run Configuration
1. Open **Run/Debug Configurations**
2. Select your Spring Boot configuration
3. In **Program arguments**, add: `--spring.profiles.active=test`
4. Click **Apply** and **OK**

### Method 2: VM Options
1. Open **Run/Debug Configurations**
2. In **VM options**, add: `-Dspring.profiles.active=test`
3. Click **Apply** and **OK**

### Method 3: Environment Variable
1. Open **Run/Debug Configurations**
2. In **Environment variables**, add:
   - Name: `SPRING_PROFILES_ACTIVE`
   - Value: `test`
3. Click **Apply** and **OK**

## 🚀 Next Steps

1. **Start Docker Oracle**: `docker-compose -f docker-compose-oracle.yml up -d`
2. **Wait for initialization**: Check logs until "DATABASE IS READY"
3. **Run with test profile**: Use IntelliJ with `--spring.profiles.active=test`
4. **Verify connection**: Look for "✅ Database connection successful!" in logs

Your application will now connect to the local Docker Oracle database with optimized settings for debugging!
