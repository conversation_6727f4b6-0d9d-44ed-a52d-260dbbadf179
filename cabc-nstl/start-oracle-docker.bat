@echo off
echo Starting Oracle Database in Docker...
echo.

REM Start Oracle database container
docker-compose -f docker-compose-oracle.yml up -d

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Oracle database container started successfully!
    echo.
    echo 📋 Connection Details:
    echo    Host: localhost
    echo    Port: 1521
    echo    SID: XE
    echo    Username: RECUSER
    echo    Password: ociPro$reC_450
    echo.
    echo ⏳ Please wait 2-3 minutes for database initialization...
    echo.
    echo 📝 To check logs: docker-compose -f docker-compose-oracle.yml logs -f oracle-db
    echo 🔍 To check status: docker ps
    echo.
    echo 🚀 Once ready, run your application with:
    echo    gradlew bootRun --args="--spring.profiles.active=test"
    echo.
) else (
    echo.
    echo ❌ Failed to start Oracle database container!
    echo Please check Docker is running and try again.
    echo.
)

pause
